import { Clock, Mail, Phone, Shield, UserCheck, Users } from 'lucide-react';
import { useTranslations } from 'next-intl';
import {
  useContractorTeamMembers,
  type TeamMemberData,
} from '../../hooks/use-contractor-team-members';
import { Badge } from './badge';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Skeleton } from './skeleton';

interface TeamMembersProps {
  contractorId: string;
  className?: string;
}

/**
 * Component to display all team members for a contractor company
 */
export function TeamMembers({ contractorId, className }: TeamMembersProps) {
  const t = useTranslations('profilePage.contractorDetails.teamMembers');
  const {
    data: teamMembers = [],
    isLoading,
    error,
  } = useContractorTeamMembers(contractorId);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('notSpecified');
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getRoleDisplay = (role: string) => {
    switch (role) {
      case 'contractor':
        return t('roles.contractor');
      case 'admin':
        return t('roles.admin');
      case 'viewer':
        return t('roles.viewer');
      default:
        return role;
    }
  };

  const getStatusBadge = (onboardingCompleted: boolean) => {
    if (onboardingCompleted) {
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 border-green-200"
        >
          <UserCheck className="h-3 w-3 mr-1" />
          {t('status.active')}
        </Badge>
      );
    } else {
      return (
        <Badge
          variant="secondary"
          className="bg-amber-100 text-amber-800 border-amber-200"
        >
          <Clock className="h-3 w-3 mr-1" />
          {t('status.pending')}
        </Badge>
      );
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-indigo-600" />
            <span>{t('title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="p-4 border rounded-lg">
              <div className="flex flex-col space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-36" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-indigo-600" />
            <span>{t('title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-600">{t('error')}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5 text-indigo-600" />
          <span>{t('title')}</span>
          <Badge variant="secondary" className="ml-auto">
            {teamMembers.length}{' '}
            {teamMembers.length === 1 ? t('member') : t('members')}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {teamMembers.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">{t('noMembers')}</p>
          </div>
        ) : (
          <div className="space-y-3">
            {teamMembers.map((member: TeamMemberData) => (
              <div
                key={member.id}
                className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-3">
                      <div>
                        <h4 className="font-medium text-sm">
                          {member.name || t('unnamedUser')}
                        </h4>
                        <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                          <Mail className="h-3 w-3" />
                          <span>{member.email}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-wrap items-center gap-2 text-xs">
                      {member.phone_number && (
                        <div className="flex items-center space-x-1 text-muted-foreground">
                          <Phone className="h-3 w-3" />
                          <span>{member.phone_number}</span>
                        </div>
                      )}

                      <div className="flex items-center space-x-1">
                        <Shield className="h-3 w-3 text-muted-foreground" />
                        <Badge variant="outline" className="text-xs">
                          {getRoleDisplay(member.user_role)}
                        </Badge>
                      </div>

                      {getStatusBadge(member.onboarding_completed)}
                    </div>
                  </div>

                  <div className="text-xs text-muted-foreground text-right">
                    <p>
                      {t('joined')}: {formatDate(member.created_at)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
