import {
  activateSubscription,
  cancelSubscription,
  checkSubscriptionAccess,
  createPmaSubscription,
  deletePmaSubscription,
  getPmaSubscription,
  getPmaSubscriptionAccess,
  getPmaSubscriptions,
  performPmaSubscriptionAccessAction,
  reactivateSubscription,
  reactivateSubscriptionAccess,
  restoreSubscriptionAccess,
  revokeSubscriptionAccess,
  setSubscriptionAccessGracePeriod,
  setSubscriptionGracePeriod,
  suspendSubscription,
  suspendSubscriptionAccess,
  updatePmaSubscription,
  type CreatePmaSubscriptionRequest,
  type PmaSubscriptionFilters,
  type UpdatePmaSubscriptionRequest,
} from '@/features/billing/services/client';
import type { PmaSubscriptionWithDetails } from '@/features/billing/types/pma-subscriptions';
import { useUser } from '@/hooks/use-auth';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

interface PmaSubscriptionsResponse {
  subscriptions: PmaSubscriptionWithDetails[];
  total: number;
}

/**
 * Hook for managing PMA subscriptions
 */
export function usePmaSubscriptions(filters: PmaSubscriptionFilters = {}) {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  const queryKey = ['pma-subscriptions', filters, user?.id];

  // Query for PMA subscriptions
  const {
    data: subscriptionsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey,
    queryFn: (): Promise<PmaSubscriptionsResponse> => {
      return getPmaSubscriptions(filters);
    },
    enabled: !!user?.id && (!filters.contractorId || !!filters.contractorId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation for creating PMA subscription
  const createSubscriptionMutation = useMutation({
    mutationFn: (data: CreatePmaSubscriptionRequest) => {
      return createPmaSubscription(data);
    },
    onSuccess: () => {
      // Invalidate and refetch subscriptions
      queryClient.invalidateQueries({ queryKey: ['pma-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['pma-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-pmas'] });
    },
  });

  return {
    // Data
    subscriptions: subscriptionsData?.subscriptions || [],
    total: subscriptionsData?.total || 0,

    // Loading states
    isLoading,
    isCreating: createSubscriptionMutation.isPending,

    // Error states
    error,
    createError: createSubscriptionMutation.error,

    // Actions
    refetch,
    createSubscription: createSubscriptionMutation.mutate,
    createSubscriptionAsync: createSubscriptionMutation.mutateAsync,
  };
}

/**
 * Hook for managing a single PMA subscription
 */
export function usePmaSubscription(subscriptionId?: string) {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  // Query for single subscription
  const {
    data: subscription,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['pma-subscription', subscriptionId, user?.id],
    queryFn: (): Promise<{ subscription: PmaSubscriptionWithDetails }> => {
      if (!subscriptionId) throw new Error('Subscription ID is required');
      return getPmaSubscription(subscriptionId);
    },
    enabled: !!subscriptionId && !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation for updating subscription
  const updateSubscriptionMutation = useMutation({
    mutationFn: (data: UpdatePmaSubscriptionRequest) => {
      if (!subscriptionId) throw new Error('Subscription ID is required');
      return updatePmaSubscription(subscriptionId, data);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['pma-subscription', subscriptionId],
      });
      queryClient.invalidateQueries({ queryKey: ['pma-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['pma-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-pmas'] });
    },
  });

  // Mutation for deleting/cancelling subscription
  const deleteSubscriptionMutation = useMutation({
    mutationFn: () => {
      if (!subscriptionId) throw new Error('Subscription ID is required');
      return deletePmaSubscription(subscriptionId);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['pma-subscription', subscriptionId],
      });
      queryClient.invalidateQueries({ queryKey: ['pma-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['pma-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-pmas'] });
    },
  });

  return {
    // Data
    subscription: subscription?.subscription,

    // Loading states
    isLoading,
    isUpdating: updateSubscriptionMutation.isPending,
    isDeleting: deleteSubscriptionMutation.isPending,

    // Error states
    error,
    updateError: updateSubscriptionMutation.error,
    deleteError: deleteSubscriptionMutation.error,

    // Actions
    refetch,
    updateSubscription: updateSubscriptionMutation.mutate,
    updateSubscriptionAsync: updateSubscriptionMutation.mutateAsync,
    deleteSubscription: deleteSubscriptionMutation.mutate,
    deleteSubscriptionAsync: deleteSubscriptionMutation.mutateAsync,

    // Convenience methods
    activateSubscription: (data?: { reason?: string }) =>
      activateSubscription(subscriptionId!, data).then(() =>
        queryClient.invalidateQueries({
          queryKey: ['pma-subscription', subscriptionId],
        }),
      ),
    suspendSubscription: (reason?: string) =>
      suspendSubscription(subscriptionId!, reason).then(() =>
        queryClient.invalidateQueries({
          queryKey: ['pma-subscription', subscriptionId],
        }),
      ),
    cancelSubscription: () =>
      cancelSubscription(subscriptionId!).then(() =>
        queryClient.invalidateQueries({
          queryKey: ['pma-subscription', subscriptionId],
        }),
      ),
    reactivateSubscription: () =>
      reactivateSubscription(subscriptionId!).then(() =>
        queryClient.invalidateQueries({
          queryKey: ['pma-subscription', subscriptionId],
        }),
      ),
    setGracePeriod: (gracePeriodDays: number = 7) =>
      setSubscriptionGracePeriod(subscriptionId!, gracePeriodDays).then(() =>
        queryClient.invalidateQueries({
          queryKey: ['pma-subscription', subscriptionId],
        }),
      ),
  };
}

/**
 * Hook for managing PMA subscription access control
 */
export function usePmaSubscriptionAccess(subscriptionId?: string) {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  // Query for subscription access status
  const {
    data: accessData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['pma-subscription-access', subscriptionId, user?.id],
    queryFn: () => {
      if (!subscriptionId) throw new Error('Subscription ID is required');
      return getPmaSubscriptionAccess(subscriptionId);
    },
    enabled: !!subscriptionId && !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for access checks)
  });

  // Mutation for access control actions
  const accessControlMutation = useMutation({
    mutationFn: (data: {
      action:
        | 'check'
        | 'restore'
        | 'revoke'
        | 'suspend'
        | 'cancel'
        | 'reactivate'
        | 'grace_period';
      reason?: string;
      gracePeriodDays?: number;
    }) => {
      if (!subscriptionId) throw new Error('Subscription ID is required');
      return performPmaSubscriptionAccessAction(subscriptionId, data);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['pma-subscription-access', subscriptionId],
      });
      queryClient.invalidateQueries({
        queryKey: ['pma-subscription', subscriptionId],
      });
      queryClient.invalidateQueries({ queryKey: ['pma-access'] });
      queryClient.invalidateQueries({ queryKey: ['user-pmas'] });
    },
  });

  return {
    // Data
    accessData,
    access: accessData?.access,
    subscription: accessData?.subscription,
    pmaId: accessData?.pmaId,
    projectId: accessData?.projectId,

    // Loading states
    isLoading,
    isPerformingAction: accessControlMutation.isPending,

    // Error states
    error,
    actionError: accessControlMutation.error,

    // Actions
    refetch,
    performAccessAction: accessControlMutation.mutate,
    performAccessActionAsync: accessControlMutation.mutateAsync,

    // Convenience methods
    checkAccess: () => checkSubscriptionAccess(subscriptionId!),
    restoreAccess: () => restoreSubscriptionAccess(subscriptionId!),
    revokeAccess: (reason?: string) =>
      revokeSubscriptionAccess(subscriptionId!, reason),
    suspendAccess: (reason?: string) =>
      suspendSubscriptionAccess(subscriptionId!, reason),
    reactivateAccess: () => reactivateSubscriptionAccess(subscriptionId!),
    setAccessGracePeriod: (gracePeriodDays: number = 7) =>
      setSubscriptionAccessGracePeriod(subscriptionId!, gracePeriodDays),
  };
}
