'use client';

import {
  PendingInvitations,
  ProjectList,
  ProjectStatsCards,
  useProjects,
  useProjectStats,
} from '@/features/projects';
import { CreateProjectButton } from '@/features/projects/components/create-project-button';
import { FolderOpen } from 'lucide-react';
import { useTranslations } from 'next-intl';

const ProjectsPage = () => {
  const t = useTranslations('pages.projects');

  const { data: projects = [], isLoading: projectsLoading } = useProjects();
  const { data: stats, isLoading: statsLoading } = useProjectStats();

  return (
    <div className="w-full max-w-full mx-auto">
      <div className="space-y-6 md:space-y-8">
        {/* Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-3 sm:gap-4">
            <div className="flex-shrink-0 p-2 sm:p-3 rounded-xl bg-primary/10">
              <FolderOpen className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900">
                {t('title')}
              </h1>
              <p className="text-sm sm:text-base text-gray-500 mt-1">
                {t('description')}
              </p>
            </div>
          </div>
          <div className="flex-shrink-0 w-full sm:w-auto">
            <CreateProjectButton />
          </div>
        </div>

        {/* Stats Cards */}
        {stats && <ProjectStatsCards stats={stats} isLoading={statsLoading} />}

        {/* Pending Invitations */}
        <PendingInvitations />

        {/* Projects List */}
        <div className="space-y-4 sm:space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              Recent Projects
            </h2>
          </div>

          <ProjectList projects={projects} isLoading={projectsLoading} />
        </div>
      </div>
    </div>
  );
};

export default ProjectsPage;
