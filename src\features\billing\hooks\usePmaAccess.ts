import { useUser } from '@/features/auth/hooks/use-auth';
import {
  checkPmaAccess,
  checkProjectPmaAccess,
  getUserAccessiblePmas,
  validateBulkPmaAccess,
  validateMiddlewarePmaAccess,
  validatePmaAccess,
} from '@/features/billing/services/client';
import type {
  PmaAccessResult,
  ProjectPmaAccess,
} from '@/features/billing/services/pma-access-control.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

interface PmaAccessResponse {
  userId: string;
  userRole: string;
  pmaId: string;
  access: PmaAccessResult;
}

interface ProjectPmaAccessResponse {
  userId: string;
  userRole: string;
  projectId: string;
  projectAccess: ProjectPmaAccess;
}

interface UserPmasResponse {
  userId: string;
  userRole: string;
  accessiblePmas: string[];
  totalAccessiblePmas: number;
}

interface PmaAccessValidationRequest {
  mode: 'single' | 'bulk' | 'middleware';
  pmaId?: string;
  pmaIds?: string[];
  path?: string;
  requireActiveSubscription?: boolean;
}

/**
 * Hook for managing PMA access checks and validation
 */
export function usePmaAccess(pmaId?: string) {
  const { data: user } = useUser();
  const queryClient = useQueryClient();

  // Query for single PMA access check
  const {
    data: pmaAccess,
    isLoading: isLoadingAccess,
    error: accessError,
    refetch: refetchAccess,
  } = useQuery({
    queryKey: ['pma-access', pmaId, user?.id],
    queryFn: async (): Promise<PmaAccessResponse> => {
      if (!pmaId) throw new Error('PMA ID is required');

      return checkPmaAccess(pmaId);
    },
    enabled: !!pmaId && !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 403/404 errors
      if (error instanceof Error && error.message.includes('403')) return false;
      if (error instanceof Error && error.message.includes('404')) return false;
      return failureCount < 2;
    },
  });

  // Query for user's accessible PMAs
  const {
    data: userPmas,
    isLoading: isLoadingUserPmas,
    error: userPmasError,
  } = useQuery({
    queryKey: ['user-pmas', user?.id],
    queryFn: async (): Promise<UserPmasResponse> => {
      return getUserAccessiblePmas();
    },
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Mutation for validating PMA access
  const validateAccessMutation = useMutation({
    mutationFn: async (request: PmaAccessValidationRequest) => {
      return validatePmaAccess(request);
    },
    onSuccess: (data) => {
      // Invalidate related queries
      if (data.mode === 'single' && data.pmaId) {
        queryClient.invalidateQueries({
          queryKey: ['pma-access', data.pmaId],
        });
      }
      if (data.mode === 'bulk') {
        // Invalidate all PMA access queries
        queryClient.invalidateQueries({
          queryKey: ['pma-access'],
        });
      }
    },
  });

  return {
    // Access data
    pmaAccess: pmaAccess?.access,
    userPmas: userPmas?.accessiblePmas || [],
    totalAccessiblePmas: userPmas?.totalAccessiblePmas || 0,

    // Loading states
    isLoadingAccess,
    isLoadingUserPmas,
    isValidating: validateAccessMutation.isPending,

    // Error states
    accessError,
    userPmasError,
    validationError: validateAccessMutation.error,

    // Actions
    refetchAccess,
    validateAccess: validateAccessMutation.mutate,
    validateAccessAsync: validateAccessMutation.mutateAsync,

    // Computed properties
    hasAccess: pmaAccess?.access?.hasAccess ?? false,
    accessReason: pmaAccess?.access?.reason,
    subscription: pmaAccess?.access?.subscription,
    gracePeriodDays: pmaAccess?.access?.gracePeriodDays,
    accessMessage: pmaAccess?.access?.message,
  };
}

/**
 * Hook for managing project-level PMA access (all PMAs in a project)
 */
export function useProjectPmaAccess(projectId?: string) {
  const { data: user } = useUser();

  const {
    data: projectAccess,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['project-pma-access', projectId, user?.id],
    queryFn: async (): Promise<ProjectPmaAccessResponse> => {
      if (!projectId) throw new Error('Project ID is required');

      return checkProjectPmaAccess(projectId);
    },
    enabled: !!projectId && !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    projectAccess: projectAccess?.projectAccess,
    isLoading,
    error,
    refetch,

    // Computed properties
    overallAccess: projectAccess?.projectAccess?.overallAccess ?? false,
    activeSubscriptions: projectAccess?.projectAccess?.activeSubscriptions ?? 0,
    totalPmas: projectAccess?.projectAccess?.totalPmas ?? 0,
    pmaAccessMap: projectAccess?.projectAccess?.pmaAccess || new Map(),
  };
}

/**
 * Hook for bulk PMA access validation
 */
export function useBulkPmaAccessValidation() {
  const validateBulkAccessMutation = useMutation({
    mutationFn: async (pmaIds: string[]) => {
      return validateBulkPmaAccess(pmaIds);
    },
  });

  return {
    validateBulkAccess: validateBulkAccessMutation.mutate,
    validateBulkAccessAsync: validateBulkAccessMutation.mutateAsync,
    isValidating: validateBulkAccessMutation.isPending,
    error: validateBulkAccessMutation.error,
    data: validateBulkAccessMutation.data,
  };
}

/**
 * Hook for middleware access validation
 */
export function useMiddlewarePmaAccess() {
  const validateMiddlewareAccessMutation = useMutation({
    mutationFn: async ({ pmaId, path }: { pmaId: string; path: string }) => {
      return validateMiddlewarePmaAccess(pmaId, path);
    },
  });

  return {
    validateMiddlewareAccess: validateMiddlewareAccessMutation.mutate,
    validateMiddlewareAccessAsync: validateMiddlewareAccessMutation.mutateAsync,
    isValidating: validateMiddlewareAccessMutation.isPending,
    error: validateMiddlewareAccessMutation.error,
    data: validateMiddlewareAccessMutation.data,
  };
}
