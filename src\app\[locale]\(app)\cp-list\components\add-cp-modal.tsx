'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  FileUploadModal,
  type FileUploadResult,
} from '@/components/ui/file-upload-modal';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useUserWithProfile } from '@/hooks/use-auth';
import {
  useCreateCompetentPerson,
  useUpdateCompetentPerson,
} from '@/hooks/use-competent-person';
import { toast } from '@/hooks/use-toast';
import type {
  CompetentPerson,
  CompetentPersonInsert,
} from '@/types/competent-person';
import { zodResolver } from '@hookform/resolvers/zod';
import { ExternalLink, FileText, Loader2, Upload, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// Schema for adding a new competent person
const addCPSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  ic_no: z
    .string()
    .min(1, 'IC Number is required')
    .regex(/^\d{6}-\d{2}-\d{4}$/, 'IC Number must be in format 123456-78-9012'),
  phone_no: z
    .string()
    .refine((val) => !val || /^0/.test(val), 'Phone number must start with 0')
    .refine(
      (val) => !val || /^(01[0-9]|0[2-9])/.test(val),
      'Phone number must be in format 01X- for mobile or 0X- for landline',
    )
    .refine(
      (val) => !val || /^(01[0-9]-[0-9]{7,8}|0[2-9]-[0-9]{7,8})$/.test(val),
      'Invalid format. Use 01X-XXXXXXX for mobile or 0X-XXXXXXXX for landline',
    )
    .refine(
      (val) => !val || !/[a-zA-Z]/.test(val),
      'Phone number cannot contain letters',
    )
    .refine(
      (val) => !val || val.replace(/\D/g, '').length <= 11,
      'Phone number cannot exceed 11 digits',
    )
    .refine(
      (val) => !val || !/(\d)\1{5,}/.test(val.replace(/\D/g, '')),
      'Phone number contains suspicious repeated digits',
    )
    .optional(),
  address: z.string().optional(),
  cp_type: z.enum(['CP1', 'CP2', 'CP3'], {
    required_error: 'CP Type is required',
  }),
  cp_registeration_no: z.string().optional(),
  cp_registeration_cert: z.string().optional(),
  cert_exp_date: z.string().optional(),
  no_of_pma: z.number().int().min(0, 'PMAs must be 0 or greater').default(0),
});

type AddCPFormValues = z.infer<typeof addCPSchema>;

interface AddCPModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingPerson?: CompetentPerson | null;
}

export function AddCPModal({
  open,
  onOpenChange,
  editingPerson,
}: AddCPModalProps) {
  const t = useTranslations('cpList.addCPModal');
  const tFields = useTranslations('cpList.fields');
  const { data: user, isLoading: isUserLoading } = useUserWithProfile();
  const profile = user?.profile;
  const createCompetentPersonMutation = useCreateCompetentPerson();
  const updateCompetentPersonMutation = useUpdateCompetentPerson();
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  const isEditMode = !!editingPerson;

  const form = useForm<AddCPFormValues>({
    resolver: zodResolver(addCPSchema),
    defaultValues: {
      name: editingPerson?.name || '',
      ic_no: editingPerson?.ic_no || '',
      phone_no: editingPerson?.phone_no || '',
      address: editingPerson?.address || '',
      cp_type: editingPerson?.cp_type || undefined,
      cp_registeration_no: editingPerson?.cp_registeration_no || '',
      cp_registeration_cert: editingPerson?.cp_registeration_cert || '',
      cert_exp_date: editingPerson?.cert_exp_date || '',
      no_of_pma: editingPerson?.no_of_pma || 0,
    },
  });

  // Helper function to handle certificate upload
  const handleCertificateUpload = async (results: FileUploadResult[]) => {
    if (results.length > 0) {
      const uploadedUrl = results[0].url;
      form.setValue('cp_registeration_cert', uploadedUrl);
    }
    setIsUploadModalOpen(false);
  };

  // Helper function to format phone number as user types
  const formatPhoneNumber = (value: string): string => {
    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '');

    // Don't format if less than 3 digits
    if (digits.length < 3) return digits;

    // Format as 01X-XXXXXXX or 0X-XXXXXXXX
    if (digits.startsWith('01')) {
      // Mobile number format: 01X-XXXXXXX
      return `${digits.slice(0, 3)}-${digits.slice(3, 11)}`;
    } else if (digits.startsWith('0')) {
      // Landline format: 0X-XXXXXXXX
      return `${digits.slice(0, 2)}-${digits.slice(2, 10)}`;
    }

    // If doesn't match expected patterns, just return first 11 digits with hyphen after 3rd digit
    return digits.length > 3
      ? `${digits.slice(0, 3)}-${digits.slice(3, 11)}`
      : digits;
  };

  // Helper function to capitalize first letter of each word in a name
  const capitalizeWords = (name: string): string => {
    if (!name) return '';
    return name
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Helper function to extract filename from URL
  const getFilenameFromUrl = (url: string): string => {
    if (!url) return '';
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || '';
      return decodeURIComponent(filename);
    } catch {
      // If URL parsing fails, try to extract from the path
      const parts = url.split('/');
      return parts[parts.length - 1] || 'certificate.pdf';
    }
  };

  // Helper function to remove the certificate
  const handleRemoveCertificate = () => {
    form.setValue('cp_registeration_cert', '');
  };

  // Reset form when editingPerson changes
  useEffect(() => {
    if (editingPerson) {
      form.reset({
        name: editingPerson.name,
        ic_no: editingPerson.ic_no,
        phone_no: editingPerson.phone_no || '',
        address: editingPerson.address || '',
        cp_type: editingPerson.cp_type,
        cp_registeration_no: editingPerson.cp_registeration_no || '',
        cp_registeration_cert: editingPerson.cp_registeration_cert || '',
        cert_exp_date: editingPerson.cert_exp_date || '',
        no_of_pma: editingPerson.no_of_pma,
      });
    } else {
      form.reset({
        name: '',
        ic_no: '',
        phone_no: '',
        address: '',
        cp_type: undefined,
        cp_registeration_no: '',
        cp_registeration_cert: '',
        cert_exp_date: '',
        no_of_pma: 0,
      });
    }
  }, [editingPerson, form]);

  const onSubmit = async (values: AddCPFormValues) => {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'You must be logged in to perform this action',
        variant: 'destructive',
      });
      return;
    }

    if (!profile?.contractor_id && !isEditMode) {
      toast({
        title: 'Error',
        description:
          'Unable to determine contractor ID. Please ensure your profile is properly set up.',
        variant: 'destructive',
      });
      return;
    }

    try {
      if (isEditMode && editingPerson) {
        // Update existing competent person
        const updateData = {
          name: capitalizeWords(values.name),
          ic_no: values.ic_no,
          phone_no: values.phone_no || undefined,
          address: values.address || undefined,
          cp_type: values.cp_type,
          cp_registeration_no: values.cp_registeration_no || undefined,
          cp_registeration_cert: values.cp_registeration_cert || undefined,
          cert_exp_date: values.cert_exp_date || undefined,
          no_of_pma: values.no_of_pma,
          updated_by: user.id,
        };
        await updateCompetentPersonMutation.mutateAsync({
          competentPersonId: editingPerson.id,
          updates: updateData,
        });
        toast({
          title: t('updateSuccess.title'),
          description: t('updateSuccess.description'),
        });
      } else {
        // Create new competent person
        const competentPersonData: CompetentPersonInsert = {
          contractor_id: profile!.contractor_id!,
          name: capitalizeWords(values.name),
          ic_no: values.ic_no,
          phone_no: values.phone_no || undefined,
          address: values.address || undefined,
          cp_type: values.cp_type,
          cp_registeration_no: values.cp_registeration_no || undefined,
          cp_registeration_cert: values.cp_registeration_cert || undefined,
          cert_exp_date: values.cert_exp_date || undefined,
          no_of_pma: values.no_of_pma,
          created_by: user.id,
        };
        await createCompetentPersonMutation.mutateAsync(competentPersonData);
        toast({
          title: t('success.title'),
          description: t('success.description'),
        });
      }

      form.reset();
      onOpenChange(false);
    } catch {
      toast({
        title: isEditMode ? t('updateError.title') : t('error.title'),
        description: isEditMode
          ? t('updateError.description')
          : t('error.description'),
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditMode ? t('editTitle') : t('title')}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {isUserLoading && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                <span className="text-sm text-muted-foreground">
                  Loading user data...
                </span>
              </div>
            )}

            {/* Name Field */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {tFields('name')}{' '}
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter full name"
                      {...field}
                      onChange={(e) => {
                        const capitalized = capitalizeWords(e.target.value);
                        field.onChange(capitalized);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* IC Number Field */}
            <FormField
              control={form.control}
              name="ic_no"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {tFields('icNo')}{' '}
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="123456-78-9012" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone Number Field */}
            <FormField
              control={form.control}
              name="phone_no"
              render={({ field }) => {
                const phoneError = form.formState.errors.phone_no;
                return (
                  <FormItem>
                    <FormLabel>{tFields('phone')}</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="01X-XXXXXXX"
                        {...field}
                        onChange={(e) => {
                          const formatted = formatPhoneNumber(e.target.value);
                          field.onChange(formatted);
                          // Trigger validation on change
                          if (formatted) form.trigger('phone_no');
                        }}
                        maxLength={12} // 01X-XXXXXXX (11 digits + 1 hyphen)
                        className={phoneError ? 'border-destructive' : ''}
                      />
                    </FormControl>
                    <FormMessage />
                    {!phoneError && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Mobile: 01X-XXXXXXX or Landline: 0X-XXXXXXXX
                      </p>
                    )}
                  </FormItem>
                );
              }}
            />

            {/* Address Field */}
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tFields('address')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter address"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* CP Type Field */}
            <FormField
              control={form.control}
              name="cp_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {tFields('cpType')}{' '}
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select CP type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="CP1">CP1</SelectItem>
                      <SelectItem value="CP2">CP2</SelectItem>
                      <SelectItem value="CP3">CP3</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Registration Number Field */}
            <FormField
              control={form.control}
              name="cp_registeration_no"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tFields('registrationNo')}</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter registration number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Registration Certificate Upload Field */}
            <FormField
              control={form.control}
              name="cp_registeration_cert"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tFields('registrationCert')}</FormLabel>
                  <FormControl>
                    <div className="space-y-3">
                      {field.value ? (
                        // Certificate Preview Card
                        <div
                          className="relative group border rounded-lg p-4 bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer"
                          onClick={() => window.open(field.value, '_blank')}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <FileText className="w-8 h-8 text-red-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-foreground truncate">
                                {getFilenameFromUrl(field.value)}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Click to preview certificate
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <ExternalLink className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveCertificate();
                                }}
                                className="h-8 w-8 p-0 hover:bg-destructive/10"
                              >
                                <X className="w-4 h-4 text-destructive" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        // Upload Button
                        <Button
                          type="button"
                          variant="outline"
                          className="w-full h-20 border-dashed"
                          onClick={() => setIsUploadModalOpen(true)}
                        >
                          <div className="flex flex-col items-center space-y-2">
                            <Upload className="w-6 h-6 text-muted-foreground" />
                            <span className="text-sm">Upload Certificate</span>
                          </div>
                        </Button>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Certificate Expiry Date Field */}
            <FormField
              control={form.control}
              name="cert_exp_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tFields('certExpiry')}</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Number of PMAs Field */}
            <FormField
              control={form.control}
              name="no_of_pma"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tFields('pmas')}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      placeholder="0"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={
                  isUserLoading ||
                  createCompetentPersonMutation.isPending ||
                  updateCompetentPersonMutation.isPending
                }
              >
                {t('cancelButton')}
              </Button>
              <Button
                type="submit"
                disabled={
                  isUserLoading ||
                  createCompetentPersonMutation.isPending ||
                  updateCompetentPersonMutation.isPending
                }
              >
                {(createCompetentPersonMutation.isPending ||
                  updateCompetentPersonMutation.isPending) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isEditMode ? t('updateButton') : t('submitButton')}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>

      {/* Certificate Upload Modal */}
      <FileUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUpload={handleCertificateUpload}
        title="Upload Registration Certificate"
        description="Upload competent person registration certificate. Accepted formats: PDF, JPG, PNG (max 10MB)"
        maxFiles={1}
        acceptedTypes=".pdf,.jpg,.jpeg,.png"
        maxSize={10 * 1024 * 1024}
        folderPath="competent-persons/certificates"
      />
    </Dialog>
  );
}
