'use client';

import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

export interface CreatableComboboxOption {
  value: string;
  label: string;
  searchValue?: string;
  content?: React.ReactNode;
}

interface CreatableComboboxProps {
  options: CreatableComboboxOption[];
  value?: string;
  onValueChange?: (value: string) => void;
  onCreateNew?: (inputValue: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  createMessage?: string;
  className?: string;
  disabled?: boolean;
  allowCreate?: boolean;
}

export function CreatableCombobox({
  options,
  value,
  onValueChange,
  onCreateNew,
  placeholder = 'Select option...',
  searchPlaceholder = 'Search...',
  emptyMessage = 'No option found.',
  createMessage = 'Create',
  className,
  disabled = false,
  allowCreate = true,
}: CreatableComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');

  const selectedOption = options.find((option) => option.value === value);

  // Check if the input value matches any existing option
  const exactMatch = options.find(
    (option) => option.label.toLowerCase() === inputValue.toLowerCase(),
  );

  const handleCreateNew = () => {
    if (inputValue.trim() && onCreateNew && !exactMatch) {
      onCreateNew(inputValue.trim());
      setInputValue('');
      setOpen(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('w-full justify-between', className)}
          disabled={disabled}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
        <Command>
          <CommandInput
            placeholder={searchPlaceholder}
            value={inputValue}
            onValueChange={setInputValue}
          />
          <CommandList>
            <CommandEmpty>
              <div className="p-2">
                <p className="text-sm text-muted-foreground mb-2">
                  {emptyMessage}
                </p>
                {allowCreate && inputValue.trim() && !exactMatch && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCreateNew}
                    className="w-full"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    {createMessage} &quot;{inputValue.trim()}&quot;
                  </Button>
                )}
              </div>
            </CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.searchValue || option.label}
                  onSelect={() => {
                    onValueChange?.(option.value === value ? '' : option.value);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      value === option.value ? 'opacity-100' : 'opacity-0',
                    )}
                  />
                  {option.content || option.label}
                </CommandItem>
              ))}
              {allowCreate && inputValue.trim() && !exactMatch && (
                <CommandItem
                  value={inputValue}
                  onSelect={handleCreateNew}
                  className="border-t"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {createMessage} &quot;{inputValue.trim()}&quot;
                </CommandItem>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
