'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SectionHeader } from '@/components/ui/section-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { generateCompanyCode } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Check, Copy, RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  companyFormSchema,
  type CompanyFormValues,
} from '../schemas/contractor-onboarding-schemas';

interface CompanyFormProps {
  onSubmit?: (values: CompanyFormValues) => void;
  onCancel?: () => void;
}

export function CompanyForm({ onSubmit, onCancel }: CompanyFormProps) {
  const [isCodeCopied, setIsCodeCopied] = useState<boolean>(false);
  const t = useTranslations('companyForm');

  // Initialize form with react-hook-form and Zod validation
  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      company_name: '',
      company_type: undefined,
      company_hotline: '',
      oem_name: '',
      code: generateCompanyCode(), // Auto-generate code on initialization
    },
  });

  const watchedCompanyType = form.watch('company_type');

  // Generate new company code
  const regenerateCode = () => {
    const newCode = generateCompanyCode();
    form.setValue('code', newCode);
  };

  // Copy code to clipboard
  const handleCopyCode = async () => {
    try {
      const code = form.getValues('code');
      await navigator.clipboard.writeText(code);
      setIsCodeCopied(true);
      setTimeout(() => setIsCodeCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // Handle form submission
  const handleSubmit = (values: CompanyFormValues) => {
    if (onSubmit) {
      onSubmit(values);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Form {...form}>
      <form
        className="bg-card rounded-3xl shadow-lg p-8 lg:p-12 border"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            {t('title')}
          </h1>
          <p className="text-muted-foreground">{t('description')}</p>
        </div>

        {/* Form Sections */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 lg:gap-12">
          {/* Company Information Section */}
          <div className="xl:col-span-1">
            <SectionHeader
              number={1}
              title={t('sections.companyInformation')}
            />

            <div className="space-y-7">
              {/* Auto-generated Company Code */}
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('fields.companyCode.label')}</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input
                          {...field}
                          readOnly
                          className="font-mono text-center tracking-wider bg-muted text-sm"
                          placeholder={t('fields.companyCode.placeholder')}
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleCopyCode}
                        className="shrink-0"
                      >
                        {isCodeCopied ? (
                          <Check className="w-4 h-4" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={regenerateCode}
                        className="shrink-0"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </Button>
                    </div>
                    <FormDescription>
                      {t('fields.companyCode.description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t('fields.companyName.label')}{' '}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('fields.companyName.placeholder')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t('fields.companyType.label')}{' '}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t('fields.companyType.placeholder')}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="COMPETENT_FIRM">
                          {t('fields.companyType.options.competentFirm')}
                        </SelectItem>
                        <SelectItem value="NON_COMPETENT_FIRM">
                          {t('fields.companyType.options.nonCompetentFirm')}
                        </SelectItem>
                        <SelectItem value="OEM">
                          {t('fields.companyType.options.oem')}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_hotline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t('fields.companyHotline.label')}{' '}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder={t('fields.companyHotline.placeholder')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* OEM Details Section - Only show if OEM is selected */}
          {watchedCompanyType === 'OEM' && (
            <div className="xl:col-span-1">
              <SectionHeader number={2} title={t('sections.oemDetails')} />

              <div className="space-y-7">
                <FormField
                  control={form.control}
                  name="oem_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t('fields.oemName.label')}{' '}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('fields.oemName.placeholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('fields.oemName.description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}

          {/* Appointed OEM/Competent Firm Section - Only show if NON_COMPETENT_FIRM is selected */}
          {watchedCompanyType === 'NON_COMPETENT_FIRM' && (
            <div className="xl:col-span-1">
              <SectionHeader
                number={2}
                title={t('sections.appointedFirmDetails')}
              />

              <div className="space-y-7">
                <FormField
                  control={form.control}
                  name="appointed_oem_competent_firm"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t('fields.appointedFirm.label')}{' '}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('fields.appointedFirm.placeholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('fields.appointedFirm.description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex flex-col sm:flex-row items-center justify-between pt-8 mt-8 border-t border-border gap-4">
          <p className="text-sm text-muted-foreground">
            {t('footer.requiredNote')}
          </p>
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              className="px-8 py-3"
            >
              {t('actions.cancel')}
            </Button>
            <Button
              type="submit"
              className="px-8 py-3"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting
                ? t('actions.registering')
                : t('actions.register')}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
