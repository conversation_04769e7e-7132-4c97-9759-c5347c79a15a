import { CreatePMACertificateInput } from '@/features/pma-management/types/create-pma';
import type {
  PMACertificate,
  PmaDbStatus,
} from '@/features/pma-management/types/pma-certificate';
import { supabase } from '@/lib/supabase';

export async function fetchPMACertificates(
  projectId: string,
  page: number,
  pageSize: number,
  sorting?: { column: string; direction: 'asc' | 'desc' },
  searchQuery?: string,
  filters?: { status?: PmaDbStatus | 'all' },
): Promise<{ data: PMACertificate[]; total: number }> {
  if (!projectId) {
    throw new Error('Project ID is required');
  }

  console.log('fetchPMACertificates called with:', {
    projectId,
    page,
    pageSize,
    sorting,
    searchQuery,
    filters,
  });

  const start = (page - 1) * pageSize;
  const end = start + pageSize - 1;

  let query = supabase
    .from('pma_certificates')
    .select(
      `
            id,
            pma_number,
            expiry_date,
            inspection_date,
            status,
            project_id,
            competent_person_id,
            location,
            state,
            file_url,
            created_at,
            updated_at,
            total_repair_cost,
            total_repair_time,
            competent_person:competent_person_id(name)
        `,
      { count: 'exact' },
    )
    .eq('project_id', projectId)
    .is('deleted_at', null);

  if (searchQuery && searchQuery.trim()) {
    console.log('Search query:', searchQuery);
    // Search across PMA number and location with case-insensitive matching
    query = query.or(
      `pma_number.ilike.%${searchQuery.trim()}%,location.ilike.%${searchQuery.trim()}%`,
    );
  }

  if (filters?.status && filters.status !== 'all') {
    console.log('Status filter:', filters.status);
    query = query.eq('status', filters.status as PmaDbStatus);
  }

  if (sorting) {
    query = query.order(sorting.column, {
      ascending: sorting.direction === 'asc',
    });
  } else {
    query = query.order('created_at', { ascending: false });
  }

  const { data, error, count } = await query.range(start, end);

  console.log('Query result:', {
    data: data?.length || 0,
    count,
    error,
  });

  if (error) {
    console.error('Error fetching PMA certificates:', error);
    throw new Error('Failed to fetch PMA certificates.');
  }

  const certificates: PMACertificate[] = data.map(
    (cert): PMACertificate => ({
      ...(cert as unknown as PMACertificate),
      // Ensure file_url is properly handled
      file_url: (cert as { file_url?: string | null }).file_url || null,
      // Extract competent person name from joined data
      competent_person_name:
        (cert as { competent_person?: { name?: string } | null })
          .competent_person?.name || null,
    }),
  );

  console.log('Returning certificates:', certificates.length);
  return { data: certificates, total: count || 0 };
}

// --- PMA Stats ---
export interface PMAStats {
  active: number;
  expiringSoon: number;
  expired: number;
}

/**
 * Fetch PMA stats (active, expiring soon, expired) for a project
 */
export async function fetchPMACertificateStats(
  projectId: string,
): Promise<PMAStats> {
  if (!projectId) throw new Error('Project ID is required');

  const now = new Date();
  const nowISO = now.toISOString();
  const oneMonthFromNow = new Date(now);
  oneMonthFromNow.setMonth(now.getMonth() + 1);
  const oneMonthISO = oneMonthFromNow.toISOString();

  // Parallel count queries
  const [activeRes, expSoonRes, expiredRes] = await Promise.all([
    supabase
      .from('pma_certificates')
      .select('id', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .is('deleted_at', null)
      .gte('expiry_date', oneMonthISO),
    supabase
      .from('pma_certificates')
      .select('id', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .is('deleted_at', null)
      .lt('expiry_date', oneMonthISO)
      .gte('expiry_date', nowISO),
    supabase
      .from('pma_certificates')
      .select('id', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .is('deleted_at', null)
      .lt('expiry_date', nowISO),
  ]);

  // Error handling
  [activeRes, expSoonRes, expiredRes].forEach((res, idx) => {
    if (res.error) {
      throw new Error(
        `Failed to fetch PMA stats (query ${idx}): ${res.error.message}`,
      );
    }
  });

  return {
    active: activeRes.count || 0,
    expiringSoon: expSoonRes.count || 0,
    expired: expiredRes.count || 0,
  };
}

export async function getPMACertificateById(
  id: string,
): Promise<PMACertificate> {
  const { data, error } = await supabase
    .from('pma_certificates')
    .select(
      `
      *,
      competent_person:competent_person_id(name)
    `,
    )
    .eq('id', id)
    .is('deleted_at', null)
    .single();

  if (error) throw error;

  // Transform the data to include competent_person_name
  const transformedData = {
    ...data,
    competent_person_name: data.competent_person?.name || null,
  };

  return transformedData;
}

export async function updatePMACertificate(
  id: string,
  updates: Partial<CreatePMACertificateInput>,
): Promise<PMACertificate> {
  const { data, error } = await supabase
    .from('pma_certificates')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function deletePMACertificate(
  id: string,
  userId: string,
): Promise<void> {
  const { error } = await supabase
    .from('pma_certificates')
    .update({
      deleted_at: new Date().toISOString(),
      deleted_by: userId,
    })
    .eq('id', id);

  if (error) {
    throw error;
  }
}

/**
 * Restore a soft-deleted PMA certificate
 */
export async function restorePMACertificate(
  id: string,
  userId: string,
): Promise<PMACertificate> {
  const { data, error } = await supabase
    .from('pma_certificates')
    .update({
      deleted_at: null,
      deleted_by: null,
      updated_at: new Date().toISOString(),
      updated_by: userId,
    })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Permanently delete a PMA certificate (hard delete)
 * Use with caution - this action cannot be undone
 */
export async function permanentlyDeletePMACertificate(
  id: string,
): Promise<void> {
  const { error } = await supabase
    .from('pma_certificates')
    .delete()
    .eq('id', id);

  if (error) {
    throw error;
  }
}

/**
 * Get soft-deleted PMA certificates for a project (for admin purposes)
 */
export async function getSoftDeletedPMACertificates(
  projectId: string,
): Promise<PMACertificate[]> {
  if (!projectId) {
    throw new Error('Project ID is required');
  }

  const { data, error } = await supabase
    .from('pma_certificates')
    .select('*')
    .eq('project_id', projectId)
    .not('deleted_at', 'is', null)
    .order('deleted_at', { ascending: false });

  if (error) {
    console.error('Error fetching soft-deleted PMA certificates:', error);
    throw new Error('Failed to fetch soft-deleted PMA certificates.');
  }

  return data || [];
}

/**
 * Get PMA certificate IDs for a project (for subscription management)
 */
export async function getPMACertificateIds(
  projectId: string,
): Promise<{ id: string }[]> {
  if (!projectId) {
    throw new Error('Project ID is required');
  }

  const { data, error } = await supabase
    .from('pma_certificates')
    .select('id')
    .eq('project_id', projectId)
    .is('deleted_at', null);

  if (error) {
    throw new Error(`Failed to fetch PMA certificates: ${error.message}`);
  }

  return data || [];
}

/**
 * Create a PMA certificate (for project creation/billing context)
 */
export async function createPMACertificateForProject(certificateData: {
  pma_number: string;
  expiry_date: string;
  location: string;
  project_id: string;
  state?:
    | 'JH'
    | 'KD'
    | 'KT'
    | 'ML'
    | 'NS'
    | 'PH'
    | 'PN'
    | 'PK'
    | 'PL'
    | 'SB'
    | 'SW'
    | 'SL'
    | 'TR'
    | 'WP'
    | 'LBN'
    | 'PW'
    | 'OTH'
    | null;
  status?: 'valid' | 'validating' | 'invalid';
  competent_person_id?: string | null;
  user_id?: string | null;
}): Promise<PMACertificate> {
  const { data, error } = await supabase
    .from('pma_certificates')
    .insert({
      ...certificateData,
      status: certificateData.status || 'validating',
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create PMA certificate: ${error.message}`);
  }

  return data;
}
