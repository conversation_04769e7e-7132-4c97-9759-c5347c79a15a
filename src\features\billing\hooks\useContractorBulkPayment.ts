import { useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  ContractorBulkPaymentData,
  BulkPaymentRequest,
  ContractorPaymentSummary,
} from '../types/contractor-bulk-payment';
import type { ContractorBillingData } from '../types/project-grouped-billing';

/**
 * Hook for processing bulk payments for all contractor PMAs
 */
export function useContractorBulkPayment() {
  const queryClient = useQueryClient();

  const processBulkPayment = useMutation({
    mutationFn: async (
      request: BulkPaymentRequest,
    ): Promise<{
      payment_url?: string;
      billplz_bill_id?: string;
      charge_id?: string;
      payment_method: 'billplz_redirect' | 'card_charge';
      total_amount: number;
      processed_subscriptions: string[];
    }> => {
      const response = await fetch('/api/billing/bulk-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create payment');
      }

      return result.data;
    },
    onSuccess: () => {
      // Invalidate relevant queries to refresh data when user returns
      queryClient.invalidateQueries({ queryKey: ['pma-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['contractor-billing'] });
    },
  });

  return {
    processBulkPayment: processBulkPayment.mutateAsync,
    isProcessing: processBulkPayment.isPending,
    error: processBulkPayment.error,
    isSuccess: processBulkPayment.isSuccess,
    reset: processBulkPayment.reset,
  };
}

/**
 * Utility function to prepare bulk payment data from contractor billing data
 */
export function prepareContractorBulkPaymentData(
  contractorData: ContractorBillingData,
): ContractorBulkPaymentData {
  // Get all subscriptions that require payment (not active or in grace period/suspended)
  const outstandingSubscriptions = contractorData.projects.flatMap((project) =>
    project.pma_subscriptions.filter(
      (subscription) =>
        subscription.status === 'pending_payment' ||
        subscription.status === 'grace_period' ||
        subscription.status === 'suspended',
    ),
  );

  // Calculate total amount for outstanding payments (using pro-rated amounts)
  const totalAmount = outstandingSubscriptions.reduce(
    (sum, subscription) => sum + (subscription.calculated_amount || 0),
    0,
  );

  // Calculate payment breakdown by status
  const paymentBreakdown = {
    active_overdue: outstandingSubscriptions.filter(
      (s) => s.status === 'pending_payment',
    ).length,
    grace_period: outstandingSubscriptions.filter(
      (s) => s.status === 'grace_period',
    ).length,
    suspended: outstandingSubscriptions.filter((s) => s.status === 'suspended')
      .length,
  };

  return {
    contractor_id: contractorData.contractor_id,
    contractor_name: contractorData.contractor_name,
    total_amount: totalAmount,
    outstanding_subscriptions: outstandingSubscriptions,
    payment_breakdown: paymentBreakdown,
    total_pmas_count: outstandingSubscriptions.length,
  };
}

/**
 * Utility function to create payment summary for display
 */
export function createContractorPaymentSummary(
  contractorData: ContractorBillingData,
): ContractorPaymentSummary {
  const bulkData = prepareContractorBulkPaymentData(contractorData);

  return {
    contractor_id: contractorData.contractor_id,
    contractor_name: contractorData.contractor_name,
    total_outstanding_amount: bulkData.total_amount,
    pmas_requiring_payment: bulkData.total_pmas_count,
    urgent_pmas_count: contractorData.urgent_subscriptions_count,
    has_suspended_pmas: contractorData.total_suspended_pmas > 0,
    next_billing_date: contractorData.projects[0]?.next_billing_date,
  };
}
