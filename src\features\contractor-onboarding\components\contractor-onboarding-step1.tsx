import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SectionHeader } from '@/components/ui/section-header';
import { useContractorTranslations } from '@/hooks/use-translations';
import { ChevronRight } from 'lucide-react';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { type ContractorStep1FormValues } from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingStep1Props {
  form: UseFormReturn<ContractorStep1FormValues>;
  onSubmit: (values: ContractorStep1FormValues) => void;
}

export const ContractorOnboardingStep1 =
  React.memo<ContractorOnboardingStep1Props>(({ form, onSubmit }) => {
    const t = useContractorTranslations();

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
          <SectionHeader number={1} title={t('onboarding.step1.title')} />

          {/* Full Name */}
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.fullName')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('onboarding.step1.fullNamePlaceholder')}
                    {...field}
                    onChange={(e) =>
                      field.onChange(e.target.value.toUpperCase())
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* IC Number */}
          <FormField
            control={form.control}
            name="icNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.icNumber')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('onboarding.step1.icNumberPlaceholder')}
                    {...field}
                    onChange={(e) =>
                      field.onChange(e.target.value.toUpperCase())
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Phone Number */}
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.phoneNumber')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder={t('onboarding.step1.phoneNumberPlaceholder')}
                    {...field}
                    onChange={(e) =>
                      field.onChange(e.target.value.toUpperCase())
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end pt-6">
            <Button type="submit" className="px-8 py-3">
              {t('onboarding.step1.nextButton')}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </form>
      </Form>
    );
  });

ContractorOnboardingStep1.displayName = 'ContractorOnboardingStep1';
