import { hasPermission } from '@/lib/rbac';
import { supabase } from '@/lib/supabase-enhanced';
import type { UserRole } from '@/types/auth';
import type { Permission } from '@/types/rbac';

export interface AuthenticatedUser {
  id: string;
  user_role: UserRole;
  contractor_id?: string | null;
  email?: string;
}

export interface AuthResult {
  user: AuthenticatedUser | null;
  error: string | null;
}

/**
 * Authenticate user and return user details with role information
 */
export async function authenticateUser(): Promise<AuthResult> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        user: null,
        error: 'Unauthorized',
      };
    }

    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      return {
        user: null,
        error: 'Failed to fetch user profile',
      };
    }

    return {
      user: {
        id: user.id,
        user_role: profile.user_role as UserRole,
        contractor_id: profile.contractor_id,
        email: user.email,
      },
      error: null,
    };
  } catch {
    return {
      user: null,
      error: 'Authentication failed',
    };
  }
}

/**
 * Authenticate user and check permissions
 */
export async function authenticateWithPermission(
  permission: Permission,
): Promise<AuthResult> {
  const authResult = await authenticateUser();

  if (authResult.error || !authResult.user) {
    return authResult;
  }

  if (!hasPermission(authResult.user.user_role, permission)) {
    return {
      user: null,
      error: 'Insufficient permissions',
    };
  }

  return authResult;
}

/**
 * Check if a user can access another user's data
 * Contractors can only access their own data
 */
export function canAccessUserData(
  requestingUser: AuthenticatedUser,
  targetUserId: string,
): boolean {
  // Admin can access any user's data
  if (requestingUser.user_role === 'admin') {
    return true;
  }

  // Contractors and viewers can only access their own data
  if (
    requestingUser.user_role === 'contractor' ||
    requestingUser.user_role === 'viewer'
  ) {
    return requestingUser.id === targetUserId;
  }

  return false;
}
