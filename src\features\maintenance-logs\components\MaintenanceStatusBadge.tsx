import { cn } from '@/lib/utils';
import {
  MAINTENANCE_STATUS,
  MAINTENANCE_STATUS_COLORS,
} from '../schemas/create-maintenance-log';

interface MaintenanceStatusBadgeProps {
  status: (typeof MAINTENANCE_STATUS)[number];
  className?: string;
}

export function MaintenanceStatusBadge({
  status,
  className,
}: MaintenanceStatusBadgeProps) {
  return (
    <span
      className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',
        MAINTENANCE_STATUS_COLORS[status],
        className,
      )}
    >
      {status}
    </span>
  );
}
