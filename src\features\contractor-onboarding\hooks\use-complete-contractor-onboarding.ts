import { useForceProfileRefresh } from '@/hooks/use-force-profile-refresh';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { ContractorFullFormValues } from '../schemas/contractor-onboarding-schemas';

type Contractor = Database['public']['Tables']['contractors']['Row'];

export function useCompleteContractorOnboarding() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const forceProfileRefresh = useForceProfileRefresh();
  return useMutation({
    mutationKey: ['contractor-onboarding', 'complete'],
    mutationFn: async (values: ContractorFullFormValues) => {
      // Get current user
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // Note: File uploads for competent person certificates have been removed
      // as part of the simplified 3-step onboarding flow

      // Update profile with onboarding data
      const { error: profileError } = await supabase
        .from('users')
        .update({
          name:
            values.fullName ||
            user.user_metadata?.full_name ||
            user.email?.split('@')[0],
          phone_number: values.phoneNumber || user.user_metadata?.phone_number,
          onboarding_completed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileError) {
        throw new Error(`Profile update failed: ${profileError.message}`);
      }

      // Handle company operations
      if (values.companyRegistrationType === 'create') {
        // Convert company name to uppercase for consistency
        const uppercaseCompanyName = values.company_name!.toUpperCase().trim();

        // Create new contractor company with retry logic for code collisions
        let attempts = 0;
        const maxAttempts = 5;
        let companyData: Contractor | null = null;
        let companyError = null;

        while (attempts < maxAttempts) {
          const { data, error } = await supabase
            .from('contractors')
            .insert({
              name: uppercaseCompanyName,
              contractor_type: values.company_type!,
              hotline: values.company_hotline || '',
              oem_name: values.oem_name || null,
              appointed_oem_competent_firm:
                values.appointed_oem_competent_firm || null,
              code: values.code!,
              created_by: user.id,
              updated_by: user.id,
            })
            .select()
            .single();

          companyData = data;
          companyError = error;

          // If no error, break out of retry loop
          if (!companyError) {
            break;
          }

          // Handle specific constraint violations
          if (companyError.code === '23505') {
            if (companyError.message.includes('name')) {
              throw new Error(
                `Company name "${uppercaseCompanyName}" already exists. Please choose a different name.`,
              );
            } else if (companyError.message.includes('code')) {
              // Generate a new code and retry
              const { generateCompanyCode } = await import('@/lib/utils');
              values.code = generateCompanyCode();
              attempts++;
              continue;
            }
          }

          // For other errors, don't retry
          break;
        }

        if (companyError) {
          if (attempts >= maxAttempts) {
            throw new Error(
              'Unable to generate a unique company code after multiple attempts. Please try again.',
            );
          }
          throw new Error(`Company creation failed: ${companyError.message}`);
        }

        // Check if company was created successfully
        if (!companyData) {
          throw new Error('Company creation failed: No company data returned');
        }

        // Update user with contractor information to associate user with the company
        const { error: contractorError } = await supabase
          .from('users')
          .update({
            contractor_id: companyData.id,
            onboarding_completed: true,
            updated_by: user.id,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);

        if (contractorError) {
          throw new Error(
            `Failed to associate user with company: ${contractorError.message}`,
          );
        }

        // Insert competent persons if any are provided
        const competentPersons = values.competent_persons || [];
        const validCompetentPersons = competentPersons.filter(
          (cp) => cp.name && cp.ic_no && cp.cp_type,
        );

        if (validCompetentPersons.length > 0 && companyData) {
          const competentPersonsData = validCompetentPersons.map((cp) => ({
            contractor_id: companyData!.id,
            name: cp.name!,
            ic_no: cp.ic_no!,
            phone_no: cp.phone_no || null,
            address: cp.address || null,
            cp_type: cp.cp_type!,
            cp_registeration_no: cp.cp_registeration_no || null,
            cp_registeration_cert: cp.cp_registeration_cert || null,
            cert_exp_date: cp.cert_exp_date || null,
            no_of_pma: cp.no_of_pma || 0,
            created_by: user.id,
            updated_by: user.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }));

          const { error: competentPersonError } = await supabase
            .from('competent_person')
            .insert(competentPersonsData);

          if (competentPersonError) {
            // Don't throw error here to avoid breaking the company creation
            // Just log the error for debugging
            toast.error(
              'Company created successfully, but there was an issue saving competent persons. You can add them later in the company settings.',
            );
          }
        }

        return { type: 'create', company: companyData };
      } else if (values.companyRegistrationType === 'join') {
        // Convert special code to uppercase for consistency (company codes should be uppercase)
        const uppercaseSpecialCode = values.specialCode!.toUpperCase().trim();

        // Find contractor company by code
        const { data: companyData, error: companyLookupError } = await supabase
          .from('contractors')
          .select('*')
          .eq('code', uppercaseSpecialCode)
          .single();

        if (companyLookupError || !companyData) {
          throw new Error(
            `Company not found with code "${uppercaseSpecialCode}". Please check the code and try again.`,
          );
        }

        // Check if user already has a contractor_id (already part of a company)
        const { data: existingUser } = await supabase
          .from('users')
          .select('contractor_id')
          .eq('id', user.id)
          .single();

        if (existingUser?.contractor_id) {
          throw new Error('You are already a member of a company');
        } // Join the company by updating user's contractor_id
        const { error: userUpdateError } = await supabase
          .from('users')
          .update({
            contractor_id: companyData.id,
            onboarding_completed: true,
            updated_by: user.id,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);

        if (userUpdateError) {
          throw new Error(`Failed to join company: ${userUpdateError.message}`);
        }

        return { type: 'join', company: companyData };
      }

      return { type: 'profile_only' };
    },
    onSuccess: async (result) => {
      try {
        // Clear onboarding and user role cookies to force refresh (only on client)
        if (typeof document !== 'undefined') {
          document.cookie =
            'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
          document.cookie =
            'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        }

        // Set a flag to indicate recent completion for fallback refresh (only on client)
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('onboarding_just_completed', 'true');
        }

        // Show success message first
        if (result?.type === 'create') {
          toast.success(
            'Company created successfully! Welcome to your new workspace.',
          );
        } else if (result?.type === 'join') {
          toast.success('Successfully joined the company!');
        } else {
          toast.success('Profile setup completed!');
        } // Invalidate all relevant queries and wait for completion
        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['user-with-profile'] }),
          queryClient.invalidateQueries({ queryKey: ['profile'] }),
          queryClient.invalidateQueries({ queryKey: ['contractor-profile'] }),
          queryClient.invalidateQueries({ queryKey: ['contractors'] }),
          queryClient.invalidateQueries({ queryKey: ['permissions'] }),
          queryClient.invalidateQueries({ queryKey: ['user'] }),
          queryClient.invalidateQueries({ queryKey: ['session'] }),
        ]);

        // Also clear the entire query cache to be extra sure
        queryClient.clear();

        // Use dedicated force refresh function for comprehensive cache clearing
        await forceProfileRefresh(); // Wait a bit longer to ensure cache is properly updated
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Navigate to projects after successful onboarding completion
        router.push('/projects');
      } catch {
        // Still navigate to projects even if cache cleanup fails
        router.push('/projects');
      }
    },
    onError: (error: Error) => {
      toast.error(
        error.message || 'Failed to complete onboarding. Please try again.',
      );
    },
  });
}
