import { authenticateWithPermission } from '@/features/auth';
import { pmaAccessControlService } from '@/features/billing/services/pma-access-control.service';
import { pmaSubscriptionsService } from '@/features/billing/services/pma-subscriptions.service';
import { NextRequest, NextResponse } from 'next/server';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/billing/pma-subscriptions/[id]/access
 * Check PMA access status for subscription
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user and check permissions for viewing projects
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id } = await params;

    // Get subscription to find PMA certificate ID
    const subscriptionResult = await pmaSubscriptionsService.getById(id);

    if (subscriptionResult.error) {
      return NextResponse.json(
        { error: subscriptionResult.error },
        { status: 500 },
      );
    }

    if (!subscriptionResult.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    const subscription = subscriptionResult.data;

    // Role-based access control: contractors can only check their own subscriptions
    if (
      user.user_role === 'contractor' &&
      subscription.contractors?.id !== user.id
    ) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check PMA access
    const accessResult = await pmaAccessControlService.checkPmaAccess(
      user.id,
      subscription.pma_certificates?.id || '',
      user.user_role,
    );

    return NextResponse.json({
      subscriptionId: id,
      pmaId: subscription.pma_certificates?.id,
      projectId: subscription.pma_certificates?.project_id,
      access: accessResult,
      subscription: subscription,
    });
  } catch (error) {
    console.error(
      'GET /api/billing/pma-subscriptions/[id]/access error:',
      error,
    );
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * POST /api/billing/pma-subscriptions/[id]/access
 * Perform access control actions on PMA subscription
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user and check permissions for updating projects
    const { user, error } = await authenticateWithPermission('projects.edit');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id } = await params;

    // Get subscription
    const subscriptionResult = await pmaSubscriptionsService.getById(id);

    if (subscriptionResult.error) {
      return NextResponse.json(
        { error: subscriptionResult.error },
        { status: 500 },
      );
    }

    if (!subscriptionResult.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    const subscription = subscriptionResult.data;

    // Role-based access control: contractors can only modify their own subscriptions
    if (
      user.user_role === 'contractor' &&
      subscription.contractors?.id !== user.id
    ) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { action, reason, gracePeriodDays } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 },
      );
    }

    let result;

    // Perform the requested action
    switch (action) {
      case 'check':
        // Just check access without modifications
        const accessResult = await pmaAccessControlService.checkPmaAccess(
          user.id,
          subscription.pma_certificates?.id || '',
          user.user_role,
        );
        return NextResponse.json({
          subscriptionId: id,
          access: accessResult,
        });

      case 'restore':
        result = await pmaSubscriptionsService.activateSubscription(id);
        break;

      case 'revoke':
      case 'suspend':
        result = await pmaSubscriptionsService.suspendSubscription(
          id,
          reason || 'Access revoked by admin',
        );
        break;

      case 'cancel':
        result = await pmaSubscriptionsService.cancelSubscription(id);
        break;

      case 'reactivate':
        result = await pmaSubscriptionsService.reactivateSubscription(id);
        break;

      case 'grace_period':
        result = await pmaSubscriptionsService.setGracePeriod(
          id,
          gracePeriodDays || 7,
        );
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 },
        );
    }

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    // Return updated access status
    const updatedAccessResult = await pmaAccessControlService.checkPmaAccess(
      user.id,
      subscription.pma_certificates?.id || '',
      user.user_role,
    );

    return NextResponse.json({
      subscriptionId: id,
      action: action,
      success: true,
      subscription: result.data,
      access: updatedAccessResult,
    });
  } catch (error) {
    console.error(
      'POST /api/billing/pma-subscriptions/[id]/access error:',
      error,
    );
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * PUT /api/billing/pma-subscriptions/[id]/access
 * Bulk access operations (for future use)
 */
export async function PUT(
  request: NextRequest,
  { params: _params }: RouteParams,
) {
  try {
    // Authenticate user and check permissions for managing projects (bulk operations require admin access)
    const { user, error } = await authenticateWithPermission('projects.manage');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Insufficient permissions for bulk operations' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Parse request body
    const body = await request.json();
    const { subscriptionIds, action, reason, gracePeriodDays } = body;

    if (!subscriptionIds || !Array.isArray(subscriptionIds)) {
      return NextResponse.json(
        { error: 'Subscription IDs array is required' },
        { status: 400 },
      );
    }

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 },
      );
    }

    // Process each subscription
    const results = [];
    for (const subscriptionId of subscriptionIds) {
      try {
        let result;

        switch (action) {
          case 'activate':
            result =
              await pmaSubscriptionsService.activateSubscription(
                subscriptionId,
              );
            break;
          case 'suspend':
            result = await pmaSubscriptionsService.suspendSubscription(
              subscriptionId,
              reason || 'Bulk suspension',
            );
            break;
          case 'cancel':
            result =
              await pmaSubscriptionsService.cancelSubscription(subscriptionId);
            break;
          case 'reactivate':
            result =
              await pmaSubscriptionsService.reactivateSubscription(
                subscriptionId,
              );
            break;
          case 'grace_period':
            result = await pmaSubscriptionsService.setGracePeriod(
              subscriptionId,
              gracePeriodDays || 7,
            );
            break;
          default:
            result = { error: `Unknown action: ${action}`, data: null };
        }

        results.push({
          subscriptionId,
          success: !result.error,
          error: result.error,
          data: result.data,
        });
      } catch (error) {
        results.push({
          subscriptionId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: null,
        });
      }
    }

    const successCount = results.filter((r) => r.success).length;
    const errorCount = results.filter((r) => !r.success).length;

    return NextResponse.json({
      action,
      totalProcessed: results.length,
      successCount,
      errorCount,
      results,
    });
  } catch (error) {
    console.error(
      'PUT /api/billing/pma-subscriptions/[id]/access error:',
      error,
    );
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
