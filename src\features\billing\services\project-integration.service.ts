import { supabase } from '@/lib/supabase';
import type { Database, Project } from '@/types';
import type { PmaSubscriptionWithDetails } from './pma-subscriptions.service';
import { pmaSubscriptionsService } from './pma-subscriptions.service';

export interface ProjectWithBilling {
  id: string;
  name: string;
  contractor_id: string | null;
  created_at: string | null;
  pmaSubscriptions?: PmaSubscriptionWithDetails[];
  billingStatus: 'active' | 'pending' | 'none';
  needsBillingSetup: boolean;
  totalPmaCount: number;
  activePmaSubscriptions: number;
}

export interface IntegrateProjectBillingParams {
  projectId: string;
  contractorId: string;
  autoActivate?: boolean;
  gracePeriodDays?: number;
}

export interface MigrationResult {
  totalProjects: number;
  successfulMigrations: number;
  failedMigrations: number;
  errors: string[];
  projectResults: {
    projectId: string;
    projectName: string;
    success: boolean;
    error?: string;
  }[];
}

export class ProjectIntegrationService {
  /**
   * Get project basic info by ID
   */
  async getProject(projectId: string): Promise<{
    data: { id: string; name: string } | null;
    error: string | null;
  }> {
    try {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id, name')
        .eq('id', projectId)
        .single();

      if (projectError) {
        return {
          data: null,
          error: projectError.message,
        };
      }

      return {
        data: project,
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getProjectsWithBillingStatus(contractorId?: string): Promise<{
    data: ProjectWithBilling[];
    error: string | null;
  }> {
    try {
      let projectQuery = supabase.from('projects').select(`
          id,
          name,
          contractor_id,
          created_at
        `);

      if (contractorId) {
        projectQuery = projectQuery.eq('contractor_id', contractorId);
      }

      const { data: projects, error: projectsError } = await projectQuery;

      if (projectsError) {
        return { data: [], error: projectsError.message };
      }

      if (!projects || projects.length === 0) {
        return { data: [], error: null };
      }

      // Get all PMA subscriptions for these projects
      const projectIds = projects.map((p) => p.id);

      // Get PMA certificates and their subscriptions
      const { data: pmaSubscriptions } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status
          )
        `,
        )
        .in('pma_certificates.project_id', projectIds);

      // Group PMA subscriptions by project
      const subscriptionsByProject = new Map<
        string,
        PmaSubscriptionWithDetails[]
      >();
      pmaSubscriptions?.forEach((sub) => {
        const projectId = sub.pma_certificates?.project_id;
        if (projectId) {
          if (!subscriptionsByProject.has(projectId)) {
            subscriptionsByProject.set(projectId, []);
          }
          subscriptionsByProject
            .get(projectId)!
            .push(sub as PmaSubscriptionWithDetails);
        }
      });

      // Get total PMA count per project
      const { data: pmaCounts } = await supabase
        .from('pma_certificates')
        .select('project_id')
        .in('project_id', projectIds)
        .is('deleted_at', null);

      const pmaCountsByProject = new Map<string, number>();
      pmaCounts?.forEach((pma) => {
        const count = pmaCountsByProject.get(pma.project_id!) || 0;
        pmaCountsByProject.set(pma.project_id!, count + 1);
      });

      // Combine project data with billing status
      const projectsWithBilling: ProjectWithBilling[] = projects.map(
        (project) => {
          const pmaSubscriptions = subscriptionsByProject.get(project.id) || [];
          const totalPmaCount = pmaCountsByProject.get(project.id) || 0;
          const activePmaSubscriptions = pmaSubscriptions.filter(
            (sub) => sub.status === 'active',
          ).length;

          let billingStatus: 'active' | 'pending' | 'none';
          let needsBillingSetup = false;

          if (totalPmaCount === 0) {
            billingStatus = 'none';
            needsBillingSetup = true;
          } else if (activePmaSubscriptions > 0) {
            billingStatus = 'active';
          } else {
            billingStatus = 'pending';
          }

          return {
            ...project,
            pmaSubscriptions,
            billingStatus,
            needsBillingSetup,
            totalPmaCount,
            activePmaSubscriptions,
          };
        },
      );

      return { data: projectsWithBilling, error: null };
    } catch (error) {
      console.error('Get projects with billing status error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async createProjectWithBilling(params: {
    projectData: {
      name: string;
      code?: string | null;
      agency_id?: string | null;
      location?: string | null;
      state?: Database['public']['Enums']['state_code'] | null;
      start_date?: string | null;
      end_date?: string | null;
      status?: string | null;
      contractor_id: string;
    };
    billingOptions?: {
      autoActivate?: boolean;
      gracePeriodDays?: number;
    };
    userId?: string;
    jkrUserId?: string;
  }): Promise<{
    data: {
      project: Project;
      pmaSubscriptions: PmaSubscriptionWithDetails[];
    } | null;
    error: string | null;
  }> {
    try {
      const {
        projectData,
        billingOptions: _billingOptions = {},
        userId,
        jkrUserId,
      } = params;

      // Create project first
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (projectError || !project) {
        return {
          data: null,
          error: projectError?.message || 'Failed to create project',
        };
      }

      // Create project user associations if userId is provided
      if (userId) {
        const projectUserInserts = [];

        // Add the project creator (contractor) to the project with admin role
        projectUserInserts.push({
          project_id: project.id,
          user_id: userId,
          role: 'admin' as const,
          status: 'accepted' as const,
          assigned_date: new Date().toISOString().split('T')[0],
          is_active: true,
          created_by: userId,
          created_at: new Date().toISOString(),
        });

        // Add JKR user if specified
        if (jkrUserId) {
          projectUserInserts.push({
            project_id: project.id,
            user_id: jkrUserId,
            role: 'admin' as const,
            status: 'accepted' as const,
            assigned_date: new Date().toISOString().split('T')[0],
            is_active: true,
            created_by: userId,
            created_at: new Date().toISOString(),
          });
        }

        // Insert all project user associations
        const { error: projectUserError } = await supabase
          .from('project_users')
          .insert(projectUserInserts);

        if (projectUserError) {
          // Log error but don't fail the entire operation
          console.error(
            'Failed to create project user associations:',
            projectUserError.message,
          );
          return {
            data: null,
            error: `Failed to create project user associations: ${projectUserError.message}`,
          };
        }
      }

      // Return project with empty PMA subscriptions
      // PMA subscriptions will be created later when PMA certificates are added
      return {
        data: {
          project,
          pmaSubscriptions: [],
        },
        error: null,
      };
    } catch (error) {
      console.error('Create project with billing error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async validateProjectBillingIntegrity(projectId?: string): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      let projectQuery = supabase.from('projects').select(`
          id,
          name,
          contractor_id,
          created_at
        `);

      if (projectId) {
        projectQuery = projectQuery.eq('id', projectId);
      }

      const { data: projects } = await projectQuery;

      if (!projects || projects.length === 0) {
        return {
          isValid: true,
          issues: ['No projects found'],
          recommendations: [],
        };
      }

      for (const project of projects) {
        // Check if project has PMA certificates
        const { data: pmaCertificates } = await supabase
          .from('pma_certificates')
          .select('id')
          .eq('project_id', project.id)
          .is('deleted_at', null);

        if (!pmaCertificates || pmaCertificates.length === 0) {
          issues.push(`Project "${project.name}" has no PMA certificates`);
          recommendations.push(
            `Add PMA certificates for project "${project.name}"`,
          );
          continue;
        }

        // Check PMA subscriptions for each certificate
        const pmaSubscriptionsResult =
          await pmaSubscriptionsService.getByProjectId(project.id);

        if (pmaSubscriptionsResult.error) {
          issues.push(
            `Failed to check PMA subscriptions for project "${project.name}"`,
          );
          continue;
        }

        const subscriptionMap = new Map(
          pmaSubscriptionsResult.data?.map(
            (sub: PmaSubscriptionWithDetails) => [sub.pma_certificate_id, sub],
          ) || [],
        );

        // Check each PMA certificate has a subscription
        for (const pmaCert of pmaCertificates) {
          const subscription = subscriptionMap.get(pmaCert.id);

          if (!subscription) {
            issues.push(
              `PMA certificate ${pmaCert.id} in project "${project.name}" has no billing subscription`,
            );
            recommendations.push(
              `Create PMA subscription for certificate in project "${project.name}"`,
            );
            continue;
          }

          // Check subscription validity
          if (
            !subscription.calculated_amount ||
            subscription.calculated_amount <= 0
          ) {
            issues.push(
              `PMA subscription for project "${project.name}" has invalid amount`,
            );
            recommendations.push(
              `Update amount for PMA subscription in project "${project.name}"`,
            );
          }

          if (!subscription.next_billing_date) {
            issues.push(
              `PMA subscription for project "${project.name}" has no next billing date`,
            );
            recommendations.push(
              `Set next billing date for PMA subscription in project "${project.name}"`,
            );
          }

          // Check for orphaned subscriptions
          if (subscription.contractor_id !== project.contractor_id) {
            issues.push(
              `PMA subscription for project "${project.name}" has contractor mismatch`,
            );
            recommendations.push(
              `Fix contractor association for PMA subscription in project "${project.name}"`,
            );
          }

          // Check for stale grace periods
          if (
            subscription.status === 'grace_period' &&
            subscription.grace_period_ends
          ) {
            const gracePeriodEnd = new Date(subscription.grace_period_ends);
            if (gracePeriodEnd < new Date()) {
              issues.push(
                `PMA subscription for project "${project.name}" has expired grace period`,
              );
              recommendations.push(
                `Suspend or reactivate PMA subscription for project "${project.name}"`,
              );
            }
          }
        }
      }

      return {
        isValid: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      console.error('Validate project billing integrity error:', error);
      return {
        isValid: false,
        issues: [
          `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ],
        recommendations: ['Review billing system configuration'],
      };
    }
  }
}

export const projectIntegrationService = new ProjectIntegrationService();
