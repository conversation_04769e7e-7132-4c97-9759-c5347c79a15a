'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { CreditCard, AlertTriangle, Shield } from 'lucide-react';
import type { NewCardData } from '../types';

export interface NewCardFormProps {
  onSubmit: (cardData: NewCardData) => Promise<void>;
  isLoading?: boolean;
  error?: Error | null;
  className?: string;
  showSaveOption?: boolean;
  allowSave?: boolean;
}

function formatCardNumber(value: string): string {
  // Remove all non-digit characters
  const cleaned = value.replace(/\D/g, '');
  // Add spaces every 4 digits
  return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
}

function formatExpiry(value: string): string {
  // Remove all non-digit characters
  const cleaned = value.replace(/\D/g, '');
  // Add slash after 2 digits
  if (cleaned.length >= 2) {
    return cleaned.slice(0, 2) + '/' + cleaned.slice(2, 4);
  }
  return cleaned;
}

function getCardBrand(cardNumber: string): string {
  const cleaned = cardNumber.replace(/\s/g, '');

  if (cleaned.startsWith('4')) return 'Visa';
  if (cleaned.startsWith('5') || cleaned.startsWith('2')) return 'Mastercard';
  if (cleaned.startsWith('3')) return 'Amex';

  return '';
}

function validateCardNumber(cardNumber: string): boolean {
  const cleaned = cardNumber.replace(/\s/g, '');

  // Basic length check
  if (cleaned.length < 13 || cleaned.length > 19) return false;

  // Luhn algorithm
  let sum = 0;
  let alternate = false;

  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i]);

    if (alternate) {
      digit *= 2;
      if (digit > 9) digit -= 9;
    }

    sum += digit;
    alternate = !alternate;
  }

  return sum % 10 === 0;
}

function validateExpiry(expiry: string): {
  isValid: boolean;
  month?: number;
  year?: number;
} {
  const [month, year] = expiry.split('/').map((part) => parseInt(part));

  if (!month || !year) return { isValid: false };
  if (month < 1 || month > 12) return { isValid: false };

  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const fullYear = year < 50 ? 2000 + year : 1900 + year;

  if (fullYear < currentYear) return { isValid: false };
  if (fullYear === currentYear && month < currentMonth)
    return { isValid: false };
  if (fullYear > currentYear + 20) return { isValid: false };

  return { isValid: true, month, year: fullYear };
}

function validateCVC(cvc: string, cardBrand: string): boolean {
  if (cardBrand.toLowerCase() === 'amex') {
    return /^\d{4}$/.test(cvc);
  }
  return /^\d{3}$/.test(cvc);
}

export function NewCardForm({
  onSubmit,
  isLoading = false,
  error,
  className,
  showSaveOption = true,
  allowSave = true,
}: NewCardFormProps) {
  const [formData, setFormData] = useState({
    number: '',
    expiry: '',
    cvc: '',
    name: '',
    saveCard: false,
  });

  const [validationErrors, setValidationErrors] = useState({
    number: '',
    expiry: '',
    cvc: '',
    name: '',
  });

  const cardBrand = getCardBrand(formData.number);

  const validateField = (field: string, value: string) => {
    const errors = { ...validationErrors };

    switch (field) {
      case 'number':
        if (!value) {
          errors.number = 'Card number is required';
        } else if (!validateCardNumber(value)) {
          errors.number = 'Invalid card number';
        } else {
          errors.number = '';
        }
        break;

      case 'expiry':
        if (!value) {
          errors.expiry = 'Expiry date is required';
        } else {
          const validation = validateExpiry(value);
          if (!validation.isValid) {
            errors.expiry = 'Invalid expiry date';
          } else {
            errors.expiry = '';
          }
        }
        break;

      case 'cvc':
        if (!value) {
          errors.cvc = 'CVC is required';
        } else if (!validateCVC(value, cardBrand)) {
          errors.cvc =
            cardBrand.toLowerCase() === 'amex'
              ? 'CVC must be 4 digits for Amex'
              : 'CVC must be 3 digits';
        } else {
          errors.cvc = '';
        }
        break;

      case 'name':
        if (!value.trim()) {
          errors.name = 'Cardholder name is required';
        } else if (value.trim().length < 2) {
          errors.name = 'Name must be at least 2 characters';
        } else {
          errors.name = '';
        }
        break;
    }

    setValidationErrors(errors);
  };

  const handleInputChange = (field: string, value: string) => {
    let processedValue = value;

    if (field === 'number') {
      processedValue = formatCardNumber(value);
      if (processedValue.replace(/\s/g, '').length > 19) return;
    } else if (field === 'expiry') {
      processedValue = formatExpiry(value);
      if (processedValue.replace(/\D/g, '').length > 4) return;
    } else if (field === 'cvc') {
      processedValue = value.replace(/\D/g, '');
      const maxLength = cardBrand.toLowerCase() === 'amex' ? 4 : 3;
      if (processedValue.length > maxLength) return;
    }

    setFormData((prev) => ({ ...prev, [field]: processedValue }));
    validateField(field, processedValue);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    validateField('number', formData.number);
    validateField('expiry', formData.expiry);
    validateField('cvc', formData.cvc);
    validateField('name', formData.name);

    // Check if there are any validation errors
    const hasErrors = Object.values(validationErrors).some(
      (error) => error !== '',
    );
    if (hasErrors) return;

    const expiryValidation = validateExpiry(formData.expiry);
    if (
      !expiryValidation.isValid ||
      !expiryValidation.month ||
      !expiryValidation.year
    ) {
      return;
    }

    const cardData: NewCardData = {
      number: formData.number.replace(/\s/g, ''),
      exp_month: expiryValidation.month,
      exp_year: expiryValidation.year,
      cvc: formData.cvc,
      name: formData.name.trim(),
      save_card: showSaveOption && allowSave ? formData.saveCard : false,
    };

    await onSubmit(cardData);
  };

  const isFormValid =
    formData.number &&
    formData.expiry &&
    formData.cvc &&
    formData.name &&
    !Object.values(validationErrors).some((error) => error !== '');

  return (
    <form onSubmit={handleSubmit} className={cn('space-y-4', className)}>
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error.message || 'Failed to process card'}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="cardNumber">Card Number</Label>
        <div className="relative">
          <Input
            id="cardNumber"
            value={formData.number}
            onChange={(e) => handleInputChange('number', e.target.value)}
            placeholder="1234 5678 9012 3456"
            className={cn(
              'pl-10',
              validationErrors.number && 'border-destructive',
            )}
            disabled={isLoading}
          />
          <CreditCard className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          {cardBrand && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs font-medium text-muted-foreground">
              {cardBrand}
            </div>
          )}
        </div>
        {validationErrors.number && (
          <p className="text-sm text-destructive">{validationErrors.number}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="expiry">Expiry Date</Label>
          <Input
            id="expiry"
            value={formData.expiry}
            onChange={(e) => handleInputChange('expiry', e.target.value)}
            placeholder="MM/YY"
            className={cn(validationErrors.expiry && 'border-destructive')}
            disabled={isLoading}
          />
          {validationErrors.expiry && (
            <p className="text-sm text-destructive">
              {validationErrors.expiry}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="cvc">CVC</Label>
          <Input
            id="cvc"
            value={formData.cvc}
            onChange={(e) => handleInputChange('cvc', e.target.value)}
            placeholder={cardBrand.toLowerCase() === 'amex' ? '1234' : '123'}
            className={cn(validationErrors.cvc && 'border-destructive')}
            disabled={isLoading}
          />
          {validationErrors.cvc && (
            <p className="text-sm text-destructive">{validationErrors.cvc}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="cardholderName">Cardholder Name</Label>
        <Input
          id="cardholderName"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          placeholder="John Doe"
          className={cn(validationErrors.name && 'border-destructive')}
          disabled={isLoading}
        />
        {validationErrors.name && (
          <p className="text-sm text-destructive">{validationErrors.name}</p>
        )}
      </div>

      {showSaveOption && allowSave && (
        <div className="flex items-center space-x-2">
          <Checkbox
            id="saveCard"
            checked={formData.saveCard}
            onCheckedChange={(checked) =>
              setFormData((prev) => ({ ...prev, saveCard: checked === true }))
            }
            disabled={isLoading}
          />
          <Label htmlFor="saveCard" className="text-sm">
            Save this card for future payments
          </Label>
        </div>
      )}

      <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg text-sm text-muted-foreground">
        <Shield className="h-4 w-4" />
        <span>Your card details are securely processed by BillPlz</span>
      </div>

      <Button
        type="submit"
        disabled={!isFormValid || isLoading}
        className="w-full"
      >
        {isLoading ? 'Processing...' : 'Add Card & Continue'}
      </Button>
    </form>
  );
}
