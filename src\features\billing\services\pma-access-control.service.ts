import { hasPermission } from '@/lib/rbac';
import { supabase } from '@/lib/supabase';
import type { UserRole } from '@/types/auth';
import { getDaysRemainingInGracePeriod } from '@/types/billing';
import type { Database } from '@/types/database';

type PmaSubscription = Database['public']['Tables']['pma_subscriptions']['Row'];
type PmaSubscriptionWithCert = PmaSubscription & {
  pma_certificates: {
    id: string;
    project_id: string | null;
    pma_number: string | null;
    status: string;
  } | null;
};

export interface PmaAccessResult {
  hasAccess: boolean;
  reason:
    | 'active'
    | 'grace_period'
    | 'trial'
    | 'admin_bypass'
    | 'no_subscription'
    | 'suspended'
    | 'expired'
    | 'invalid_pma';
  subscription?: PmaSubscriptionWithAccess | null;
  gracePeriodDays?: number;
  message: string;
}

export interface PmaSubscriptionWithAccess extends PmaSubscriptionWithCert {
  accessAllowed: boolean;
  isInGracePeriod: boolean;
  daysUntilSuspension?: number;
}

export interface ProjectPmaAccess {
  projectId: string;
  pmaAccess: Map<string, PmaAccessResult>; // PMA certificate ID -> access result
  overallAccess: boolean; // true if user has access to at least one PMA in the project
  activeSubscriptions: number;
  totalPmas: number;
}

export interface UserPmaAccess {
  userId: string;
  userRole: UserRole;
  pmaId: string;
  hasAccess: boolean;
  accessType: 'role_based' | 'subscription_based' | 'denied';
  subscription?: PmaSubscriptionWithAccess | null;
}

export class PmaAccessControlService {
  /**
   * Check access to a specific PMA certificate
   */
  async checkPmaAccess(
    userId: string,
    pmaId: string,
    userRole?: UserRole,
  ): Promise<PmaAccessResult> {
    try {
      // Get user role if not provided
      let role = userRole;
      if (!role) {
        const { data: user } = await supabase
          .from('users')
          .select('user_role')
          .eq('id', userId)
          .single();

        if (!user) {
          return {
            hasAccess: false,
            reason: 'no_subscription',
            message: 'User not found',
          };
        }
        role = user.user_role as UserRole;
      }

      // Admin users bypass billing restrictions
      if (role === 'admin') {
        return {
          hasAccess: true,
          reason: 'admin_bypass',
          message: 'Admin access granted',
        };
      }

      // Check if PMA certificate exists and is valid
      const { data: pmaCert } = await supabase
        .from('pma_certificates')
        .select('id, project_id, pma_number, status')
        .eq('id', pmaId)
        .is('deleted_at', null)
        .single();

      if (!pmaCert) {
        return {
          hasAccess: false,
          reason: 'invalid_pma',
          message: 'PMA certificate not found',
        };
      }

      if (pmaCert.status !== 'valid') {
        return {
          hasAccess: false,
          reason: 'invalid_pma',
          message: `PMA certificate status is ${pmaCert.status}`,
        };
      }

      // For contractors and viewers, check subscription status
      if (role === 'contractor' || role === 'viewer') {
        // Get subscription for this PMA with certificate details
        const { data: subscription } = await supabase
          .from('pma_subscriptions')
          .select(
            `
            *,
            pma_certificates!inner (
              id,
              project_id,
              pma_number,
              status
            )
          `,
          )
          .eq('pma_certificate_id', pmaId)
          .single();

        if (!subscription) {
          return {
            hasAccess: false,
            reason: 'no_subscription',
            message: 'No subscription found for this PMA certificate',
          };
        }

        // Verify the user is associated with this subscription
        if (subscription.contractor_id !== userId && role === 'contractor') {
          return {
            hasAccess: false,
            reason: 'no_subscription',
            message: 'User is not the contractor for this subscription',
          };
        }

        // Check subscription access using PMA-specific logic
        const accessAllowed = this.checkPmaSubscriptionAccess(subscription);
        const isInGracePeriod = subscription.status === 'grace_period';
        const daysUntilSuspension = isInGracePeriod
          ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
          : undefined;

        const subscriptionWithAccess: PmaSubscriptionWithAccess = {
          ...subscription,
          accessAllowed,
          isInGracePeriod,
          daysUntilSuspension: daysUntilSuspension ?? undefined,
        };

        if (accessAllowed) {
          const reason =
            subscription.status === 'active'
              ? 'active'
              : subscription.status === 'trial'
                ? 'trial'
                : 'grace_period';

          const message =
            subscription.status === 'active'
              ? 'Active subscription'
              : subscription.status === 'trial'
                ? 'Trial period active'
                : `Grace period: ${daysUntilSuspension} days remaining`;

          return {
            hasAccess: true,
            reason,
            subscription: subscriptionWithAccess,
            gracePeriodDays: daysUntilSuspension ?? undefined,
            message,
          };
        }

        // Determine specific reason for denial
        const reason =
          subscription.status === 'suspended'
            ? ('suspended' as const)
            : subscription.status === 'cancelled'
              ? ('expired' as const)
              : subscription.status === 'grace_period'
                ? ('expired' as const)
                : ('no_subscription' as const);

        return {
          hasAccess: false,
          reason,
          subscription: subscriptionWithAccess,
          message: this.getAccessDeniedMessage(subscription.status),
        };
      }

      return {
        hasAccess: false,
        reason: 'no_subscription',
        message: 'Invalid user role',
      };
    } catch (error) {
      console.error('Check PMA access error:', error);
      return {
        hasAccess: false,
        reason: 'no_subscription',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check access to all PMAs in a project and return aggregated access info
   */
  async checkProjectPmaAccess(
    userId: string,
    projectId: string,
    userRole?: UserRole,
  ): Promise<ProjectPmaAccess> {
    try {
      // Get user role if not provided
      let role = userRole;
      if (!role) {
        const { data: user } = await supabase
          .from('users')
          .select('user_role')
          .eq('id', userId)
          .single();

        if (!user) {
          throw new Error('User not found');
        }
        role = user.user_role as UserRole;
      }

      // Get all valid PMA certificates for this project
      const { data: pmaCerts } = await supabase
        .from('pma_certificates')
        .select('id, project_id, pma_number, status')
        .eq('project_id', projectId)
        .eq('status', 'valid')
        .is('deleted_at', null);

      if (!pmaCerts || pmaCerts.length === 0) {
        return {
          projectId,
          pmaAccess: new Map(),
          overallAccess: role === 'admin', // Admins have access even without PMAs
          activeSubscriptions: 0,
          totalPmas: 0,
        };
      }

      // Check access for each PMA
      const pmaAccessMap = new Map<string, PmaAccessResult>();
      const accessPromises = pmaCerts.map(async (pma) => {
        const result = await this.checkPmaAccess(userId, pma.id, role);
        return { pmaId: pma.id, result };
      });

      const results = await Promise.all(accessPromises);

      let activeSubscriptions = 0;
      let hasAnyAccess = false;

      for (const { pmaId, result } of results) {
        pmaAccessMap.set(pmaId, result);
        if (result.hasAccess) {
          hasAnyAccess = true;
          if (result.reason === 'active' || result.reason === 'admin_bypass') {
            activeSubscriptions++;
          }
        }
      }

      return {
        projectId,
        pmaAccess: pmaAccessMap,
        overallAccess: hasAnyAccess,
        activeSubscriptions,
        totalPmas: pmaCerts.length,
      };
    } catch (error) {
      console.error('Check project PMA access error:', error);
      return {
        projectId,
        pmaAccess: new Map(),
        overallAccess: false,
        activeSubscriptions: 0,
        totalPmas: 0,
      };
    }
  }

  /**
   * Get all projects that a user has access to based on PMA subscriptions
   */
  async getUserAccessibleProjects(
    userId: string,
    userRole?: UserRole,
  ): Promise<{ projectIds: string[]; error: string | null }> {
    try {
      // Get user role if not provided
      let role = userRole;
      if (!role) {
        const { data: user } = await supabase
          .from('users')
          .select('user_role')
          .eq('id', userId)
          .single();

        if (!user) {
          return { projectIds: [], error: 'User not found' };
        }
        role = user.user_role as UserRole;
      }

      // Admin users can access all projects
      if (role === 'admin') {
        const { data: projects } = await supabase
          .from('projects')
          .select('id')
          .is('deleted_at', null);

        return {
          projectIds: projects?.map((p) => p.id) || [],
          error: null,
        };
      }

      // For contractors, get projects through valid PMA subscriptions
      if (role === 'contractor') {
        const { data: subscriptions } = await supabase
          .from('pma_subscriptions')
          .select(
            `
            pma_certificates!inner (
              project_id
            )
          `,
          )
          .eq('contractor_id', userId)
          .in('status', ['active', 'grace_period']);

        if (!subscriptions) {
          return { projectIds: [], error: null };
        }

        // Extract unique project IDs
        const projectIds = [
          ...new Set(
            subscriptions
              .map((sub) => sub.pma_certificates?.project_id)
              .filter((id): id is string => id !== null && id !== undefined),
          ),
        ];

        return { projectIds, error: null };
      }

      return { projectIds: [], error: null };
    } catch (error) {
      console.error('Get user accessible projects error:', error);
      return {
        projectIds: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get user's accessible PMAs across all projects
   */
  async getUserAccessiblePmas(
    userId: string,
    userRole?: UserRole,
  ): Promise<{ pmaIds: string[]; error: string | null }> {
    try {
      // Get user role if not provided
      let role = userRole;
      if (!role) {
        const { data: user } = await supabase
          .from('users')
          .select('user_role')
          .eq('id', userId)
          .single();

        if (!user) {
          return { pmaIds: [], error: 'User not found' };
        }
        role = user.user_role as UserRole;
      }

      // Admin users can access all valid PMAs
      if (role === 'admin') {
        const { data: pmas } = await supabase
          .from('pma_certificates')
          .select('id')
          .eq('status', 'valid')
          .is('deleted_at', null);

        return {
          pmaIds: pmas?.map((p) => p.id) || [],
          error: null,
        };
      }

      // For contractors, get PMAs through valid subscriptions
      if (role === 'contractor') {
        const { data: subscriptions } = await supabase
          .from('pma_subscriptions')
          .select('pma_certificate_id')
          .eq('contractor_id', userId)
          .in('status', ['active', 'grace_period']);

        if (!subscriptions) {
          return { pmaIds: [], error: null };
        }

        return {
          pmaIds: subscriptions.map((sub) => sub.pma_certificate_id),
          error: null,
        };
      }

      return { pmaIds: [], error: null };
    } catch (error) {
      console.error('Get user accessible PMAs error:', error);
      return {
        pmaIds: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate comprehensive PMA access for a user
   */
  async validatePmaAccess(params: {
    userId: string;
    userRole: UserRole;
    pmaId: string;
    requireActiveSubscription?: boolean;
  }): Promise<{
    isValid: boolean;
    userPmaAccess: UserPmaAccess;
    redirectPath?: string;
  }> {
    const {
      userId,
      userRole,
      pmaId,
      requireActiveSubscription = true,
    } = params;

    try {
      // Check basic role permissions
      const hasProjectPermission = hasPermission(userRole, 'projects.view');

      if (!hasProjectPermission) {
        return {
          isValid: false,
          userPmaAccess: {
            userId,
            userRole,
            pmaId,
            hasAccess: false,
            accessType: 'denied',
          },
          redirectPath: '/dashboard',
        };
      }

      // Admin users have automatic access
      if (userRole === 'admin') {
        return {
          isValid: true,
          userPmaAccess: {
            userId,
            userRole,
            pmaId,
            hasAccess: true,
            accessType: 'role_based',
          },
        };
      }

      // Check subscription-based access
      const accessResult = await this.checkPmaAccess(userId, pmaId, userRole);

      // If access is required and user doesn't have it, redirect to billing
      if (requireActiveSubscription && !accessResult.hasAccess) {
        // Get project ID for redirect
        const { data: pmaCert } = await supabase
          .from('pma_certificates')
          .select('project_id')
          .eq('id', pmaId)
          .single();

        const projectId = pmaCert?.project_id || '';
        const redirectPath =
          accessResult.reason === 'no_subscription'
            ? `/billing/subscribe?pmaId=${pmaId}&projectId=${projectId}`
            : `/billing/payment?pmaId=${pmaId}&projectId=${projectId}`;

        return {
          isValid: false,
          userPmaAccess: {
            userId,
            userRole,
            pmaId,
            hasAccess: false,
            accessType: 'denied',
            subscription: accessResult.subscription,
          },
          redirectPath,
        };
      }

      return {
        isValid: true,
        userPmaAccess: {
          userId,
          userRole,
          pmaId,
          hasAccess: accessResult.hasAccess,
          accessType: 'subscription_based',
          subscription: accessResult.subscription,
        },
      };
    } catch (error) {
      console.error('Validate PMA access error:', error);
      return {
        isValid: false,
        userPmaAccess: {
          userId,
          userRole,
          pmaId,
          hasAccess: false,
          accessType: 'denied',
        },
        redirectPath: '/dashboard',
      };
    }
  }

  private getAccessDeniedMessage(status: string): string {
    switch (status) {
      case 'suspended':
        return 'PMA access has been suspended';
      case 'cancelled':
        return 'PMA subscription has been cancelled';
      case 'pending_payment':
        return 'Payment is pending for this PMA certificate';
      case 'grace_period':
        return 'Grace period has expired for this PMA';
      default:
        return 'Access denied due to subscription status';
    }
  }

  /**
   * Check if PMA subscription allows access
   * Similar to checkProjectAccess but for PMA subscriptions
   */
  private checkPmaSubscriptionAccess(subscription: PmaSubscription): boolean {
    // Active status always allows access
    if (subscription.status === 'active') {
      return true;
    }

    // Grace period allows access if not expired
    if (
      subscription.status === 'grace_period' &&
      subscription.grace_period_ends
    ) {
      const now = new Date();
      const gracePeriodEnd = new Date(subscription.grace_period_ends);
      return now < gracePeriodEnd;
    }

    // All other statuses deny access
    return false;
  }
}

export const pmaAccessControlService = new PmaAccessControlService();
