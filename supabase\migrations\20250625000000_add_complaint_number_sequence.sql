-- Add sequence for synchronized complaint numbers
-- This ensures sequential, non-random ticket IDs per year

-- Create a function to generate synchronized complaint numbers
CREATE OR REPLACE FUNCTION generate_complaint_number()
RETURNS text AS $$
DECLARE
    current_year INTEGER;
    next_number INTEGER;
    complaint_number TEXT;
BEGIN
    -- Get current year
    current_year := EXTRACT(YEAR FROM CURRENT_DATE);
    
    -- Get the highest number for current year
    SELECT COALESCE(
        MAX(CAST(SPLIT_PART(number, '-', 3) AS INTEGER)), 0
    ) + 1
    INTO next_number
    FROM complaints
    WHERE number LIKE 'DCL-' || current_year || '-%';
    
    -- Format the complaint number with zero-padding
    complaint_number := 'DCL-' || current_year || '-' || LPAD(next_number::text, 4, '0');
    
    RETURN complaint_number;
END;
$$ LANGUAGE plpgsql;

-- Update existing complaints to have properly formatted numbers if they don't already
UPDATE complaints 
SET number = generate_complaint_number()
WHERE number IS NULL OR number = '';

-- Add a trigger to automatically generate complaint numbers on insert
CREATE OR REPLACE FUNCTION set_complaint_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.number IS NULL OR NEW.number = '' THEN
        NEW.number := generate_complaint_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger that fires before insert
DROP TRIGGER IF EXISTS trigger_set_complaint_number ON complaints;
CREATE TRIGGER trigger_set_complaint_number
    BEFORE INSERT ON complaints
    FOR EACH ROW
    EXECUTE FUNCTION set_complaint_number();
