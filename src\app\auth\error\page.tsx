'use client';

import { But<PERSON> } from '@/components/ui/button';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const error = searchParams?.get('error') || 'unknown';

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case 'invalid_code':
        return {
          title: 'Invalid Verification Link',
          message:
            'The verification link is invalid or has expired. Please try signing up again.',
          action: 'Back to Sign Up',
        };
      case 'callback_failed':
        return {
          title: 'Authentication Failed',
          message:
            'Something went wrong during authentication. Please try again.',
          action: 'Try Again',
        };
      case 'access_denied':
        return {
          title: 'Access Denied',
          message: 'You do not have permission to access this resource.',
          action: 'Back to Login',
        };
      default:
        return {
          title: 'Authentication Error',
          message: 'An unexpected error occurred. Please try again.',
          action: 'Back to Login',
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
            {errorInfo.title}
          </h2>
          <p className="mt-2 text-sm text-gray-600">{errorInfo.message}</p>
        </div>

        <div className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/contractor/register">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {errorInfo.action}
            </Link>
          </Button>

          <div className="text-center">
            <Link
              href="/contractor/login"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Loading...</h2>
          </div>
        </div>
      }
    >
      <AuthErrorContent />
    </Suspense>
  );
}
