import {
  canAccessRoute,
  canPerformAction,
  getUserPermissions,
  hasPermission,
  hasRoleOrHigher,
} from '@/lib/rbac';
import type { UserRole } from '@/types/auth';
import type { Action, Permission, Resource } from '@/types/rbac';
import { useMemo } from 'react';
import { useUserWithProfile } from './use-auth';

export interface UsePermissionsReturn {
  userRole: UserRole | null;
  permissions: Permission[];
  hasPermission: (permission: Permission) => boolean;
  canAccessRoute: (path: string) => boolean;
  canPerformAction: (resource: Resource, action: Action) => boolean;
  hasRoleOrHigher: (requiredRole: UserRole) => boolean;
  isLoading: boolean;
  isJKR: boolean;
  isContractor: boolean;
  isClient: boolean;
}

/**
 * Hook to get user permissions based on their role
 */
export function usePermissions(): UsePermissionsReturn {
  const { data: user, isLoading } = useUserWithProfile();

  const userRole = useMemo(() => {
    // Ensure we have a valid user role from the profile
    const role = user?.profile?.user_role;
    return role || null;
  }, [user?.profile?.user_role]);

  const permissions = useMemo(() => {
    return getUserPermissions(userRole);
  }, [userRole]);

  const permissionCheckers = useMemo(() => {
    return {
      hasPermission: (permission: Permission) =>
        hasPermission(userRole, permission),
      canAccessRoute: (path: string) => canAccessRoute(userRole, path),
      canPerformAction: (resource: Resource, action: Action) =>
        canPerformAction(userRole, resource, action),
      hasRoleOrHigher: (requiredRole: UserRole) =>
        hasRoleOrHigher(userRole, requiredRole),
    };
  }, [userRole]);

  const roleCheckers = useMemo(() => {
    return {
      isJKR: userRole === 'admin', // Admin users are equivalent to JKR users
      isContractor: userRole === 'contractor',
      isClient: userRole === 'viewer', // Viewer users are equivalent to clients
    };
  }, [userRole]);

  return {
    userRole,
    permissions,
    isLoading,
    ...permissionCheckers,
    ...roleCheckers,
  };
}

/**
 * Hook to check a specific permission
 */
export function useHasPermission(permission: Permission): boolean {
  const { hasPermission } = usePermissions();
  return hasPermission(permission);
}

/**
 * Hook to check route access
 */
export function useCanAccessRoute(path: string): boolean {
  const { canAccessRoute } = usePermissions();
  return canAccessRoute(path);
}

/**
 * Hook to check if user has a specific role or higher
 */
export function useHasRoleOrHigher(requiredRole: UserRole): boolean {
  const { hasRoleOrHigher } = usePermissions();
  return hasRoleOrHigher(requiredRole);
}
