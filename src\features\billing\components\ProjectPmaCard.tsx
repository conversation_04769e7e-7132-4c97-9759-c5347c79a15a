'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import type { ProjectWithPmas } from '@/features/billing/types';
import { cn } from '@/lib/utils';
import {
  Calendar,
  ChevronDown,
  ChevronRight,
  DollarSign,
  FolderOpen,
  MapPin,
  Shield,
  Zap,
} from 'lucide-react';
import { useState } from 'react';
import { SubscriptionCard } from './SubscriptionCard';

export interface ProjectPmaCardProps {
  project: ProjectWithPmas;
  onPayNow?: (subscriptionId: string) => void;
  onViewProject?: (projectId: string) => void;
  onManageSubscription?: (subscriptionId: string) => void;
  onCancel?: (subscriptionId: string) => void;
  onReactivate?: (subscriptionId: string) => void;
  onUpdatePaymentMethod?: (subscriptionId: string) => void;
  onViewPaymentHistory?: (subscriptionId: string) => void;
  onUpdateBilling?: (subscriptionId: string) => void;
  onDownloadReceipt?: (subscriptionId: string) => void;
  className?: string;
  defaultExpanded?: boolean;
}

export function ProjectPmaCard({
  project,
  onPayNow,
  onViewProject,
  onManageSubscription,
  onCancel,
  onReactivate,
  onUpdatePaymentMethod,
  onViewPaymentHistory,
  onUpdateBilling,
  onDownloadReceipt,
  className,
  defaultExpanded = false,
}: ProjectPmaCardProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const getProjectStatus = (): 'healthy' | 'warning' | 'critical' => {
    if (project.suspended_pmas > 0) return 'critical';
    if (project.grace_period_pmas > 0 || project.has_urgent_payments)
      return 'warning';
    return 'healthy';
  };

  const getStatusColor = (status: 'healthy' | 'warning' | 'critical') => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
    }
  };

  const status = getProjectStatus();
  const statusColorClass = getStatusColor(status);

  return (
    <Card className={cn('transition-all hover:shadow-md', className)}>
      <CardHeader
        className="cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1">
            <div className="flex-shrink-0">
              <div className="p-2 rounded-lg bg-primary/10">
                <FolderOpen className="h-5 w-5 text-primary" />
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <CardTitle className="text-lg truncate">
                  {project.project_name}
                </CardTitle>
                {project.has_urgent_payments && (
                  <Badge variant="destructive" className="text-xs">
                    <Zap className="h-3 w-3 mr-1" />
                    Urgent
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                {project.project_location && (
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span className="truncate">{project.project_location}</span>
                  </div>
                )}

                <div className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  <span>
                    {project.pma_count} PMA{project.pma_count !== 1 ? 's' : ''}
                  </span>
                </div>

                <div className="flex items-center gap-1">
                  <DollarSign className="h-3 w-3" />
                  <span>
                    RM {project.total_monthly_amount.toFixed(2)}/month
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Status Badge */}
            <div
              className={cn(
                'px-2 py-1 rounded-md border text-xs font-medium',
                statusColorClass,
              )}
            >
              {project.active_pmas} Active
              {project.suspended_pmas > 0 &&
                ` • ${project.suspended_pmas} Suspended`}
              {project.grace_period_pmas > 0 &&
                ` • ${project.grace_period_pmas} Grace`}
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-1">
              {/* {onViewProject && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewProject(project.project_id);
                  }}
                >
                  View
                </Button>
              )} */}

              {/* Expand/Collapse Indicator */}
              <div className="ml-2">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                )}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <Separator className="mb-4" />

          {/* Project Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {project.active_pmas}
              </div>
              <div className="text-xs text-muted-foreground">Active PMAs</div>
            </div>

            {project.grace_period_pmas > 0 && (
              <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="text-2xl font-bold text-yellow-600">
                  {project.grace_period_pmas}
                </div>
                <div className="text-xs text-muted-foreground">
                  Grace Period
                </div>
              </div>
            )}

            {project.suspended_pmas > 0 && (
              <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
                <div className="text-2xl font-bold text-red-600">
                  {project.suspended_pmas}
                </div>
                <div className="text-xs text-muted-foreground">Suspended</div>
              </div>
            )}

            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="text-lg font-bold">
                RM {project.total_monthly_amount.toFixed(2)}
              </div>
              <div className="text-xs text-muted-foreground">Monthly Total</div>
            </div>
          </div>

          {/* Next Billing Date */}
          {project.next_billing_date && (
            <div className="flex items-center gap-2 mb-4 p-3 bg-muted/30 rounded-lg">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Next billing:
              </span>
              <span className="text-sm font-medium">
                {new Date(project.next_billing_date).toLocaleDateString(
                  'en-MY',
                  {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                  },
                )}
              </span>
            </div>
          )}

          {/* Individual PMA Subscription Cards */}
          <div className="space-y-4">
            <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
              PMA Subscriptions ({project.pma_count})
            </h4>

            <div className="space-y-3">
              {project.pma_subscriptions.map((subscription) => (
                <SubscriptionCard
                  key={subscription.id}
                  subscription={subscription}
                  projectName={
                    subscription.pma_certificates?.pma_number || 'Unknown PMA'
                  }
                  projectId={project.project_id}
                  onPayNow={onPayNow}
                  onViewProject={onViewProject}
                  _onManageSubscription={onManageSubscription}
                  onCancel={onCancel}
                  onReactivate={onReactivate}
                  onUpdatePaymentMethod={onUpdatePaymentMethod}
                  onViewPaymentHistory={onViewPaymentHistory}
                  onUpdateBilling={onUpdateBilling}
                  onDownloadReceipt={onDownloadReceipt}
                  variant="compact"
                  className="ml-4"
                />
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
