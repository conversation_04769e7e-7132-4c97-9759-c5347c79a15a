import { useUserWithProfile } from '@/hooks/use-auth';
import type { PmaSubscriptionWithAccess } from '@/types/billing';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';

// Types that were previously imported from services
export interface ProjectAccessResult {
  hasAccess: boolean;
  reason:
    | 'active'
    | 'grace_period'
    | 'admin_bypass'
    | 'no_subscription'
    | 'suspended'
    | 'expired';
  subscription?: PmaSubscriptionWithAccess | null;
  gracePeriodDays?: number;
  message: string;
}

// ================================
// ACCESS STATE TYPES
// ================================

export interface AccessState {
  hasAccess: boolean;
  subscription: PmaSubscriptionWithAccess | null;
  reason: AccessDeniedReason | null;
  gracePeriodDaysRemaining: number | null;
  isInGracePeriod: boolean;
}

export type AccessDeniedReason =
  | 'no_subscription'
  | 'payment_overdue'
  | 'subscription_cancelled'
  | 'subscription_suspended'
  | 'grace_period_expired'
  | 'project_not_found'
  | 'user_not_authorized';

// ================================
// PROJECT ACCESS QUERIES
// ================================

/**
 * Check if user has access to a specific project based on billing status
 */
export function useCheckProjectAccess(projectId: string) {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['project-access', projectId, user?.id],
    queryFn: async (): Promise<AccessState> => {
      if (!user || !projectId) {
        return {
          hasAccess: false,
          subscription: null,
          reason: 'user_not_authorized',
          gracePeriodDaysRemaining: null,
          isInGracePeriod: false,
        };
      }

      // Use API route instead of direct service call
      const response = await fetch(
        `/api/billing/projects/${projectId}/access?userId=${user.id}&role=${user.profile?.user_role}`,
      );
      if (!response.ok) {
        throw new Error('Failed to check project access');
      }
      const apiResult = await response.json();
      const accessResult: ProjectAccessResult = apiResult.data.accessStatus;

      // Map service result to hook interface
      const mapReason = (
        reason: ProjectAccessResult['reason'],
      ): AccessDeniedReason | null => {
        switch (reason) {
          case 'no_subscription':
            return 'no_subscription';
          case 'suspended':
            return 'subscription_suspended';
          case 'expired':
            return 'grace_period_expired';
          default:
            return null;
        }
      };

      return {
        hasAccess: accessResult.hasAccess,
        subscription: accessResult.subscription || null,
        reason: mapReason(accessResult.reason),
        gracePeriodDaysRemaining: accessResult.gracePeriodDays || null,
        isInGracePeriod: accessResult.subscription?.isInGracePeriod || false,
      };
    },
    enabled: !!user && !!projectId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute for billing status changes
  });
}

// ================================
// ACCESS GUARD HOOK
// ================================

/**
 * Access guard hook that redirects users if they don't have access to a project
 */
export function useAccessGuard(
  projectId: string,
  options: {
    redirectTo?: string;
    onAccessDenied?: (reason: AccessDeniedReason) => void;
    enableRedirect?: boolean;
  } = {},
) {
  const {
    redirectTo = '/billing/payment-required',
    onAccessDenied,
    enableRedirect = true,
  } = options;

  const router = useRouter();
  const {
    data: accessState,
    isLoading,
    error,
  } = useCheckProjectAccess(projectId);

  const handleAccessDenied = useCallback(
    (reason: AccessDeniedReason) => {
      if (onAccessDenied) {
        onAccessDenied(reason);
        return;
      }

      if (enableRedirect) {
        const params = new URLSearchParams({
          projectId,
          reason,
          returnTo: window.location.pathname,
        });
        router.push(`${redirectTo}?${params.toString()}`);
      }
    },
    [onAccessDenied, enableRedirect, redirectTo, projectId, router],
  );

  useEffect(() => {
    if (
      !isLoading &&
      accessState &&
      !accessState.hasAccess &&
      accessState.reason
    ) {
      handleAccessDenied(accessState.reason);
    }
  }, [isLoading, accessState, handleAccessDenied]);

  return {
    hasAccess: accessState?.hasAccess ?? false,
    isLoading,
    error,
    accessState,
    checkAccess: () => accessState,
  };
}

// ================================
// CACHE MANAGEMENT
// ================================

/**
 * Hook to invalidate project access cache
 */
export function useInvalidateProjectAccess() {
  const queryClient = useQueryClient();

  return useCallback(
    (projectId?: string, _contractorId?: string, _userId?: string) => {
      if (projectId) {
        // Invalidate specific project access
        queryClient.invalidateQueries({
          queryKey: ['project-access', projectId],
        });
        queryClient.invalidateQueries({
          queryKey: ['project-subscription-status', projectId],
        });
      } else {
        // Invalidate all project access queries
        queryClient.invalidateQueries({
          queryKey: ['project-access'],
        });
        queryClient.invalidateQueries({
          queryKey: ['project-subscription-status'],
        });
      }

      // Also invalidate projects list as access changes might affect visibility
      queryClient.invalidateQueries({
        queryKey: ['projects'],
      });
    },
    [queryClient],
  );
}

// ================================
// UTILITY HOOKS
// ================================

/**
 * Check if current user has billing access to multiple projects
 */
export function useMultipleProjectsAccess(projectIds: string[]) {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['multiple-projects-access', projectIds, user?.id],
    queryFn: async (): Promise<Record<string, AccessState>> => {
      if (!user || projectIds.length === 0) return {};

      // Use API route instead of direct service call
      const response = await fetch('/api/billing/access/validate?type=bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          validations: projectIds.map((projectId) => ({
            id: projectId,
            userId: user.id,
            projectId,
            userRole: user.profile?.user_role || 'viewer',
            requireActiveSubscription: true,
          })),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to check multiple project access');
      }

      const apiResult = await response.json();
      const bulkResult = apiResult.data;

      const results: Record<string, AccessState> = {};

      // Map service results to hook interface
      const mapReason = (
        reason: ProjectAccessResult['reason'],
      ): AccessDeniedReason | null => {
        switch (reason) {
          case 'no_subscription':
            return 'no_subscription';
          case 'suspended':
            return 'subscription_suspended';
          case 'expired':
            return 'grace_period_expired';
          default:
            return null;
        }
      };

      // Process API results (different format than direct service call)
      bulkResult.results.forEach(
        (result: {
          id: string;
          success: boolean;
          data?: {
            accessDetails: {
              hasAccess: boolean;
              subscription?: PmaSubscriptionWithAccess;
              reason: ProjectAccessResult['reason'];
              gracePeriodDays?: number;
            };
          };
          error?: string;
        }) => {
          if (result.success && result.data) {
            const accessDetails = result.data.accessDetails;
            results[result.id] = {
              hasAccess: accessDetails.hasAccess,
              subscription: accessDetails.subscription || null,
              reason: mapReason(accessDetails.reason),
              gracePeriodDaysRemaining: accessDetails.gracePeriodDays || null,
              isInGracePeriod:
                accessDetails.subscription?.isInGracePeriod || false,
            };
          } else {
            // Handle failed validation
            results[result.id] = {
              hasAccess: false,
              subscription: null,
              reason: 'project_not_found',
              gracePeriodDaysRemaining: null,
              isInGracePeriod: false,
            };
          }
        },
      );

      return results;
    },
    enabled: !!user && projectIds.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}
