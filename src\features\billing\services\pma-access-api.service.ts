/**
 * Client-side API service for PMA access operations
 * This handles HTTP communication with the PMA access API endpoints
 */

interface PmaAccessValidationRequest {
  mode: 'single' | 'bulk' | 'middleware';
  pmaId?: string;
  pmaIds?: string[];
  path?: string;
  requireActiveSubscription?: boolean;
}

interface PmaAccessResponse {
  userId: string;
  userRole: string;
  pmaId: string;
  access: import('./pma-access-control.service').PmaAccessResult;
}

interface ProjectPmaAccessResponse {
  userId: string;
  userRole: string;
  projectId: string;
  projectAccess: import('./pma-access-control.service').ProjectPmaAccess;
}

interface UserPmasResponse {
  userId: string;
  userRole: string;
  accessiblePmas: string[];
  totalAccessiblePmas: number;
}

/**
 * Check PMA access for a specific PMA certificate
 */
export async function checkPmaAccess(
  pmaId: string,
): Promise<PmaAccessResponse> {
  const response = await fetch(
    `/api/billing/pma-access/validate?pmaId=${pmaId}`,
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to check PMA access');
  }

  return response.json();
}

/**
 * Check PMA access for all PMAs in a project
 */
export async function checkProjectPmaAccess(
  projectId: string,
): Promise<ProjectPmaAccessResponse> {
  const response = await fetch(
    `/api/billing/pma-access/validate?projectId=${projectId}`,
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to check project PMA access');
  }

  return response.json();
}

/**
 * Get user's accessible PMAs
 */
export async function getUserAccessiblePmas(): Promise<UserPmasResponse> {
  const response = await fetch('/api/billing/pma-access/validate');

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to get user PMAs');
  }

  return response.json();
}

/**
 * Validate PMA access with various modes
 */
export async function validatePmaAccess(request: PmaAccessValidationRequest) {
  const response = await fetch('/api/billing/pma-access/validate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to validate access');
  }

  return response.json();
}

/**
 * Validate bulk PMA access
 */
export async function validateBulkPmaAccess(pmaIds: string[]) {
  return validatePmaAccess({
    mode: 'bulk',
    pmaIds,
    requireActiveSubscription: true,
  });
}

/**
 * Validate middleware PMA access
 */
export async function validateMiddlewarePmaAccess(pmaId: string, path: string) {
  return validatePmaAccess({
    mode: 'middleware',
    pmaId,
    path,
  });
}
