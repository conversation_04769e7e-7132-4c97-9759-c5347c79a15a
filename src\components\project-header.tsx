'use client';

import { clearProjectIdFromCookies } from '@/lib/middleware-utils';
import { useProjectContext } from '@/providers/project-context';
import { useQueryClient } from '@tanstack/react-query';
import { ChevronRight, Home } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

interface ProjectHeaderProps {
  projectName?: string;
  module: string;
  isLoading?: boolean;
}

export function ProjectHeader({
  projectName,
  module,
  isLoading,
}: ProjectHeaderProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { clearProject, setSelectedProject } = useProjectContext();

  // Handle clearing project when clicking "Projects"
  const handleBackToProjects = useCallback(() => {
    try {
      // Immediately clear cookies to prevent refetch
      clearProjectIdFromCookies();
      // Set a flag to indicate user intentionally cleared project
      sessionStorage.setItem('user_cleared_project', 'true');
      // Immediately update query data to trigger re-render
      queryClient.setQueryData(['selectedProject'], null);
      // Clear project context
      clearProject();
      setSelectedProject(null);
      // Navigate after clearing context
      router.replace('/projects');
    } catch (error) {
      console.error('Failed to navigate back to projects:', error);
    }
  }, [clearProject, setSelectedProject, router, queryClient]);

  return (
    <header className="w-full">
      {/* Minimalist Navigation Bar */}
      <div className="border-b border-gray-200 bg-white">
        <div className="w-full max-w-full px-3 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex items-center justify-between min-w-0">
            {/* Simple Breadcrumbs */}
            <nav
              className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm min-w-0 flex-1 overflow-hidden"
              aria-label="Breadcrumb"
            >
              <Link
                href="/dashboard"
                className="text-gray-500 hover:text-gray-700 flex-shrink-0"
              >
                <Home className="h-3 w-3 sm:h-4 sm:w-4" />
              </Link>
              <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-300 flex-shrink-0" />
              <button
                onClick={handleBackToProjects}
                className="text-gray-600 hover:text-gray-900 flex-shrink-0"
              >
                Projects
              </button>
              {(projectName || isLoading) && (
                <>
                  <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-300 flex-shrink-0" />
                  <span className="text-gray-600 max-w-[120px] sm:max-w-[200px] truncate">
                    {isLoading ? (
                      <span className="animate-pulse bg-gray-300 h-4 w-16 rounded inline-block"></span>
                    ) : (
                      projectName
                    )}
                  </span>
                </>
              )}
              <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-300 flex-shrink-0" />
              <span className="text-gray-900 font-medium truncate">
                {module}
              </span>
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
}
