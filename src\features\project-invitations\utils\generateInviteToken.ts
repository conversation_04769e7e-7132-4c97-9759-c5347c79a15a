/**
 * Generate a secure token for project invitations
 * Uses crypto.randomUUID() for security
 */
export function generateInviteToken(): string {
  return crypto.randomUUID();
}

/**
 * Generate expiry date for invitations (7 days from now)
 */
export function generateInviteExpiryDate(): string {
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + 7); // 7 days from now
  return expiryDate.toISOString();
}
