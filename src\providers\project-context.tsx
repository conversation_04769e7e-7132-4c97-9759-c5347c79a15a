'use client';

import {
  clearProjectIdFromCookies,
  getProjectIdFromCookies,
  setProjectIdInCookies,
} from '@/lib/middleware-utils';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createContext,
  ReactNode,
  useContext,
  useMemo,
  useCallback,
} from 'react';
import { useProject } from '@/features/projects';

import type { Database } from '@/types/database';

type ProjectRow = Database['public']['Tables']['projects']['Row'];

interface ProjectContextValue {
  selectedProjectId: string | null;
  selectedProject: ProjectRow | null;
  setSelectedProject: (project: ProjectRow | null) => void;
  selectProject: (projectId: string) => void;
  clearProject: () => void;
  isInProjectContext: boolean;
  isInitialized: boolean;
  isProjectLoading: boolean;
}

const ProjectContext = createContext<ProjectContextValue | undefined>(
  undefined,
);

interface ProjectContextProviderProps {
  children: ReactNode;
}

export function ProjectContextProvider({
  children,
}: ProjectContextProviderProps) {
  const queryClient = useQueryClient();

  // Query to get selected project ID from cookies
  const { data: selectedProjectId, isSuccess: isInitialized } = useQuery({
    queryKey: ['selectedProject'],
    queryFn: () => getProjectIdFromCookies(),
    staleTime: Infinity,
    gcTime: Infinity,
  });

  // Auto-fetch project data when selectedProjectId is available
  const { data: selectedProject, isLoading: isProjectLoading } = useProject(
    selectedProjectId || '',
  );

  // Mutation to select a project
  const selectProjectMutation = useMutation({
    mutationFn: (projectId: string) => {
      setProjectIdInCookies(projectId);
      return Promise.resolve(projectId);
    },
    onSuccess: (projectId) => {
      queryClient.setQueryData(['selectedProject'], projectId);
    },
  });

  // Mutation to clear project
  const clearProjectMutation = useMutation({
    mutationFn: () => {
      clearProjectIdFromCookies();
      return Promise.resolve(null);
    },
    onSuccess: () => {
      queryClient.setQueryData(['selectedProject'], null);
    },
  });

  const selectProject = useCallback(
    (projectId: string) => {
      selectProjectMutation.mutate(projectId);
    },
    [selectProjectMutation],
  );

  const clearProject = useCallback(() => {
    clearProjectMutation.mutate();
  }, [clearProjectMutation]);

  // Helper function to set selected project manually (for backwards compatibility)
  const setSelectedProject = useCallback((_project: ProjectRow | null) => {
    // This is now a no-op since selectedProject is derived from the useProject hook
    // Keeping for backwards compatibility but it won't do anything
    console.warn(
      'setSelectedProject is deprecated. Use selectProject(projectId) instead.',
    );
  }, []);

  const isInProjectContext =
    selectedProjectId !== null && selectedProjectId !== undefined;

  // Memoize the context value to prevent infinite re-renders
  const contextValue = useMemo(
    () => ({
      selectedProjectId: selectedProjectId ?? null,
      selectedProject: selectedProject ?? null,
      setSelectedProject,
      selectProject,
      clearProject,
      isInProjectContext,
      isInitialized,
      isProjectLoading,
    }),
    [
      selectedProjectId,
      selectedProject,
      isInitialized,
      isProjectLoading,
      setSelectedProject,
      selectProject,
      clearProject,
      isInProjectContext,
    ],
  );

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error(
      'useProjectContext must be used within a ProjectContextProvider',
    );
  }
  return context;
}
