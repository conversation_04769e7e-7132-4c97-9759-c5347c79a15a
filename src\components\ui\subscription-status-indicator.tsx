'use client';

import { Badge } from '@/components/ui/badge';
import type { Database } from '@/types/database';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  CreditCard,
  Gift,
  XCircle,
} from 'lucide-react';

export interface SubscriptionStatusIndicatorProps {
  status: Database['public']['Enums']['subscription_status'] | null;
  hasAccess: boolean;
  gracePeriodEnds?: string | null;
  trialEnds?: string | null;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

export function SubscriptionStatusIndicator({
  status,
  hasAccess,
  gracePeriodEnds,
  trialEnds,
  size = 'md',
  showText = true,
}: SubscriptionStatusIndicatorProps) {
  if (!status) {
    return (
      <Badge variant="secondary" className="flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        {showText && <span>No Subscription</span>}
      </Badge>
    );
  }

  const getStatusConfig = () => {
    switch (status) {
      case 'active':
        return {
          variant: 'default' as const,
          icon: CheckCircle,
          text: hasAccess ? 'Active' : 'Active (No Access)',
          className: hasAccess
            ? 'bg-green-100 text-green-800 border-green-200'
            : 'bg-yellow-100 text-yellow-800 border-yellow-200',
        };
      case 'trial':
        return {
          variant: 'secondary' as const,
          icon: Gift,
          text: hasAccess ? 'Trial Period' : 'Trial Expired',
          className: hasAccess
            ? 'bg-purple-100 text-purple-800 border-purple-200'
            : 'bg-red-100 text-red-800 border-red-200',
        };
      case 'grace_period':
        return {
          variant: 'secondary' as const,
          icon: Clock,
          text: hasAccess ? 'Grace Period' : 'Grace Period Expired',
          className: hasAccess
            ? 'bg-blue-100 text-blue-800 border-blue-200'
            : 'bg-red-100 text-red-800 border-red-200',
        };
      case 'pending_payment':
        return {
          variant: 'secondary' as const,
          icon: CreditCard,
          text: 'Pending Payment',
          className: 'bg-orange-100 text-orange-800 border-orange-200',
        };
      case 'suspended':
        return {
          variant: 'destructive' as const,
          icon: XCircle,
          text: 'Suspended',
          className: 'bg-red-100 text-red-800 border-red-200',
        };
      case 'cancelled':
        return {
          variant: 'destructive' as const,
          icon: XCircle,
          text: 'Cancelled',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
        };
      default:
        return {
          variant: 'secondary' as const,
          icon: AlertCircle,
          text: 'Unknown',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
        };
    }
  };

  const config = getStatusConfig();
  // Icons removed per user feedback for cleaner UI
  const textSize =
    size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm';

  return (
    <div className="flex flex-col gap-1">
      <Badge
        variant={config.variant}
        className={`flex items-center gap-1 ${config.className} ${textSize}`}
      >
        {showText && <span>{config.text}</span>}
      </Badge>
      {status === 'grace_period' && gracePeriodEnds && hasAccess && (
        <span className="text-xs text-muted-foreground">
          Ends: {new Date(gracePeriodEnds).toLocaleDateString()}
        </span>
      )}
      {status === 'trial' && trialEnds && hasAccess && (
        <span className="text-xs text-muted-foreground">
          Trial ends: {new Date(trialEnds).toLocaleDateString()}
        </span>
      )}
    </div>
  );
}

export function SubscriptionAccessDenied({
  message,
  status,
  gracePeriodEnds,
  trialEnds,
}: {
  message: string;
  status: Database['public']['Enums']['subscription_status'] | null;
  gracePeriodEnds?: string | null;
  trialEnds?: string | null;
}) {
  return (
    <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
      <XCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
      <div className="flex-1">
        <h4 className="text-sm font-medium text-red-900 mb-1">
          Subscription Required
        </h4>
        <p className="text-sm text-red-700 mb-2">{message}</p>
        {status === 'grace_period' && gracePeriodEnds && (
          <p className="text-xs text-red-600">
            Grace period ends: {new Date(gracePeriodEnds).toLocaleDateString()}
          </p>
        )}
        {status === 'trial' && trialEnds && (
          <p className="text-xs text-red-600">
            Trial period ended: {new Date(trialEnds).toLocaleDateString()}
          </p>
        )}
        <SubscriptionStatusIndicator
          status={status}
          hasAccess={false}
          gracePeriodEnds={gracePeriodEnds}
          trialEnds={trialEnds}
          size="sm"
        />
      </div>
    </div>
  );
}
