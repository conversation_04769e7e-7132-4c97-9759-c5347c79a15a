// ================================
// CENTRAL TYPE EXPORTS
// SimPLE Project Type Index
// ================================

// Core database types
export type {
  Database,
  Enums,
  Tables,
  TablesInsert,
  TablesUpdate,
} from './database';

// Authentication & User Management
export type * from './auth';

// Role-Based Access Control & Subscription-Based Access Control
export type * from './access-control';
export type * from './rbac';

// Feature-specific types
export type * from './competent-person';

// Billing & Payment Gateway (BillPlz Integration)
export type * from './billing';

// ================================
// RE-EXPORTS FOR CONVENIENCE
// Common types that are used frequently across the app
// ================================

import type { Database } from './database';

// User and authentication types (commonly used)
export type UserRole = Database['public']['Enums']['user_role'];
export type User = Database['public']['Tables']['users']['Row'];
export type Project = Database['public']['Tables']['projects']['Row'];
export type ProjectUser = Database['public']['Tables']['project_users']['Row'];

// Billing types (commonly used)
export type SubscriptionStatus =
  Database['public']['Enums']['subscription_status'];
export type PaymentStatus = Database['public']['Enums']['payment_status'];

// Access control types (commonly used)
export { AccessState } from './access-control';
export type {
  ProjectAccessInfo,
  SubscriptionAccessRules,
} from './access-control';
