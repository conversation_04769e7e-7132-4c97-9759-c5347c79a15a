'use client';

import { useLogout } from '@/features/auth';
import { usePermissions } from '@/hooks/use-permissions';
import { useNavigationTranslations } from '@/hooks/use-translations';
import { Link, usePathname } from '@/i18n/navigation';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import {
  BarChart3,
  Calendar,
  CreditCard,
  Home,
  LogOut,
  MessageSquare,
  Shield,
  User,
  UserCheck,
  Users as UsersIcon,
} from 'lucide-react';
import { useCallback, useMemo } from 'react';

// Menu item interface
interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
  translationKey: string;
}

// Menu item constants (same as unified sidebar)
const PROJECT_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: Home,
    translationKey: 'dashboard',
  },
  {
    title: 'PMAs',
    url: '/pmas',
    icon: Shield,
    translationKey: 'pmas',
  },
  {
    title: 'Maintenance Logs',
    url: '/maintenance-logs',
    icon: Calendar,
    translationKey: 'maintenanceLogs',
  },
  {
    title: 'Complaints',
    url: '/complaints',
    icon: MessageSquare,
    translationKey: 'complaints',
  },
  {
    title: 'Members',
    url: '/members',
    icon: UsersIcon,
    translationKey: 'members',
  },
];

const GENERAL_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Projects',
    url: '/projects',
    icon: BarChart3,
    translationKey: 'projects',
  },
  {
    title: 'Profile',
    url: '/profile',
    icon: User,
    translationKey: 'profile',
  },
];

const CONTRACTOR_MENU_ITEMS: MenuItem[] = [
  ...GENERAL_MENU_ITEMS,
  {
    title: 'CP List',
    url: '/cp-list',
    icon: UserCheck,
    translationKey: 'cpList',
  },
  {
    title: 'Billing',
    url: '/billing',
    icon: CreditCard,
    translationKey: 'billing',
  },
];

const ADMIN_PROJECT_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: Home,
    translationKey: 'dashboard',
  },
  {
    title: 'PMAs',
    url: '/pmas',
    icon: Shield,
    translationKey: 'pmas',
  },
  {
    title: 'Maintenance Logs',
    url: '/maintenance-logs',
    icon: Calendar,
    translationKey: 'maintenanceLogs',
  },
  {
    title: 'Complaints',
    url: '/admincomplaint',
    icon: MessageSquare,
    translationKey: 'complaints',
  },
  {
    title: 'Members',
    url: '/members',
    icon: UsersIcon,
    translationKey: 'members',
  },
];

// Utility functions
const getCleanPathname = (pathname: string): string => {
  return pathname.replace(/^\/(en|ms)/, '') || '/';
};

const isRouteActive = (url: string, pathname: string): boolean => {
  const cleanPathname = getCleanPathname(pathname);

  if (url === '/projects') {
    return cleanPathname === '/projects' || cleanPathname === '/';
  }
  if (url === '/dashboard') {
    return cleanPathname === '/dashboard';
  }
  return cleanPathname.startsWith(url);
};

export function MobileBottomNav() {
  const pathname = usePathname();
  const { isLoading, userRole } = usePermissions();
  const { selectedProjectId, clearProject, setSelectedProject } =
    useProjectContext();
  const t = useNavigationTranslations();
  const logoutMutation = useLogout();

  // Memoized values
  const isInProjectContext = useMemo(
    () => Boolean(selectedProjectId),
    [selectedProjectId],
  );

  const currentMenuItems = useMemo(() => {
    if (isInProjectContext) {
      // Admin users get special menu items when in project context
      if (userRole === 'admin') {
        return ADMIN_PROJECT_MENU_ITEMS;
      }
      return PROJECT_MENU_ITEMS;
    }

    // Role-based menu items for general context
    switch (userRole) {
      case 'contractor':
        return CONTRACTOR_MENU_ITEMS;
      case 'admin':
      case 'viewer':
      default:
        return GENERAL_MENU_ITEMS;
    }
  }, [isInProjectContext, userRole]);

  // Get the first 3 items for main navigation, logout will be the 4th item
  const mainNavItems = currentMenuItems.slice(0, 3);

  // Memoized callback for logout
  const handleLogout = useCallback(async () => {
    try {
      clearProject();
      setSelectedProject(null);
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [clearProject, setSelectedProject, logoutMutation]);

  if (isLoading) {
    return (
      <nav className="fixed bottom-0 inset-x-0 z-50 bg-background border-t shadow-md">
        <div className="flex justify-between items-center h-16 px-4">
          {[...Array(4)].map((_, index) => (
            <div
              key={index}
              className="flex flex-col items-center justify-center space-y-1"
            >
              <div className="h-5 w-5 bg-muted animate-pulse rounded" />
              <div className="h-3 w-8 bg-muted animate-pulse rounded" />
            </div>
          ))}
        </div>
      </nav>
    );
  }

  return (
    <nav className="fixed bottom-0 inset-x-0 z-50 bg-background border-t shadow-md">
      <div className="flex justify-between items-center h-16 px-4">
        {mainNavItems.map((item) => {
          const isActive = isRouteActive(item.url, pathname);
          const IconComponent = item.icon;
          return (
            <Link
              key={item.translationKey}
              href={item.url}
              className={cn(
                'flex flex-col items-center justify-center space-y-0.5 text-xs flex-1 min-w-0 px-1',
                isActive
                  ? 'text-primary'
                  : 'text-muted-foreground hover:text-foreground',
              )}
            >
              <IconComponent className="h-4 w-4 flex-shrink-0" />
              <span className="text-center leading-tight">
                {t(item.translationKey)}
              </span>
            </Link>
          );
        })}

        {/* Logout button */}
        <button
          onClick={handleLogout}
          disabled={logoutMutation.isPending}
          className={cn(
            'flex flex-col items-center justify-center space-y-0.5 text-xs transition-colors flex-1 min-w-0 px-1',
            logoutMutation.isPending
              ? 'text-muted-foreground/50 cursor-not-allowed'
              : 'text-muted-foreground hover:text-destructive',
          )}
          aria-label={logoutMutation.isPending ? 'Signing Out...' : 'Sign Out'}
        >
          <LogOut className="h-4 w-4 flex-shrink-0" />
          <span className="text-center leading-tight">
            {logoutMutation.isPending ? 'Signing Out...' : 'Logout'}
          </span>
        </button>
      </div>
    </nav>
  );
}
