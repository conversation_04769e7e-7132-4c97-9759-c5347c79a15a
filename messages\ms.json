{"common": {"loading": "Memuatkan...", "save": "Simpan", "cancel": "<PERSON><PERSON>", "submit": "Hantar", "edit": "Sunting", "delete": "Padam", "confirm": "<PERSON><PERSON><PERSON>", "back": "Kembali", "next": "Seterusnya", "previous": "Sebelumnya", "search": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "yes": "Ya", "no": "Tidak", "notSet": "Tidak ditetapkan", "unknown": "Tidak diketahui", "firstTime": "<PERSON> pertama", "justNow": "<PERSON><PERSON> sahaja", "new": "<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON>", "export": "Eksport", "settings": "Tetapan", "projects": "Projek", "currentProject": "<PERSON><PERSON><PERSON>", "noRecordsFound": "Tiada rekod di<PERSON>ui", "noRecordsAvailable": "Tiada rekod tersedia", "tryAdjustingFilters": "Cuba laraskan penapis anda atau cipta rekod baharu", "filters": "Penap<PERSON>", "filterOptions": "<PERSON><PERSON><PERSON>", "clearAll": "Kosongkan Semua", "columns": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "showingEntries": "<PERSON><PERSON><PERSON><PERSON><PERSON> {count} da<PERSON><PERSON> {total} entri", "recordsFound": "{count} rekod di<PERSON>ui", "noRecords": "Tiada rekod tersedia"}, "components": {"removeMemberDialog": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON>h anda pasti mahu membuang ahli ini daripada projek?", "warning": "<PERSON>li ini akan kehilangan akses kepada projek tetapi boleh dipulihkan kemudian oleh admin.", "cancel": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON>", "successMessage": "{memberName} telah dibuang daripada projek", "errorMessage": "<PERSON><PERSON> memb<PERSON> ahli: {error}"}}, "navigation": {"navigation": "Na<PERSON><PERSON><PERSON>", "professionalEdition": "Portal Kontraktor", "adminPanel": "Portal Pentadbir", "portalTitle": {"contractor": "Portal Kontraktor", "admin": "Portal Pentadbir"}, "signingOut": "Log keluar...", "signOut": "<PERSON><PERSON>", "loadingMenu": "Memuatkan menu...", "languages": {"en": "English", "ms": "Bahasa Malaysia"}, "dashboard": "<PERSON><PERSON>", "project": "Projek", "projects": "Projek", "lifts": "Lif", "buildings": "Bangunan", "directory": "Direktori CP", "dailyLogs": "<PERSON><PERSON>", "pmas": "Pengurusan PMA", "complaints": "<PERSON><PERSON>", "contractors": "Kontraktor", "competentPersons": "Orang Kompeten", "complaintAdmin": "<PERSON><PERSON>", "clients": "<PERSON><PERSON><PERSON>", "blacklist": "<PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "users": "Pengguna", "profile": "Profil", "cpList": "Senarai CP", "settings": "Tetapan", "maintenanceLogs": "<PERSON><PERSON>", "members": "<PERSON><PERSON>", "billing": "Bil", "pmaManagement": "Pengurusan PMA", "login": "Log Ma<PERSON>k", "register": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON>", "descriptions": {"dashboard": "<PERSON><PERSON><PERSON>n k<PERSON>lu<PERSON>han aktiviti pengurusan lif", "projects": "<PERSON><PERSON> projek dan tugasan yang diberikan", "lifts": "Urus inventori dan status lif", "buildings": "<PERSON><PERSON> mak<PERSON> bang<PERSON>n", "contracts": "Pengurusan kontrak <PERSON>len<PERSON>an", "dailyLogs": "Log pemantauan dan penyelengg<PERSON>an harian", "pmas": "<PERSON><PERSON><PERSON><PERSON>", "complaints": "<PERSON><PERSON> dan isu pelanggan", "contractors": "<PERSON><PERSON> pendaftaran dan pensijilan kontraktor", "competentPersons": "<PERSON>rus individu kompeten dalam sistem", "userManagement": "<PERSON><PERSON> akaun pengguna dan keizinan", "clients": "<PERSON>rus maklumat klien", "blacklist": "Pen<PERSON><PERSON>an kontraktor yang disenarai hitam", "analytics": "Prestasi dan cerapan sistem", "reports": "Jana dan lihat laporan sistem", "users": "Pengurusan pengguna sistem", "profile": "<PERSON>rus tetapan profil anda", "billing": "<PERSON><PERSON> langganan dan pembayaran", "cpList": "<PERSON><PERSON> dan urus individu kompeten dalam syarikat anda", "settings": "Konfigurasi dan tetapan sistem"}}, "pmaManagement": {"title": "Tambah Butiran PMA", "subtitle": "Ma<PERSON><PERSON><PERSON> butiran bagi setiap tugasan PMA", "newLogEntry": "<PERSON><PERSON> Baharu", "searchPlaceholder": "Cari log PMA...", "searchFilterTitle": "Cari & Tapis", "searchFilterDescription": "Cari log PMA tertentu", "filter": "Penap<PERSON>", "export": "Eksport", "addMorePmaEntries": "Tambah lebih banyak entri PMA di atas", "form": {"pmaEntry": "Entri PMA #{number}", "remove": "<PERSON><PERSON>", "pmaInformation": "Maklumat PMA", "pmaInformationDescription": "Lengkapkan semua medan untuk menambah tugasan PMA baharu", "addedPmaEntries": "Entri PMA Ditambah", "addedPmaEntriesDescription": "Semak entri PMA anda sebelum dihantar", "customer": "Pelanggan", "customerPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama klien", "agency": "Agensi", "agencyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama agensi", "location": "<PERSON><PERSON>", "locationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> butiran lokasi", "pmaNumber": "No. PMA", "pmaNumberPlaceholder": "Masukkan nombor PMA", "competentPerson": "Orang Kompeten", "competentPersonPlaceholder": "<PERSON><PERSON>h orang kompeten", "noCompetentPersonsAvailable": "Tiada orang kompeten tersedia", "pmaExpiryDate": "<PERSON><PERSON><PERSON> PMA", "pmaExpiryDatePlaceholder": "<PERSON><PERSON>h tarikh tamat PMA", "lifLocation": "Lokasi LIF", "lifLocationPlaceholder": "Masukkan lokasi LIF terperinci (contoh: Bangunan A, Tingkat 3, Bank Lif 1)", "pmaCertificateFile": "<PERSON><PERSON> PMA", "pmaCertificateFileNote": "(PDF, JPEG, atau PNG - maksimum 10MB)", "type": "Jenama", "supervisor": "Penyelia", "supervisorPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama penyelia", "inspectionDate": "<PERSON><PERSON><PERSON>", "pdfUpload": "Sijil PMA (PDF)", "pdfUploadDescription": "Muat naik dokumen sijil PMA (format PDF, JPEG, atau PNG, maksimum 10MB)", "assignCp": "Tugaskan CP", "ready": "Sedia", "edit": "Sunting", "delete": "Padam"}, "validation": {"atLeastOnePmaRequired": "Sekurang-kurangnya satu PMA diperlukan.", "locationRequired": "<PERSON><PERSON>an", "pmaNumberRequired": "Nombor PMA diperlukan", "inspectionDateRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "competentPersonRequired": "Orang kompeten diperlukan", "pmaExpiryRequired": "Tarikh tamat PMA diperlukan"}, "actions": {"saveDraft": "Simpan Dr<PERSON>", "addAnotherPma": "Tambah PMA", "cancel": "<PERSON><PERSON>", "submitPma": "Tambah Entri PMA", "saveChanges": "<PERSON><PERSON><PERSON>"}, "statistics": {"activePmas": "PMA Aktif", "completed": "Se<PERSON><PERSON>", "expiringSoon": "<PERSON>kan <PERSON> Tempoh", "overdue": "Le<PERSON>"}, "activityLogs": {"title": "Log Aktiviti PMA", "subtitle": "<PERSON><PERSON> dan urus semua entri PMA dan status semasanya"}, "table": {"pmaNumber": "Nombor PMA", "project": "Projek", "contractor": "Kontraktor", "createdDate": "<PERSON><PERSON><PERSON>", "expiryDate": "<PERSON><PERSON><PERSON>", "status": "Status", "actions": "<PERSON><PERSON><PERSON>", "totalRepairCost": "<PERSON><PERSON><PERSON>k <PERSON>uli<PERSON>", "totalRepairTime": "<PERSON><PERSON><PERSON>", "loading": "Memuatkan...", "empty": "Tiada log PMA ditemui", "noRecordsFound": "Tiada rekod di<PERSON>ui", "tryAdjustingFilters": "Cuba laraskan penapis anda atau tambah entri PMA baharu"}, "status": {"active": "Aktif", "expiring_soon": "<PERSON><PERSON>", "expired": "Tamat Tempoh", "completed": "Se<PERSON><PERSON>"}, "pagination": {"showing": "Memaparkan {count} daripada {total} entri", "previous": "Sebelumnya", "next": "Seterusnya"}, "detail": {"title": "Butiran Si<PERSON>l PMA", "dateCreated": "<PERSON><PERSON><PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON>", "downloadCertificate": "<PERSON><PERSON>", "viewCertificate": "<PERSON><PERSON>", "noCertificateUploaded": "<PERSON><PERSON><PERSON> sijil dimuat naik", "cannotPreviewFile": "Tidak dapat mempratonton jenis fail ini", "pdfPreviewUnavailable": "Pratonton PDF tidak tersedia"}, "filters": {"title": "<PERSON><PERSON>", "searchPlaceholder": "Cari mengikut nombor PMA, lokasi, atau negeri...", "optionsTitle": "<PERSON><PERSON><PERSON>", "clearAll": "Kosongkan Semua"}, "pages": {"add": {"title": "Tambah Entri PMA Baharu", "description": "Tambah satu atau lebih entri PMA untuk projek anda.", "breadcrumb": "Tambah PMA Baharu", "cancel": "<PERSON><PERSON>"}, "edit": {"title": "Edit Entri PMA", "description": "Kemaskini tarikh tamat untuk entri PMA ini.", "breadcrumb": "Edit PMA", "cancel": "<PERSON><PERSON>"}}}, "dashboard": {"title": "<PERSON><PERSON>", "welcomeBack": "<PERSON><PERSON><PERSON> kembali, {name}!", "welcomeMessage": "Selamat datang, {name}!", "welcomeDescription": "<PERSON><PERSON><PERSON> adalah apa yang berlaku dengan projek anda hari ini.", "loading": "Memuatkan papan pemuka...", "loadingProject": "Memuatkan papan pemuka projek...", "errorLoading": "<PERSON><PERSON> me<PERSON> profil: {error}", "profile": {"title": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "phone": "Telefon Bimbit", "memberSince": "<PERSON><PERSON>", "lastLogin": "Log Ma<PERSON>"}, "quickActions": {"title": "<PERSON><PERSON><PERSON>", "editProfile": "Sunting Profil", "settings": "Tetapan"}, "recentActivity": {"title": "Aktiviti Terkini", "noActivity": "Tiada aktiviti terkini untuk dipaparkan."}, "statistics": {"title": "Statistik Akaun", "loginStatus": "Status Log Masuk", "accountType": "<PERSON><PERSON>", "lastLogin": "Log Ma<PERSON>", "active": "Aktif", "standard": "Standard"}, "widgets": {"pma": {"title": "Sijil PMA", "viewAll": "<PERSON><PERSON>", "loading": "Memuatkan data sijil PMA...", "certificateStatus": "Status Sijil", "active": "Aktif", "expiringSoon": "<PERSON>kan <PERSON> Tempoh", "expired": "Tamat Tempoh", "noCertificateData": "Tiada data sijil tersedia", "noCertificateMessage": "<PERSON><PERSON>da sijil dalam sistem lagi.", "addCertificate": "Tambah Sijil", "total": "<PERSON><PERSON><PERSON>", "noProblematicLifts": "<PERSON><PERSON><PERSON>", "allLiftsNormal": "<PERSON><PERSON>a lif anda beroperasi dengan normal - kerja yang baik!", "mostProblematicLifts": "<PERSON><PERSON>"}, "maintenance": {"title": "<PERSON><PERSON>", "viewAll": "<PERSON><PERSON>", "loading": "Memuatkan data penyelenggaraan...", "recentLogs": "Log <PERSON>", "noLogs": "Tiada log penyelenggaraan dalam sistem lagi.", "completedToday": "<PERSON><PERSON><PERSON>", "pendingTasks": "Tugas Tertangguh", "noMaintenanceData": "Tiada Data Penyelenggaraan", "loadingStatus": "Memuatkan data status...", "maintenanceStatus": "Status Penyelenggaraan", "addMaintenanceLog": "Tambah Log Penyelenggaraan", "loadingRecent": "Memuatkan log penyelenggaraan terkini...", "recentMaintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getStartedMessage": "<PERSON><PERSON><PERSON> dengan mencipta log penyelenggaraan pertama anda.", "status": {"completed": "Se<PERSON><PERSON>", "pending": "Tertangguh", "overdue": "Tertunggak", "inProgress": "<PERSON><PERSON>"}, "total": "<PERSON><PERSON><PERSON>"}, "complaints": {"title": "<PERSON><PERSON>", "viewAll": "<PERSON><PERSON>", "loading": "Memuatkan data aduan...", "recentComplaints": "<PERSON><PERSON>", "noComplaints": "Tiada Data Aduan", "noComplaintsMessage": "Tiada aduan dalam sistem lagi.", "noComplaintsFound": "<PERSON><PERSON><PERSON>", "noComplaintsReported": "Tiada <PERSON>", "noComplaintsInPeriod": "Tiada aduan dalam tempoh masa yang dipilih. Ini mungkin petanda baik!", "openComplaints": "<PERSON><PERSON>", "resolvedComplaints": "<PERSON><PERSON>", "createComplaint": "<PERSON><PERSON><PERSON>", "statusDistribution": "Taburan Status Aduan", "loadingRecent": "Memuatkan aduan terkini...", "total": "<PERSON><PERSON><PERSON>", "getStartedMessage": "<PERSON><PERSON><PERSON> dengan mencipta aduan pertama anda.", "trendAnalysisTitle": "<PERSON><PERSON><PERSON>", "analytics": "ANALITIK", "status": {"new": "<PERSON><PERSON>", "inProgress": "<PERSON><PERSON>", "resolved": "Diselesaikan", "closed": "Ditutup"}, "trendAnalysis": "<PERSON><PERSON><PERSON>", "timeRange": {"6months": "6 <PERSON><PERSON><PERSON>", "12months": "12 <PERSON><PERSON><PERSON>"}, "chartDescription": "Jejak trend aduan dari semasa ke semasa untuk mengenal pasti corak dan meningkatkan masa respons.", "chartOverview": {"6months": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> jumlah aduan untuk 6 bulan terakhir (Jan-Jun 2024)", "12months": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han jumlah aduan sepanjang 12 bulan yang lalu (Jan 2023-Dec 2024)"}, "months": {"January": "<PERSON><PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON>", "March": "<PERSON>", "April": "April", "May": "<PERSON>", "June": "Jun", "July": "Jul<PERSON>", "August": "Ogos", "September": "September", "October": "Oktober", "November": "November", "December": "Disember"}, "addLog": "Tambah Log Penyelenggaraan", "noData": "Tiada data", "noChange": "<PERSON><PERSON><PERSON>", "thisPeriod": "tempoh ini"}, "stats": {"expiringSoon": "<PERSON>kan <PERSON> Tempoh", "expiringSoonDescription": "<PERSON><PERSON><PERSON> yang akan tamat tempoh dalam 30 hari akan datang", "dailyCompletion": "<PERSON><PERSON><PERSON><PERSON>", "dailyCompletionDescription": "Log penyelenggaraan yang selesai berbanding jumlah sijil", "openComplaints": "<PERSON><PERSON>", "openComplaintsDescription": "<PERSON><PERSON> yang me<PERSON>an per<PERSON>ian", "overdueTasksDescription": "Tugas penyelenggaraan yang diselesaikan selepas 5 petang"}}}, "pages": {"projects": {"title": "Projek", "description": "<PERSON><PERSON> projek dan tugasan anda", "noProjects": "Tiada Projek <PERSON>", "noProjectsDescription": "Anda belum mempunyai projek yang ditu<PERSON>.", "createFirst": "Cipta Projek Pertama Anda", "stats": {"total": "<PERSON><PERSON><PERSON>"}, "create": {"title": "Cipta Projek Baharu", "description": "<PERSON><PERSON><PERSON> butiran di bawah untuk mencipta projek baharu", "successTitle": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> dicipta", "successDescription": "<PERSON><PERSON><PERSON> baharu anda telah dicipta dan sedia untuk digunakan", "errorTitle": "<PERSON><PERSON> mencipta projek", "errorGeneric": "Terdapat ralat semasa mencipta projek. Sila cuba lagi", "restrictions": {"adminTitle": "<PERSON><PERSON><PERSON>", "adminMessage": "Pentadbir tidak boleh mencipta projek baharu. Sebagai pentadbir, anda hanya boleh dijemput ke projek sedia ada oleh kontraktor.", "adminHelp": "<PERSON><PERSON> anda perlu mengaks<PERSON> projek, sila hubungi pemilik projek untuk menjemput anda.", "accessDeniedTitle": "<PERSON><PERSON><PERSON>", "accessDeniedMessage": "Anda tidak mempunyai kebenaran untuk mencipta projek.", "backToProjects": "Kembali ke Projek"}, "form": {"title": "Maklumat Projek", "description": "<PERSON><PERSON><PERSON><PERSON> maklumat asas tentang projek anda", "multiStepTitle": "Cipta Projek Baharu", "multiStepDescription": "Sediakan projek anda dengan semua butiran dan dokumentasi yang diperlukan", "requiredFieldsNote": "<PERSON><PERSON><PERSON> medan yang bertanda", "areRequired": "<PERSON><PERSON><PERSON>", "sections": {"projectDetails": "Butiran <PERSON>", "pmaInformation": "Maklumat PMA", "pmaDescription": "<PERSON><PERSON>n but<PERSON>n <PERSON> (PMA) untuk projek ini."}, "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama projek"}, "code": {"label": "Nombor Se<PERSON>", "placeholder": "cth., QUO-2025-001"}, "agency": {"label": "Agensi", "placeholder": "<PERSON><PERSON><PERSON> agensi atau taip untuk buat baharu", "searchPlaceholder": "Cari agensi...", "createNew": "<PERSON><PERSON>t agensi baharu", "emptyMessage": "Tiada agensi dijumpai"}, "state": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> negeri"}, "location": {"label": "<PERSON><PERSON>", "placeholder": "Masukkan lokasi projek"}, "personInCharge": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> pihak be<PERSON>", "noStateSelected": "<PERSON>la pilih negeri terlebih dahulu untuk melihat kakitangan JKR yang tersedia untuk wilayah tersebut.", "noPersonnelFound": "Tiada kakitangan JKR ditemui untuk negeri yang dipilih", "personnelFoundNote": "Menunjukkan kakitangan JKR untuk", "searchPlaceholder": "<PERSON>i mengikut nama, emel, atau peranan...", "emptyMessage": "Tiada kakitangan JKR ditemui."}, "startDate": {"label": "<PERSON><PERSON><PERSON>"}, "endDate": {"label": "<PERSON><PERSON><PERSON>"}, "status": {"label": "Status", "placeholder": "Pilih status projek", "options": {"pending": "Tertunda", "active": "Aktif", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>"}}, "description": {"label": "Penerangan", "placeholder": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>gan projek (pilihan)"}, "pmaNumber": {"label": "Nombor PMA", "placeholder": "Masukkan nombor PMA"}, "pmaExpiry": {"label": "<PERSON><PERSON><PERSON>"}, "lifLocation": {"label": "Lokasi LIF", "placeholder": "Masukkan lokasi LIF terperinci (cth., Bangunan A, Aras 3, Bank Lif 1)"}, "competentPerson": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>h orang layak", "noCompetentPersonsAvailable": "Tiada orang layak tersedia", "noCompetentPersonsNote": "Tiada orang layak ditemui. <PERSON>la tambah orang layak ke profil kontraktor anda terlebih dahulu."}, "pmaCertificateFile": {"label": "<PERSON><PERSON> PMA", "note": "(PDF, JPEG, atau PNG)", "removeFile": "Buang fail", "fileUploaded": "<PERSON><PERSON> berjaya dimuat naik"}}, "actions": {"create": "Cipta <PERSON>", "creating": "Mencipta...", "next": "Seterusnya", "previous": "Sebelumnya", "cancel": "<PERSON><PERSON>", "addAnotherPma": "Tambah PMA Lain", "remove": "<PERSON><PERSON>", "createProject": "Cipta <PERSON>", "creatingProject": "Mencipta Projek..."}, "pma": {"cardTitle": "PMA {number}", "remove": "<PERSON><PERSON>"}}, "validation": {"required": "Medan wajib tidak diisi", "requiredFields": "<PERSON>la isi semua medan wajib", "dateError": "Julat tarikh tidak sah", "endDateAfterStart": "<PERSON><PERSON>h tamat mesti selepas tarikh mula", "unsavedChanges": "Anda mempunyai perubahan yang belum disimpan. <PERSON><PERSON>h anda pasti mahu membatalkan?", "unsavedChangesMessage": "Anda mempunyai perubahan yang belum disimpan yang akan hilang jika anda mening<PERSON>kan halaman ini. <PERSON><PERSON>h anda pasti mahu teruskan?"}, "success": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> dicipta", "description": "<PERSON><PERSON><PERSON> baharu anda telah dicipta dan sedia untuk digunakan"}, "error": {"title": "<PERSON><PERSON> mencipta projek", "description": "Terdapat ralat semasa mencipta projek. Sila cuba lagi"}}, "invitations": {"title": "Jemputan Tertunda", "description": "<PERSON><PERSON><PERSON> dan balas jemputan projek.", "noInvitations": "<PERSON>a tiada jemputan projek yang tertunda.", "accept": "Terima", "decline": "<PERSON><PERSON>", "roleLabel": "<PERSON><PERSON><PERSON>", "projectCode": "<PERSON><PERSON>", "location": "<PERSON><PERSON>", "unknownProject": "Projek Tidak Diketahui"}}, "members": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> ahli pasukan projek dan peranan mereka", "addMember": "<PERSON><PERSON> Ahli", "teamMembers": "<PERSON><PERSON>", "noMembers": "<PERSON><PERSON><PERSON> ahli pasukan di<PERSON>ui", "noMembersDescription": "Projek ini belum mempunyai ahli pasukan.", "addFirstMember": "<PERSON><PERSON> ahli pasukan pertama anda", "addMembers": {"title": "Tambah Ahli Pasukan", "description": "Tambah pengguna sedia ada ke dalam projek ini. <PERSON><PERSON><PERSON> akan dimaklumkan dan boleh menerima atau menolak jemputan."}, "member": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "roles": {"technician": "Juruteknik", "competentPerson": "Orang Kompeten", "admin": "Pentadbir", "viewer": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"accepted": "Aktif", "invited": "Dijemput", "declined": "<PERSON><PERSON><PERSON>"}, "addAnother": "Tambah Ah<PERSON>", "cancel": "<PERSON><PERSON>", "sendInvitations": "<PERSON><PERSON>", "sending": "Menghantar...", "invitation": {"success": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>a <PERSON>", "error": "<PERSON><PERSON> j<PERSON>an"}, "modal": {"title": "<PERSON><PERSON> Ah<PERSON> Baharu", "description": "<PERSON><PERSON><PERSON> ahli baharu untuk menyertai projek ini", "addAnother": "<PERSON><PERSON> ahli lain", "removeMember": "<PERSON><PERSON>", "email": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> alamat emel ahli"}, "role": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>n", "options": {"technician": "Juruteknik"}}, "errors": {"alreadyAdded": "Pengguna ini sudah menjadi ahli projek", "userNotFound": "Pengguna dengan emel ini tidak wujud dalam sistem", "invalidEmail": "<PERSON><PERSON> masukkan alamat emel yang sah", "duplicateEmail": "Emel ini telah dimasukkan", "addingFailed": "<PERSON><PERSON> menambah ahli ini ke projek", "inviteFailed": "<PERSON><PERSON> men<PERSON> jemputan kepada {email}: {error}"}, "status": {"success": "<PERSON><PERSON><PERSON><PERSON>", "alreadyAdded": "<PERSON><PERSON> men<PERSON> ahli", "userNotFound": "Pengguna tidak ditemui", "invited": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>"}, "success": {"invited": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a di<PERSON> kepada {email}"}, "actions": {"cancel": "<PERSON><PERSON>", "add": "<PERSON><PERSON> Ahli", "adding": "Menambah..."}}}, "maintenanceLogs": {"page": {"errorTitle": "Sesuatu telah berlaku", "errorDescription": "<PERSON><PERSON> men<PERSON> ralat semasa memuatkan log penyelenggaraan anda. Sila cuba lagi.", "tryAgain": "Cuba Lagi", "dashboardTitle": "<PERSON><PERSON>", "dashboardDescription": "Pantau status peralatan dan aktiviti penyelengg<PERSON>an anda secara masa nyata", "recordsTitle": "<PERSON><PERSON><PERSON>", "recordsFound": "{count} rekod di<PERSON>ui", "noRecords": "Tiada rekod tersedia", "addRecord": "Tambah Rekod", "searchFilterTitle": "Cari & Tapis", "searchFilterDescription": "<PERSON>i rekod penyelen<PERSON>an tertentu", "tableTitle": "Jadual Data", "tableRecords": "{count} rekod <PERSON>", "noData": "Tiada data tersedia", "pageOf": "Halaman {current} daripada {total}", "showingResults": "Menunjukkan {from} hingga {to} daripada {total} hasil", "cards": {"fullyFunctional": {"title": "Berfungsi Sepenuhnya", "description": "Peralatan berfungsi dengan baik"}, "partiallyWorking": {"title": "Sebahagiannya Berfungsi", "description": "<PERSON><PERSON>"}, "broken": {"title": "Rosak/Masalah", "description": "<PERSON><PERSON> tin<PERSON>an segera"}}}, "title": "<PERSON><PERSON>", "description": {"default": "<PERSON><PERSON> dan urus semua aktiviti penyelenggaraan", "withProject": "Rekod <PERSON>an komprehensif untuk {projectName}"}, "addLog": "Tambah Log", "table": {"loading": "Memuatkan...", "empty": "Tiada log penyelenggaraan di<PERSON>ui", "columns": {"operation_log_type": "<PERSON><PERSON>", "status": "Status", "log_date": "<PERSON><PERSON><PERSON>", "contractor_name": "<PERSON><PERSON>", "person_in_charge_name": "Orang <PERSON>", "description": "Penerangan", "pma_number": "Sijil PMA", "actions": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON> o<PERSON>h"}}, "filters": {"type": {"placeholder": "<PERSON><PERSON><PERSON> jenis", "all": "<PERSON><PERSON><PERSON>"}, "status": {"placeholder": "Pilih status", "all": "Semua Status"}, "dateRange": {"placeholder": "<PERSON><PERSON><PERSON> julat tarikh"}, "search": {"placeholder": "Cari log penyelenggaraan", "placeholderDetailed": "<PERSON>i mengi<PERSON>t pen<PERSON>gan, orang bertang<PERSON>, telefon, kontraktor, jenis, status, atau nombor PMA...", "placeholderEnhanced": "<PERSON>i mengikut penerangan, orang, kontraktor, jenis, status...", "placeholderCompact": "<PERSON>i mengikut penerangan, kontraktor, orang bertang<PERSON>wab..."}}, "cards": {"error": "Gagal memuat statistik penyelenggaraan", "dailyLogs": {"title": "<PERSON><PERSON>", "description": "Aktiviti penyelenggaraan harian"}, "secondSchedule": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>"}, "mantrap": {"title": "Mantrap", "description": "Aktiviti mantrap kritikal"}, "totalLogs": {"title": "<PERSON><PERSON><PERSON>", "description": "{count} bulan ini", "trend": "{value} berbanding bulan lepas"}, "recentActivity": {"title": "Aktiviti Terkini", "description": "7 hari terakhir"}, "thisMonth": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> bulan se<PERSON>a"}, "previousMonth": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> bulan lepas"}, "operationTypeDistribution": "<PERSON><PERSON><PERSON>", "weekTrend": "Trend Aktiviti 7-<PERSON>", "logs": "log"}, "status": {"fully function": "Berfungsi Sepenuhnya", "broken": "<PERSON><PERSON>"}, "operationType": {"daily logs": "<PERSON><PERSON>", "second schedule": "<PERSON><PERSON><PERSON>", "mantrap": "Mantrap"}, "pagination": {"pageOf": "Halaman {current} daripada {total}", "prev": "Ke halaman sebelumnya", "next": "<PERSON> halaman set<PERSON>"}, "create": {"title": "Cipta Log Penyelenggaraan", "description": "Rekod aktiviti penyelenggaraan baharu untuk projek anda.", "breadcrumb": "Cipta Log Baharu", "loading": "<PERSON><PERSON><PERSON> butiran projek...", "cancel": "<PERSON><PERSON>", "form": {"logDate": "<PERSON><PERSON><PERSON>", "operationType": "<PERSON><PERSON>", "status": "Status", "pmaCertificate": "Nombor Sijil PMA", "description": "Penerangan", "selectOperationType": "<PERSON><PERSON><PERSON> jeni<PERSON>i", "selectStatus": "Pilih status", "selectPmaCertificate": "Pilih nombor sijil PMA", "descriptionPlaceholder": "Masukkan butiran log penyelenggaraan...", "descriptionCharacterCount": "{current}/{max} aksara", "descriptionMinLength": "Penerangan mestilah sekurang-kurangnya {min} aksara", "descriptionNearLimit": "<PERSON><PERSON> {remaining} aksara yang tinggal", "submit": "Cipta Log Penyelenggaraan", "submitting": "Sedang mencipta...", "noPmaCertificates": "Tiada sijil PMA tersedia"}, "errors": {"title": "<PERSON><PERSON>", "failedToCreate": "Gagal mencipta log penyelenggaraan. Sila cuba lagi.", "failedToLoadPma": "Gagal memuat sijil PMA", "failedToLoadProject": "<PERSON><PERSON> memuat butiran projek", "projectNotFound": "Projek tidak dijumpai atau ralat berlaku."}}, "detail": {"title": "<PERSON>iran <PERSON>", "close": "<PERSON><PERSON><PERSON>", "noPersonInCharge": "Tiada maklumat orang bertang<PERSON>jawab tersedia"}}}, "auth": {"loginTitle": "Log Masuk ke Akaun Anda", "loginSubtitle": "<PERSON><PERSON><PERSON><PERSON> emel anda di bawah untuk log masuk ke akaun", "registerTitle": "<PERSON><PERSON><PERSON>", "registerSubtitle": "<PERSON><PERSON><PERSON><PERSON> mak<PERSON> anda di bawah untuk mencipta akaun", "email": "<PERSON><PERSON>", "password": "<PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>", "fullName": "<PERSON><PERSON>", "phoneNumber": "Telefon Bimbit", "role": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "fullNamePlaceholder": "<PERSON>", "phonePlaceholder": "+*********** atau **********", "forgotPassword": "Lupa kata laluan?", "selectRole": "<PERSON><PERSON><PERSON> peranan anda", "jkr": "JKR (Jabatan Kerja Raya)", "jkrPic": "JKR PIC (Pegawai Bertanggungjawab)", "jkrAdmin": "Pentadbir JKR", "admin": "Pentadbir", "contractor": "Kontraktor", "viewer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "login": "Log Ma<PERSON>k", "signUp": "<PERSON><PERSON><PERSON>", "signIn": "<PERSON><PERSON><PERSON>", "createAccount": "<PERSON><PERSON><PERSON>", "signingIn": "Sedang log masuk...", "creatingAccount": "Sedang mencipta akaun...", "noAccount": "<PERSON><PERSON>da akaun lagi?", "alreadyHaveAccount": "Sudah ada akaun?", "phoneTooltip1": "Hanya nombor telefon Malaysia", "phoneTooltip2": "cth: +***********, **********", "passwordRequirements": "<PERSON>a la<PERSON>an mesti mengandu<PERSON>i:", "passwordReq1": "Sekurang-kurangnya 8 aksara", "passwordReq2": "<PERSON><PERSON> (A-Z)", "passwordReq3": "<PERSON><PERSON> huruf kecil (a-z)", "passwordReq4": "Satu <PERSON><PERSON> (0-9)", "passwordReq5": "<PERSON>tu aksara khas (@$!%*?&)", "loginSuccess": "Log masuk berjaya! Mengalihkan ke papan pemuka...", "loginFailed": "Log masuk gagal. Sila cuba lagi.", "registerSuccess": "Akaun berjaya dicipta! Sila semak emel anda untuk mengesahkan akaun.", "registerFailed": "<PERSON><PERSON> mencipta akaun. Sila cuba lagi.", "userExists": "<PERSON><PERSON>un dengan emel ini sudah wujud. Sila cuba log masuk.", "profileCreationError": "Pendaftaran gagal semasa cipta profil. Sila cuba semula atau hubungi sokongan.", "signupDisabled": "Pendaftaran akaun sedang dilumpuhkan. Sila hubungi sokongan.", "adminLoginTitle": "Log Masuk Portal Pentadbir", "adminLoginSubtitle": "<PERSON>ks<PERSON> papan pemuka pentadbir", "adminRegisterTitle": "Pendaftaran Pentadbir", "adminRegisterSubtitle": "<PERSON><PERSON><PERSON> akaun pentad<PERSON> anda", "adminLogin": "Log Masuk <PERSON>bir", "adminSignUp": "<PERSON><PERSON><PERSON>", "adminSignIn": "Log Masuk <PERSON>bir", "createAdminAccount": "<PERSON><PERSON><PERSON>", "creatingAdminAccount": "Sedang mencipta akaun pentadbir...", "noAdminAccount": "<PERSON><PERSON><PERSON> akaun pentadbir?", "alreadyHaveAdminAccount": "Sudah ada akaun pentadbir?", "adminLoginSuccess": "Log masuk pentadbir berjaya! Mengalihkan ke papan pemuka...", "adminRegisterSuccess": "Akaun pentadbir berjaya dicipta! Sila semak emel anda untuk mengesahkan akaun.", "adminRegisterSuccessCheckEmail": "Akaun pentadbir berjaya dicipta! Sila semak emel anda dan klik pautan pengesahan untuk mengaktifkan akaun.", "contractorLoginTitle": "Log Masuk Portal Kontraktor", "contractorLoginSubtitle": "<PERSON><PERSON><PERSON> papan pemuka kontraktor anda", "contractorRegisterTitle": "Pendaftaran Kontraktor", "contractorRegisterSubtitle": "Ser<PERSON> rang<PERSON>an kontraktor kami", "contractorLogin": "Log Masuk <PERSON>", "contractorSignUp": "<PERSON><PERSON>ar Kontraktor", "contractorSignIn": "Log Masuk <PERSON>", "createContractorAccount": "<PERSON><PERSON><PERSON>", "creatingContractorAccount": "Sedang mencipta akaun kontraktor...", "noContractorAccount": "<PERSON><PERSON><PERSON> akaun kontraktor?", "alreadyHaveContractorAccount": "Sudah ada akaun kontraktor?", "contractorLoginSuccess": "Log masuk kontraktor berjaya! Mengalihkan ke papan pemuka...", "contractorRegisterSuccess": "Akaun kontraktor berjaya dicipta! Sila semak emel anda untuk mengesahkan akaun.", "contractorRegisterSuccessCheckEmail": "Akaun kontraktor berjaya dicipta! Sila semak emel anda dan klik pautan pengesahan untuk mengaktifkan akaun.", "contractorNote": "Nota: <PERSON><PERSON><PERSON><PERSON> syarikat tambahan akan diperlukan semasa onboarding.", "contractorNoteSubtext": "<PERSON><PERSON> akan <PERSON><PERSON>n ma<PERSON> s<PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan keupayaan projek selepas pendaftaran.", "forgotPasswordTitle": "Tetapkan semula kata laluan", "forgotPasswordSubtitle": "<PERSON><PERSON><PERSON><PERSON> emel anda di bawah dan kami akan hantar pautan tetapan semula", "sendResetLink": "Hantar Pa<PERSON>", "sendingResetLink": "Sedang menghantar pautan tetapan semula...", "resetLinkSent": "<PERSON><PERSON> semak emel anda untuk pautan tetapan semula", "resetLinkFailed": "<PERSON>l menghantar pautan. Sila cuba lagi.", "backToLogin": "Ke<PERSON>li ke log masuk", "verifyCodeTitle": "<PERSON>sukka<PERSON> kod pengesahan", "verifyCodeSubtitle": "Masukkan kod 6-digit yang dihantar ke emel anda", "verificationCode": "<PERSON><PERSON>", "codePlaceholder": "Masukkan kod 6-digit", "verifyCode": "<PERSON><PERSON><PERSON>", "verifyingCode": "Mengesahkan kod...", "codeVerified": "Kod disahkan! Mengalihkan...", "codeVerificationFailed": "Kod tidak sah atau telah tamat tempoh. Sila cuba lagi.", "didNotReceiveCode": "Tidak menerima kod?", "resendCode": "<PERSON><PERSON> semula kod", "alreadyHaveCode": "Sudah ada kod pengesahan?", "resetPasswordTitle": "Tetapkan kata laluan baharu", "resetPasswordSubtitle": "<PERSON><PERSON>h kata laluan yang kuat untuk akaun anda", "newPassword": "<PERSON><PERSON>", "confirmNewPassword": "<PERSON><PERSON><PERSON>", "newPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kata laluan baharu anda", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON> kata laluan baharu anda", "updatePassword": "<PERSON><PERSON> kini <PERSON>", "updatingPassword": "Mengemas kini kata laluan...", "passwordUpdated": "Kata laluan berjaya dikemas kini! Sila log masuk dengan kata laluan baharu.", "passwordUpdateFailed": "Gagal mengemas kini kata laluan. Sila cuba lagi.", "checkYourEmail": "<PERSON><PERSON><PERSON> emel anda", "emailVerificationSent": "<PERSON><PERSON> telah menghantar pautan pengesahan ke emel anda. <PERSON>la semak dan klik pautan tersebut untuk mengaktifkan akaun.", "emailSentTo": "<PERSON><PERSON> ke:", "emailVerificationInstructions": "<PERSON><PERSON> pautan pengesahan dalam emel anda untuk melengkapkan pendaftaran. <PERSON><PERSON>n akan tamat dalam 24 jam.", "resendEmail": "<PERSON><PERSON> semula emel pen<PERSON>", "resendingEmail": "Menghan<PERSON> semula...", "emailResent": "<PERSON><PERSON> pengesahan berjaya di<PERSON>tar!", "emailResendFailed": "Gagal menghantar semula emel. Sila cuba lagi.", "emailRequired": "<PERSON><PERSON><PERSON> emel dip<PERSON><PERSON>an untuk menghantar semula penges<PERSON>.", "emailNotReceived": "Tidak menerima emel? Semak folder spam anda.", "checkSpamFolder": "Semak folder spam", "contactSupport": "Hubungi so<PERSON>"}, "contractor": {"onboarding": {"title": "Pendaftaran Kontraktor", "step1": {"title": "Maklumat Peribadi", "fullName": "<PERSON><PERSON>", "fullNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama penuh anda", "icNumber": "Nombor Kad Pengenalan", "icNumberPlaceholder": "123456-78-9012", "phoneNumber": "Telefon Bimbit", "phoneNumberPlaceholder": "01X-XXXXXXX", "role": "<PERSON><PERSON><PERSON>", "rolePlaceholder": "<PERSON><PERSON><PERSON> peranan anda", "roleHelp": "<PERSON><PERSON><PERSON> peranan anda dalam syarikat", "technician": "Juruteknik", "admin": "Pentadbir", "cp": "<PERSON><PERSON> (CP)", "nextButton": "Seterusnya: <PERSON><PERSON><PERSON><PERSON>"}, "step2": {"title": "<PERSON><PERSON><PERSON><PERSON> - {role}", "nextButton": "Seterusnya: <PERSON><PERSON><PERSON>", "cp": {"title": "Maklumat Orang Kompeten", "name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON>kkan nama orang kompeten", "category": "<PERSON><PERSON><PERSON>", "categoryPlaceholder": "<PERSON><PERSON><PERSON> ka<PERSON>gori <PERSON>", "categoryA": "Kategori A", "categoryB": "Kategori B", "categoryC": "Kategori C", "icNo": "Nombor IC", "icNoPlaceholder": "123456-78-9012", "cpNumber": "Nombor CP", "cpNumberPlaceholder": "Masukkan nombor pendaftaran CP", "tel": "Telefon Bimbit", "telPlaceholder": "01X-XXXXXXX", "email": "<PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "liftListFiles": "<PERSON><PERSON><PERSON>", "liftFilesPlateholder": "Muat naik fail senarai lif (PDF, DOC, DOCX, XLS, XLSX)", "noLiftFiles": "Tiada fail senarai lif dimuat naik lagi", "registrationCertFile": "<PERSON><PERSON>", "registrationCertFilePlaceholder": "<PERSON>at naik sijil pendaft<PERSON> (PDF, DOC, DOCX, JPG, PNG)", "registrationCertFileHelp": "Muat naik sijil pendaftaran CP anda (minimum 10MB)"}, "admin": {"title": "Maklumat Pentadbir", "name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama pentadbir", "nameHelp": "Akan digunakan untuk pengenalan pentadbiran"}, "technician": {"title": "Maklumat Juruteknik", "name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama juruteknik", "nameHelp": "Akan digunakan untuk pengenalan juruteknik"}}, "step3": {"title": "<PERSON><PERSON><PERSON>", "registrationType": "<PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> syarikat baharu yang belum didaftarkan dalam sistem", "joinTitle": "Sertai Syarikat Sedia Ada", "joinDescription": "Sertai syarikat yang telah berdaftar dalam sistem", "specialCode": "<PERSON><PERSON>", "specialCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> kod khas yang diberikan oleh syarikat", "specialCodeHelp": "<PERSON><PERSON><PERSON><PERSON> kod khas yang diberikan oleh pentadbir syarikat anda", "continueToCreation": "Teruskan", "joinCompanyButton": "Sertai <PERSON>ika<PERSON>"}, "step4": {"title": "<PERSON><PERSON><PERSON><PERSON>", "companyCode": "<PERSON><PERSON>", "companyCodeHelp": "Kod unik ini akan mengenal pasti syarikat anda dalam sistem", "companyName": "<PERSON><PERSON>", "companyNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama syarikat anda", "companyNameHelp": "<PERSON><PERSON> syar<PERSON>t akan ditukar ke huruf besar dan mestilah unik.", "companyNamePreview": "Pratonton: {name}", "companyType": "<PERSON><PERSON>", "companyTypeHelp": "<PERSON><PERSON><PERSON> jeni<PERSON> s<PERSON> anda", "companyHotline": "Hotline Syarikat", "companyHotlinePlaceholder": "Masukkan nombor hotline syarikat", "oemName": "<PERSON>a OEM", "oemNamePlaceholder": "Ma<PERSON>kkan nama OEM", "oemNameHelp": "<PERSON><PERSON><PERSON><PERSON> untuk syarikat jenis O<PERSON>", "appointedOemCompetentFirm": "Firma OEM/Kompeten yang Dilantik", "appointedOemCompetentFirmPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama OEM atau firma kompeten yang dilantik", "appointedOemCompetentFirmHelp": "Diperlukan untuk firma bukan kompeten - nyatakan OEM atau firma kompeten yang dilantik untuk perkhidmatan anda", "competentPersons": {"title": "Orang Kompeten", "subtitle": "Tambah orang kompeten untuk syarikat anda", "addButton": "Tambah Orang Kompeten", "removeButton": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama penuh", "icNo": "Nombor IC", "icNoPlaceholder": "123456-78-9012", "phoneNo": "Telefon Bimbit", "phoneNoPlaceholder": "+***********", "address": "<PERSON><PERSON><PERSON>", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> alamat penuh", "cpType": "<PERSON><PERSON>", "cpTypePlaceholder": "<PERSON><PERSON><PERSON>eni<PERSON>", "cpRegisterationNo": "Nombor Pendaftaran", "cpRegisterationNoPlaceholder": "Masukkan nombor pendaftaran", "cpRegisterationCert": "<PERSON><PERSON><PERSON>", "certExpDate": "<PERSON><PERSON><PERSON>", "noOfPma": "Bilangan PMA", "uploadCert": "<PERSON><PERSON>", "previewCert": "Klik untuk pratonton sijil", "types": {"CP1": "CP1 - Orang Kompeten Tahap 1", "CP2": "CP2 - Orang Kompeten Tahap 2", "CP3": "CP3 - Orang Kompeten Tahap 3"}}, "createButton": "<PERSON><PERSON><PERSON>", "creatingButton": "Sedang mencipta syarikat..."}, "progress": {"step": "Langka<PERSON> {current} daripada {total}", "personalInfo": "Maklumat Peribadi", "companyInfo": "Mak<PERSON>at Syarikat", "finalDetails": "Ma<PERSON><PERSON><PERSON>"}}}, "company": {"types": {"sdn_bhd": "<PERSON><PERSON><PERSON> (Sdn Bhd)", "bhd": "<PERSON><PERSON><PERSON> (Bhd)", "partnership": "<PERSON>kon<PERSON><PERSON>", "sole_proprietorship": "Pemilikan <PERSON>", "llp": "Perkongsian Liabiliti Terhad (LLP)", "competent_firm": "<PERSON>rma <PERSON>", "non_competent_firm": "Firma Buka<PERSON>", "oem": "OEM (Pengilang Peralatan Asal)"}, "availability": {"checking": "Sedang menyemak ketersediaan...", "available": "<PERSON><PERSON> s<PERSON>ikat tersedia", "unavailable": "<PERSON><PERSON> syarikat telah di<PERSON>bil", "error": "<PERSON><PERSON> se<PERSON>a <PERSON> k<PERSON>"}}, "agencies": {"JKR": "Jabatan Kerja Raya Malaysia (JKR)", "KKM": "Kementerian Kesihatan Malaysia (KKM)", "KPM": "Kementerian Pendidikan Malaysia (KPM)", "KPKT": "Kementerian Perumahan dan <PERSON>mpatan (KPKT)", "KKR": "Kementerian <PERSON> (KKdW)", "KPDNHEP": "Kementerian Perdagangan Dalam Nege<PERSON> dan <PERSON> (KPDNHEP)", "MOSTI": "Kementerian Sains, Teknologi <PERSON> (MOSTI)", "KPWKM": "Kementerian Pembangunan <PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (KPWKM)", "DBKL": "Dewan Bandaraya Kuala Lumpur (DBKL)", "MBPJ": "<PERSON><PERSON>aling Jaya (MBPJ)", "MBSJ": "<PERSON><PERSON>araya <PERSON>ang <PERSON> (MBSJ)", "MBSA": "<PERSON><PERSON> (MBSA)", "MPK": "<PERSON><PERSON> (MPK)", "MBIP": "<PERSON><PERSON> (MBIP)", "MPJB": "<PERSON><PERSON> (MPJBT)", "MBPP": "<PERSON><PERSON> (MBPP)", "MPSP": "<PERSON><PERSON> (MPSP)", "MBMB": "<PERSON><PERSON> (MBMB)", "MPAG": "<PERSON><PERSON> (MPAG)", "TNB": "Tenaga Nasional Berhad (TNB)", "SYABAS": "Syarikat Bekalan Air Selangor (SYABAS)", "IWK": "Indah Water Konsortium (IWK)", "PLUS": "PLUS Malaysia Berhad", "KTMB": "<PERSON><PERSON><PERSON> (KTMB)", "MRT": "Mass Rapid Transit Corporation (MRT Corp)", "LRT": "Rapid Rail Sdn Bhd", "HSB": "Hospital Selayang", "HKL": "Hospital Kuala Lumpur", "HUSM": "Hospital Universiti Sains Malaysia (HUSM)", "HUKM": "Hospital Universiti Kebangsaan Malaysia (HUKM)", "UM": "Universiti Malaya (UM)", "UKM": "Universiti Kebangsaan Malaysia (UKM)", "USM": "Universiti Sains Malaysia (USM)", "UTM": "Universiti Teknologi Malaysia (UTM)", "UPM": "Universiti Putra Malaysia (UPM)", "UiTM": "Universiti Teknologi MARA (UiTM)", "Other": "Lain-lain"}, "states": {"JH": "<PERSON><PERSON>", "KD": "Kedah", "KT": "<PERSON><PERSON><PERSON>", "ML": "<PERSON><PERSON>", "NS": "<PERSON><PERSON><PERSON>", "PH": "<PERSON><PERSON>", "PN": "<PERSON><PERSON><PERSON>", "PK": "<PERSON><PERSON>", "PL": "<PERSON><PERSON>", "SB": "Sabah", "SW": "Sarawak", "SL": "Selangor", "TR": "Terengganu", "WP": "W.P. Kuala Lumpur", "LBN": "<PERSON><PERSON><PERSON><PERSON>", "PW": "<PERSON><PERSON><PERSON><PERSON>", "OTH": "Lain-lain"}, "validation": {"required": "Medan ini wajib diisi", "email": "<PERSON><PERSON> masukkan alamat emel yang sah", "emailNoSubaddressing": "<PERSON><PERSON><PERSON> emel dengan simbol '+' tidak di<PERSON>an", "password": "Kata laluan mesti sekurang-kurangnya 8 aksara", "passwordMin": "Kata laluan mesti sekurang-kurangnya 8 aksara", "passwordLowercase": "<PERSON>a laluan mesti mengandungi sekurang-kura<PERSON><PERSON> satu huruf kecil", "passwordUppercase": "<PERSON>a laluan mesti mengandungi sekurang-kura<PERSON><PERSON> satu huruf besar", "passwordNumber": "<PERSON>a laluan mesti mengandungi sekurang-kura<PERSON>nya satu nombor", "passwordSpecial": "<PERSON>a laluan mesti mengandungi sekurang-kurangnya satu aksara khas (@$!%*?&)", "passwordMatch": "<PERSON>a la<PERSON>an tidak sepadan", "nameMin": "Nama mesti sekurang-kurangnya 2 aksara", "phoneFormat": "Sila masukkan nombor telefon Malaysia yang sah", "roleRequired": "<PERSON>la pilih peranan", "icNumber": "Sila masukkan nombor kad pengenalan yang sah", "emailPlusNotAllowed": "<PERSON><PERSON><PERSON> emel den<PERSON> '+' tidak dibenarkan untuk akaun admin dan kontraktor", "emailPlusWarning": "<PERSON>la gunakan alamat emel standard tanpa aksara '+'"}, "errors": {"somethingWrong": "<PERSON> sesuatu yang tidak kena", "tryAgain": "Sila cuba lagi", "networkError": "<PERSON><PERSON>. <PERSON><PERSON> semak sambungan anda.", "unauthorized": "Anda tidak dibenarkan untuk melakukan tindakan ini", "notFound": "Sumber yang diminta tidak dijumpai"}, "invitation": {"loading": "Memuatkan jemputan...", "title": "Anda <PERSON>!", "description": "{inviterName} telah menje<PERSON> anda untuk menyertai projek", "someone": "Seseorang", "projectDetails": {"title": "Maklumat Projek", "project": "Projek", "role": "<PERSON><PERSON><PERSON>", "invitedBy": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>h", "unknownProject": "Projek Tidak Dikenali", "member": "<PERSON><PERSON>", "unknown": "Tidak diketahui"}, "nextStep": {"title": "<PERSON><PERSON><PERSON>", "description": "Anda akan diminta untuk mencipta kata laluan bagi menyediakan akaun SimPLE anda."}, "button": {"accepting": "Meneri<PERSON>...", "accept": "<PERSON><PERSON>", "acceptAndSetup": "Terima & Sediakan <PERSON>un"}, "disclaimer": "<PERSON>gan menerima jem<PERSON>an ini, anda bersetuju untuk menyertai projek ini dan bekerjasama dengan ahli pasukan.", "invalid": {"title": "Jemputan Tidak Sah", "description": "Pautan jemputan ini tidak sah, telah tamat tempoh, atau telah digunakan. Sila hubungi orang yang menjemput anda untuk jemputan baharu."}, "error": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> berlaku semasa memu<PERSON> jemputan. <PERSON>la cuba lagi atau hubungi sokongan."}, "accepted": {"title": "Jemputan <PERSON>!", "description": "<PERSON>a telah berjaya <PERSON> {projectName}. <PERSON><PERSON>ih ke papan pemuka projek...", "unknownProject": "projek"}, "redirecting": "Mengalih ke projek...", "accept": {"title": "Jemputan <PERSON>", "description": "Anda telah dijemput untuk menyertai projek", "success": "<PERSON><PERSON><PERSON><PERSON> {projectName}!", "error": "<PERSON><PERSON> j<PERSON>: {error}", "expired": "Jemputan ini telah tamat tempoh atau tidak sah", "loading": "Memproses jemputan...", "acceptButton": "<PERSON><PERSON>", "loginRequired": "Sila log masuk untuk menerima jemputan ini"}}, "error": {"title": "<PERSON> sesuatu yang tidak kena", "description": "<PERSON><PERSON> men<PERSON> ralat yang tidak dijangka. Sila cuba lagi atau kembali ke halaman sebelumnya.", "retryButton": "Cuba semula", "goBackButton": "Kembali"}, "notFound": {"title": "404", "description": "Halaman tidak dijumpai", "backButton": "<PERSON><PERSON><PERSON> ke <PERSON>"}, "authPortal": {"title": "Selamat Datang ke SimPLE", "subtitle": "<PERSON><PERSON><PERSON> peranan anda untuk mengakses portal yang sesuai", "adminPortalTitle": "Portal Pentadbir", "adminPortalDescription": "Untuk pemilik hartanah, pengurus fasiliti dan pentadbir sistem", "contractorPortalTitle": "Portal Kontraktor", "contractorPortalDescription": "Untuk kontraktor dan penyedia perkhidmatan yang berdaftar", "adminLogin": "Log Masuk <PERSON>", "adminRegister": "<PERSON><PERSON><PERSON>", "contractorLogin": "Log Masuk <PERSON>", "contractorRegister": "<PERSON><PERSON>ar Kontraktor", "helpText": "Perlukan bantuan? Hubungi sokongan untuk bantuan.", "heroTitle": "Platform Profesional", "heroSubtitle": "Menghubungkan pentadbir dan kontraktor dengan lancar", "scrollToLearn": "Skrol untuk ketahui lebih lan<PERSON>t", "adminFeatures": {"projectManagement": "Pengurusan & Pemantauan Projek", "userManagement": "Pengurusan Pengguna & Kawalan Akses", "reports": "Laporan & Papan Pemuka <PERSON>"}, "contractorFeatures": {"bidding": "Log Harian – Penyelenggaraan & Aduan", "tracking": "Penjejakan Kemajuan & Pelaporan", "compliance": "Pengurusan Dokumen & Pematuhan"}, "aboutSimple": {"title": "Apa itu SimPLE?", "subtitle": "<PERSON><PERSON>. <PERSON><PERSON><PERSON>.", "description": "SimPLE ialah platform berpusat untuk pemantauan lif pintar dan pengurusan penyelenggaraan, direka untuk memastikan keselamatan, prestasi, dan masa <PERSON>i yang optimum. Dibangunkan untuk menyokong pemilik bangunan, pen<PERSON><PERSON> fasiliti, dan pen<PERSON><PERSON> pen<PERSON>, SimPLE menyediakan:", "features": {"digital": {"title": "Kebolehlihatan masa nyata ke dalam prestasi sistem lif", "description": "Pantau operasi lif dan metrik prestasi serta-merta."}, "compliance": {"title": "Log penyelenggaraan berpusat", "description": "Simpan rekod bersatu semua tugasan penyelenggaraan untuk audit mudah."}, "efficiency": {"title": "<PERSON><PERSON><PERSON><PERSON> dan penye<PERSON>aian isu yang efisien", "description": "Lapor dan selesaikan isu lif dengan cepat melalui aliran kerja yang cekap."}, "transparency": {"title": "Pandangan boleh tindakan melalui analitik prestasi", "description": "Dapatkan kejelasan melalui laporan dan analitik berasaskan data."}}, "descrt": "<PERSON>a ada untuk hartanah ked<PERSON>, komersial atau institusi, SimPLE membolehkan anda mengawal sepenuhnya operasi lif — men<PERSON><PERSON><PERSON> risiko, <PERSON><PERSON><PERSON> kos dan meningkatkan kualiti perkhidmatan."}, "nav": {"home": "<PERSON><PERSON>", "about": "Tentang", "whatIsSimple": "Apa itu SimPLE?", "contact": "Hubung<PERSON>"}, "services": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Penyelesaian pengurusan lif yang menyeluruh untuk bangunan moden", "liftManagement": {"title": "Pen<PERSON><PERSON><PERSON> Aset Lif", "description": "Pengurusan digital sepenuhnya sistem lif, dari pemasangan ke penyelenggaraan dan penamatan perkhidmatan.", "features": {"maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inspection": "<PERSON><PERSON><PERSON><PERSON>", "compliance": "Buku Log Digital & Pemantauan Pematuhan"}}, "safety": {"title": "Keselamatan & Pematuhan", "description": "Memastikan pematuhan kepada piawaian keselamatan dan peraturan", "features": {"standards": "Piawaian Keselamatan Antarabangsa", "certification": "Program Pensijilan Profesional", "reporting": "Dokumentasi Mitigasi Risiko"}}, "platform": {"title": "Platform Operasi Pintar", "description": "Teknologi canggih untuk operasi yang lancar", "features": {"realtime": "Pemantauan Sistem Masa Nyata & Pemberitahuan", "analytics": "Papan P<PERSON>lit<PERSON> Disesua<PERSON>", "integration": "Integrasi Lancar dengan Sistem Pihak <PERSON>"}}}, "blog": {"title": "Berita & Wawasan Terkini", "subtitle": "Kekal dikemas kini dengan trend industri dan amalan terbaik", "readMore": "Baca Lagi", "viewAll": "<PERSON><PERSON>", "post1": {"date": "15 Disember 2024", "title": "Amalan Terbaik dalam Penyelenggaraan Lif", "excerpt": "Ke<PERSON>ui strategi penyelenggaraan terkini yang memastikan prestasi dan keselamatan optimum."}, "post2": {"date": "10 Disember 2024", "title": "Kemaskini Peraturan Keselamatan 2024", "excerpt": "Kemaskini penting tentang peraturan keselamatan lif dan keperluan pematuhan untuk tahun ini."}, "post3": {"date": "5 Disember 2024", "title": "Transformasi Digital dalam <PERSON> Lif", "excerpt": "Bagaimana teknologi moden merevolusikan cara kita mengurus dan menyelenggara sistem lif."}}, "pricing": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON> pakej komp<PERSON><PERSON><PERSON><PERSON> yang meliputi semua k<PERSON><PERSON>an pengu<PERSON>an lif anda", "popularBadge": "Paling Popular", "package": {"name": "Platform Pengurusan Lif & Eskalator Digital", "description": "<PERSON><PERSON><PERSON> yang anda perlukan untuk pengurusan lif profesional", "period": "setiap lif / bulan", "billing": "<PERSON><PERSON> bulanan, batal bila-bila masa", "whatsIncluded": "<PERSON><PERSON> Di<PERSON>", "features": {"cloudBased": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> awan – aks<PERSON> selam<PERSON>, bila-bila masa/di mana sahaja", "unlimitedUsers": "Tempat duduk pengguna tanpa had – <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Orang Ko<PERSON>n, Pemerhati", "unlimitedStorage": "Penyimpanan dokumen tanpa had – PDF, imej dan jenis fail lain", "certificateTracking": "Penjejakan sijil PMA & Orang Kompeten – peringatan automatik", "digitalLogs": "Log aduan & penyelenggaraan digital – sejarah isu hujung ke hujung", "realtimeDashboard": "Papan pemuka masa nyata – pemantauan prestasi langsung dan amaran"}, "cta": "<PERSON><PERSON><PERSON>", "guarantee": "Jaminan wang dikembalikan 30 hari"}, "contactInfo": "Perlukan penye<PERSON>aian tersuai atau ada soalan tentang harga?", "contactUs": "<PERSON><PERSON><PERSON><PERSON>"}, "newsletter": {"title": "Kekal Berhubung", "subtitle": "Dapatkan kemas kini terkini, berita industri dan wawasan eksklusif terus ke peti masuk anda", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> alamat emel anda", "subscribe": "<PERSON><PERSON>", "privacy": "<PERSON><PERSON> privasi anda. <PERSON><PERSON><PERSON> n<PERSON>gan pada bila-bila masa."}}, "cpList": {"title": "Senarai Orang Kompeten", "description": "Urus orang kompeten dalam syarikat anda", "addCP": "Tambah OK", "addFirstCP": "Tambah OK Pertama", "totalCPs": "<PERSON><PERSON><PERSON>", "byType": "<PERSON><PERSON><PERSON><PERSON>", "expiredCerts": "Sijil Tamat Tempoh", "totalPMAs": "<PERSON><PERSON>lah PMA", "noCompetentPersons": {"title": "Tiada Orang Kompeten Ditemui", "description": "Anda belum menambah sebarang orang kompeten ke dalam syarikat anda. Tambah orang kompeten pertama untuk bermula."}, "accessRestricted": {"title": "<PERSON><PERSON><PERSON>", "description": "Ciri ini hanya tersedia untuk pengguna kontraktor."}, "errorLoading": {"title": "<PERSON><PERSON>", "description": "Gagal memuatkan orang kompeten"}, "addCPModal": {"title": "Tambah Orang Kompeten", "editTitle": "Edit Orang Kompeten", "submitButton": "Tambah OK", "updateButton": "Kemaskini OK", "cancelButton": "<PERSON><PERSON>", "success": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Orang kompeten berjaya ditambah"}, "updateSuccess": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>ang kompeten ber<PERSON><PERSON>"}, "error": {"title": "<PERSON><PERSON>", "description": "Gagal menambah orang kompeten"}, "updateError": {"title": "<PERSON><PERSON>", "description": "Gagal mengemaskini orang kompeten"}}, "fields": {"name": "<PERSON><PERSON>", "icNo": "No. <PERSON><PERSON>", "phone": "Telefon Bimbit", "address": "<PERSON><PERSON><PERSON>", "cpType": "<PERSON><PERSON>", "registrationNo": "No. <PERSON>", "registrationCert": "<PERSON><PERSON><PERSON>", "certExpiry": "<PERSON><PERSON><PERSON>", "pmas": "PMA", "contact": "Hubung<PERSON>", "viewCert": "<PERSON><PERSON>", "expired": "Tamat Tempoh", "valid": "Sah", "notSpecified": "Tidak dinyatakan", "invalidDate": "<PERSON><PERSON>h tidak sah", "editAction": "Edit", "requiredField": "<PERSON><PERSON><PERSON>", "optionalField": "<PERSON><PERSON><PERSON>"}}, "admin": {"onboarding": {"title": "Lengkapkan Tetapan <PERSON>", "description": "Konfigurasikan akses pentadbiran dan keutamaan pemantauan anda untuk mengoptimumkan pengalaman pengurusan lif JKR.", "form": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "saving": "Sedang menyimpan keutamaan anda..."}}, "complaints": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON>rus laporan kerosakan dan permintaan penye<PERSON>an", "continue": "Teruskan", "stats": {"totalReports": "<PERSON><PERSON><PERSON>", "submitted": "<PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "inProgress": "Sedang Diproses", "verified": "<PERSON><PERSON><PERSON><PERSON>", "pendingApproval": "<PERSON><PERSON><PERSON>", "underReview": "<PERSON><PERSON><PERSON>", "approved": "Dilulus<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "openReports": "<PERSON><PERSON><PERSON>", "closedReports": "<PERSON><PERSON><PERSON>", "outstanding": "Tertunggak (Mendesak)", "overdueDescription": "Laporan tertunggak"}, "table": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON> dan jejak laporan kerosakan anda dengan keupayaan susulan", "reportId": "ID Tiket", "dateSubmitted": "<PERSON><PERSON><PERSON>", "pmaNumber": "No PMA", "location": "<PERSON><PERSON>", "issueSummary": "<PERSON><PERSON><PERSON>", "status": "Status", "completionDate": "<PERSON><PERSON><PERSON>", "followUp": "<PERSON><PERSON><PERSON>", "verificationStatus": "Status Pengesahan", "contractor": "Kontraktor", "cost": "<PERSON><PERSON> (RM)", "actions": "<PERSON><PERSON><PERSON>", "newReport": "<PERSON><PERSON><PERSON>", "advancedFilter": "<PERSON><PERSON><PERSON>"}, "filters": {"title": "<PERSON><PERSON>", "project": "Projek", "projectPlaceholder": "<PERSON><PERSON><PERSON>", "pmaNo": "No PMA", "pmaNoPlaceholder": "Masukkan nombor PMA", "admin": "Admin", "adminPlaceholder": "<PERSON><PERSON><PERSON> admin", "status": "Status", "statusPlaceholder": "Pilih status", "dateRange": "Julat Tarikh", "resetFilters": "Tetap <PERSON>", "allStatus": "Semua Status"}, "charts": {"weeklyTrend": "Trend Penghantaran Laporan <PERSON>", "statusDistribution": "Taburan Status Laporan", "chartImplementation": "<PERSON><PERSON> akan <PERSON>n"}, "exportReport": "Eksport Laporan", "createAduan": "<PERSON><PERSON><PERSON>", "form": {"title": "<PERSON><PERSON>", "subtitle": "Sistem Pengurusan <PERSON>", "sectionA": {"title": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "complaintDate": "<PERSON><PERSON><PERSON>", "agency": "Agensi", "contractorCompanyName": "<PERSON><PERSON> / Kontraktor", "location": "<PERSON><PERSON>", "pmaNumber": "Nombor PMA Lif", "damageDescription": "<PERSON><PERSON>", "expectedCompletionDate": "<PERSON><PERSON><PERSON>", "involvesManTrap": "Melibatkan Terperangkap", "yes": "Ya", "no": "Tidak"}, "sectionB": {"title": "Maklumat Pembaikan", "note": "Bahagian ini boleh diisi selepas pembaikan selesai atau sekarang jika maklumat sudah tersedia", "actualCompletionDate": "<PERSON><PERSON><PERSON>", "repairCompletionTime": "<PERSON><PERSON>", "causeOfDamage": "<PERSON><PERSON><PERSON>", "correctionAction": "<PERSON><PERSON><PERSON>", "proofOfRepair": "<PERSON><PERSON><PERSON>", "beforePhoto": "Gambar <PERSON>", "afterPhoto": "<PERSON><PERSON><PERSON>", "repairCost": "<PERSON><PERSON>"}, "actions": {"upload": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "updateSectionB": "<PERSON><PERSON>"}, "notes": {"title": "<PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> r<PERSON>an bertanda (*) ad<PERSON>h wajib diisi", "proofRequired": "<PERSON><PERSON><PERSON> (gambar) mesti dimasukkan selepas kerja siap", "autoSubmit": "<PERSON><PERSON><PERSON> akan dihantar secara automatik"}, "fileUpload": {"acceptedFormats": "Format diterima: JPG, PNG, PDF (Maksimum 10MB setiap fail)", "maxFiles": "Maksimum 5 fail dibenarkan"}, "buttons": {"back": "🔙 Ke<PERSON>li", "submit": "📤 <PERSON><PERSON>", "update": "📤 <PERSON><PERSON>", "submitting": "Sedang dihantar...", "updating": "Sedang dikemas kini..."}, "placeholders": {"email": "<EMAIL>", "selectDate": "<PERSON><PERSON><PERSON> ta<PERSON>h", "selectAgency": "<PERSON><PERSON><PERSON>", "selectPMA": "Pilih Nombor Lif PMA", "repairCost": "0.00"}}, "status": {"underReview": "<PERSON><PERSON><PERSON>", "approved": "Dilulus<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "submitted": "<PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "complete": "Se<PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON>", "inProgress": "Sedang Diproses", "resolved": "<PERSON><PERSON><PERSON><PERSON>", "pendingApproval": "<PERSON><PERSON><PERSON>", "verified": "<PERSON><PERSON><PERSON><PERSON>", "closed": "Ditutup"}, "followUp": {"inProgress": "Sedang Diproses", "pendingApproval": "<PERSON><PERSON><PERSON>", "verified": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"upload": "<PERSON><PERSON>", "add": "Tambah", "resubmit": "<PERSON><PERSON>", "view": "Lihat", "verify": "<PERSON><PERSON><PERSON>", "verifySuccess": "<PERSON><PERSON> berjaya di<PERSON>!", "verifyError": "<PERSON><PERSON> men<PERSON> aduan"}, "verification": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>la pilih tarikh pengesahan dan sahkan bahawa anda ingin mengesahkan aduan ini sebagai selesai.", "dateLabel": "<PERSON><PERSON><PERSON>", "selectDate": "<PERSON><PERSON><PERSON> tarikh pen<PERSON>", "verifyButton": "<PERSON><PERSON><PERSON>", "verifying": "Mengesahkan...", "verifiedBy": "<PERSON><PERSON><PERSON><PERSON>:", "verifiedDate": "<PERSON><PERSON><PERSON>:", "verificationDetails": "<PERSON><PERSON><PERSON>"}, "pagination": {"showing": "<PERSON><PERSON><PERSON><PERSON><PERSON> {start}-{end} daripada {total} entri", "previous": "Sebelumnya", "next": "Seterusnya"}, "upload": {"title": "<PERSON>at <PERSON>", "subtitle": "Tambah dokumen sokongan untuk laporan yang sedang disemak", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> ta<PERSON>, atau dokumen yang diminta", "chooseFiles": "<PERSON><PERSON><PERSON>", "takePhoto": "Ambil Gambar"}, "recentActivity": {"title": "Aktiviti Terkini", "subtitle": "Kemaskini terkini mengenai laporan aduan anda"}, "common": {"search": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>"}, "urgentSection": {"title": "<PERSON><PERSON> (Mendesak) - La<PERSON><PERSON> ({count})", "description": "<PERSON><PERSON><PERSON> yang telah melepasi tarikh siap yang dijangka dan memer<PERSON>an perhatian segera", "daysOverdue": "{days} hari{plural} tertunggak", "expectedLabel": "Dijangka:"}}, "profilePage": {"loading": {"title": "Memuatkan profil anda...", "description": "<PERSON>la tunggu semasa kami mengambil maklumat anda"}, "error": {"title": "Tidak Dapat Memuatkan Profil", "description": "<PERSON>mi tidak dapat memuatkan maklumat profil anda. Ini mungkin masalah sementara.", "refreshPage": "<PERSON><PERSON>", "goToDashboard": "<PERSON>gi ke Dashboard"}, "contractor": {"onboarding": {"title": "Lengkapkan Pendaftaran Anda", "description": "Anda hampir selesai! Lengkapkan profil kontraktor anda untuk mengakses semua ciri dan mula bekerjasama.", "submitting": "Melengkapkan pendaftaran anda...", "submittingDescription": "Ini mungkin mengambil masa beberapa saat"}, "completed": {"title": "<PERSON><PERSON>", "description": "Pendaftaran kontraktor anda telah berjaya dilengkapkan dan disahkan.", "profileInformation": "Maklumat Profil", "fullName": "<PERSON><PERSON>", "emailAddress": "<PERSON><PERSON><PERSON>", "phoneNumber": "Telefon Bimbit", "roleAndStatus": "Peranan & Status", "contractor": "Kontraktor", "verifiedRole": "<PERSON><PERSON><PERSON>", "registrationComplete": "Pendaftaran Lengkap", "allRequirementsFulfilled": "<PERSON><PERSON><PERSON>i", "goToDashboard": "<PERSON>gi ke Dashboard", "accountSettings": "<PERSON><PERSON><PERSON>", "joined": "Menyertai", "loadingContractorDetails": "Memuatkan butiran kontraktor...", "unableToLoadDetails": "Tidak dapat memuatkan butiran kontraktor. Ini mungkin menun<PERSON>kkan penyediaan profil yang tidak lengkap atau ralat sistem."}}, "jkr": {"title": "Profil Pentadbir JKR", "description": "Selamat datang ke panel pentadbir Sistem Pengurusan Lif JKR. Pantau dan urus operasi penyelenggaraan lif di semua kemudahan.", "profileInformation": "Maklumat Profil", "fullName": "<PERSON><PERSON>", "emailAddress": "<PERSON><PERSON><PERSON>", "phoneNumber": "Telefon Bimbit", "roleAndStatus": "Peranan & Status", "administrator": "Pentadbir", "verifiedRole": "<PERSON><PERSON><PERSON>", "fullAccess": "<PERSON><PERSON><PERSON>", "systemAdministrator": "Pentadbir Sistem", "systemManagement": "Pen<PERSON><PERSON><PERSON> Sistem", "description2": "Urus operasi sistem dan akses pengguna", "manageDashboard": "Urus Dashboard", "systemSettings": "Tetapan <PERSON>"}, "client": {"title": "<PERSON><PERSON>", "description": "Selamat datang ke portal klien anda. Pantau dan urus projek dan permintaan penyelenggaraan lif anda.", "profileInformation": "Maklumat Profil", "fullName": "<PERSON><PERSON>", "emailAddress": "<PERSON><PERSON><PERSON>", "phoneNumber": "Telefon Bimbit", "roleAndStatus": "Peranan & Status", "client": "<PERSON><PERSON><PERSON>", "verifiedRole": "<PERSON><PERSON><PERSON>", "activeAccount": "Akaun Aktif", "projectAccess": "<PERSON><PERSON><PERSON>", "projectManagement": "<PERSON><PERSON><PERSON><PERSON>", "description2": "Pantau projek <PERSON>lenggaraan lif anda", "viewProjects": "<PERSON><PERSON>", "accountSettings": "<PERSON><PERSON><PERSON>", "buildingClient": "Pemilik Bangunan/Klien", "assetOwner": "<PERSON><PERSON><PERSON><PERSON> aset dan pengawasan penye<PERSON>an", "activeStatus": "Status Aktif", "accountVerified": "<PERSON><PERSON><PERSON> disahkan dan aktif", "goToDashboard": "<PERSON>gi ke Dashboard", "assetManagement": "<PERSON><PERSON><PERSON><PERSON>", "assetManagementDescription": "<PERSON>ks<PERSON> pantas ke pengurusan bangunan dan penjadualan penyelenggaraan", "myBuildings": "<PERSON><PERSON><PERSON>", "liftStatus": "Status Lif", "maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fallback": {"message": "Maklumat profil anda me<PERSON>an perhatian. <PERSON>la leng<PERSON>kan persediaan anda atau hubungi sokongan untuk bantuan."}, "contractorDetails": {"incomplete": {"title": "Profil Kontraktor Tidak Lengkap", "description": "Persediaan profil kontraktor anda tidak lengkap. <PERSON><PERSON> leng<PERSON> proses onboarding anda."}, "companyInformation": {"title": "Mak<PERSON>at Syarikat", "active": "Aktif", "companyName": "<PERSON><PERSON>", "companyType": "<PERSON><PERSON>", "companyCode": "<PERSON><PERSON>", "companyHotline": "Hotline Syarikat", "oemName": "<PERSON>a OEM", "registered": "<PERSON><PERSON><PERSON><PERSON>", "notSpecified": "Tidak dinyatakan", "types": {"competentFirm": "<PERSON><PERSON>", "nonCompetentFirm": "Firma Tidak Berkelayakan", "oem": "OEM"}}, "userDetails": {"title": "Butiran <PERSON>", "fullName": "<PERSON><PERSON>", "email": "E-mel", "phoneNumber": "Telefon Bimbit", "userType": "<PERSON><PERSON>", "joined": "Menyertai"}, "documents": {"title": "Dokumen", "description": "<PERSON><PERSON>i pengurusan dokumen akan tersedia tidak lama lagi.", "uploadRegistration": "<PERSON><PERSON>", "uploadLif": "Muat Naik Fail Senarai LIF", "registrationModal": {"title": "<PERSON><PERSON>", "description": "Muat naik sijil pendaftaran baru. Format yang diterima: PDF, JPG, PNG (maksimum 10MB)"}, "lifModal": {"title": "Muat Naik Fail Senarai LIF", "description": "Muat naik fail senarai LIF. <PERSON><PERSON> boleh muat naik sehingga 5 fail. Format yang diterima: PDF, JPG, PNG (maksimum 10MB setiap satu)"}}, "teamMembers": {"title": "<PERSON><PERSON>", "member": "ahli", "members": "ahli", "noMembers": "<PERSON><PERSON><PERSON> ahli pasukan dijumpai untuk syarikat ini.", "error": "Tidak dapat memuatkan ahli pasukan. Sila cuba lagi kemudian.", "unnamedUser": "Pengguna Tanpa Nama", "joined": "Menyertai", "notSpecified": "Tidak dinyatakan", "roles": {"contractor": "Kontraktor", "admin": "Pentadbir", "viewer": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"active": "Aktif", "pending": "<PERSON><PERSON><PERSON>"}}}, "companyForm": {"title": "Pendaftaran Syarikat", "description": "Daftarkan syarikat anda dalam pangkalan data kontraktor", "sections": {"companyInformation": "Mak<PERSON>at Syarikat", "oemDetails": "Butiran OEM", "appointedFirmDetails": "<PERSON><PERSON><PERSON> ya<PERSON>"}, "fields": {"companyCode": {"label": "<PERSON><PERSON>", "description": "Kod unik ini akan mengenal pasti syarikat anda dalam sistem. Format: YYMM-XXXX-XXXX", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>..."}, "companyName": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama syarikat anda"}, "companyType": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> jeni<PERSON> s<PERSON> anda", "options": {"competentFirm": "<PERSON><PERSON>", "nonCompetentFirm": "Firma Tidak Berkelayakan", "oem": "OEM (Pengilang Peralatan Asal)"}}, "companyHotline": {"label": "Hotline Syarikat", "placeholder": "Masukkan nombor hotline syarikat"}, "oemName": {"label": "<PERSON>a OEM", "placeholder": "Ma<PERSON>kkan nama OEM", "description": "<PERSON><PERSON><PERSON><PERSON> untuk syarikat jenis O<PERSON>"}, "appointedFirm": {"label": "OEM/Firma Berkelayakan yang Dilantik", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama OEM atau firma berkelayakan yang dilantik", "description": "Diperlukan untuk firma tidak berkelayakan - nyatakan OEM atau firma berkelayakan yang dilantik untuk perkhidmatan anda"}}, "actions": {"copy": "<PERSON><PERSON>", "refresh": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>", "registering": "Mendaftar..."}, "footer": {"requiredNote": "<PERSON><PERSON><PERSON> medan yang ditandakan dengan * adalah diperlukan"}}}, "settingsPage": {"title": "<PERSON><PERSON><PERSON>", "description": "Urus<PERSON> tetapan <PERSON>, pilihan k<PERSON>, dan pilihan pengalaman pengguna anda.", "loading": {"title": "Memuatkan Tetapan", "description": "<PERSON>la tunggu semasa kami memuatkan tetapan akaun anda..."}, "tabs": {"profile": "Profil", "security": "Keselamatan", "preferences": "<PERSON><PERSON><PERSON>"}, "profile": {"title": "Maklumat Profil", "description": "<PERSON><PERSON> kini maklumat peribadi dan butiran hubungan anda.", "fullName": "<PERSON><PERSON>", "fullNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama penuh anda", "email": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> alamat e-mel anda", "phoneNumber": "Nombor Telefon", "phoneNumberPlaceholder": "Masukkan nombor telefon anda", "roleInformation": "Mak<PERSON><PERSON>", "userRole": "<PERSON><PERSON><PERSON>", "accountType": "<PERSON><PERSON>", "contractorAccount": "<PERSON><PERSON><PERSON>", "saveSuccess": "Maklumat profil berjaya dikemas kini!", "saveError": "Gagal mengemas kini maklumat profil. Sila cuba lagi."}, "security": {"title": "Keselamatan & Log Masuk", "description": "Uruskan tetapan keselamatan akaun dan pilihan pengesahan anda.", "password": "<PERSON><PERSON>", "passwordDescription": "<PERSON><PERSON><PERSON> akaun anda menggunakan kata laluan yang panjang dan rawak untuk kekal selamat.", "changePassword": "<PERSON><PERSON>", "changePasswordDescription": "<PERSON><PERSON><PERSON>n kata laluan semasa anda dan pilih kata laluan selamat yang baharu.", "currentPassword": "<PERSON><PERSON>", "currentPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kata laluan semasa anda", "newPassword": "<PERSON><PERSON>", "newPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kata laluan baharu anda", "confirmPassword": "<PERSON><PERSON><PERSON>", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON> kata laluan baharu anda", "updatePassword": "<PERSON><PERSON>", "passwordChangeSuccess": "Kata laluan berjaya dikemas kini!", "passwordChangeError": "Gagal mengemas kini kata laluan. Sila cuba lagi.", "passwordMismatch": "<PERSON>a laluan baharu tidak sepadan. Sila cuba lagi.", "twoFactorAuth": "Pengesahan Dua Faktor", "twoFactorDescription": "Tambah lapisan keselamatan tambahan pada akaun anda dengan pengesahan dua faktor."}, "preferences": {"title": "Pengalaman Pengguna", "description": "Sesuaikan pengalaman pengguna dan pilihan pemberitahuan anda.", "appearance": "Penampilan", "theme": "<PERSON><PERSON>", "themeDescription": "<PERSON><PERSON><PERSON> skema warna pilihan anda untuk aplikasi.", "themeNote": "<PERSON>da masa ini, hanya mod terang tersedia. Mod gelap akan tersedia dalam kemas kini akan datang.", "light": "Terang", "dark": "<PERSON><PERSON><PERSON>", "notifications": "Pemberitahuan", "emailNotifications": "Pemberitahuan E-mel", "emailNotificationsDescription": "Terima pemberitahuan e-mel untuk kemas kini dan aktiviti penting.", "pushNotifications": "Pemberita<PERSON><PERSON>", "pushNotificationsDescription": "Terima pemberitahuan tolak pada peranti anda untuk kemas kini masa nyata.", "marketingEmails": "<PERSON><PERSON><PERSON><PERSON>", "marketingEmailsDescription": "Terima e-mel tentang ciri baharu, petua, dan kemas kini produk.", "saveSuccess": "<PERSON><PERSON><PERSON> berjaya dikemas kini!", "saveError": "Gagal mengemas kini pilihan. Sila cuba lagi."}}, "billing": {"dashboard": {"title": "<PERSON>n <PERSON>", "subtitle": "Uruskan langganan dan akses projek anda", "tabs": {"all": "<PERSON><PERSON><PERSON>", "urgent": "Mendesak", "active": "Aktif", "inactive": "Tidak Aktif"}, "viewModes": {"grid": "Paparan <PERSON>", "list": "<PERSON><PERSON>"}, "stats": {"totalSubscriptions": "<PERSON><PERSON><PERSON>", "activeProjects": "Projek Aktif", "monthlyTotal": "<PERSON><PERSON><PERSON>", "urgentItems": "<PERSON><PERSON>"}, "accessOverview": {"title": "<PERSON><PERSON><PERSON><PERSON>", "allProjectsActive": "Se<PERSON>a projek mempunyai akses aktif", "someProjectsRestricted": "{count} projek mempunyai akses terhad", "viewSuspended": "Lihat Projek <PERSON>"}, "urgentActions": {"title": "Tindakan Mendesak Diperlukan", "gracePeriodEnding": "Tempoh kelonggaran akan berakhir tidak lama lagi", "paymentOverdue": "Pembayaran tertunggak", "accessSuspended": "<PERSON><PERSON><PERSON> dig<PERSON>", "payNow": "<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON>"}, "noSubscriptions": {"title": "Tiada Langganan Aktif", "description": "Anda belum mempunyai sebarang langganan aktif lagi.", "action": "Cipta <PERSON>"}, "errors": {"loadFailed": "Gagal memuatkan maklumat bil. Sila cuba lagi.", "retry": "Cuba Lagi"}}, "setup": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Sediakan bil untuk {projectName}", "steps": {"summary": {"title": "<PERSON><PERSON><PERSON>", "description": "Se<PERSON>k butiran projek dan maklumat bil"}, "payment": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Konfigurasikan maklumat bil anda"}, "confirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Lengkapkan persediaan dan aktifkan akses"}}, "projectDetails": {"title": "Butiran <PERSON>", "name": "<PERSON><PERSON>", "description": "Penerangan", "subscription": "<PERSON><PERSON><PERSON>", "monthlyFee": "<PERSON><PERSON>", "billing": "Maklumat Bil"}, "paymentForm": {"title": "Maklumat Pembayaran", "description": "<PERSON><PERSON><PERSON><PERSON> butiran pem<PERSON> anda untuk mengaktifkan akses projek", "processingFee": "Yuran pemprosesan sebanyak RM 1.00 dikenakan"}, "confirmation": {"title": "<PERSON><PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> pembayaran berjaya disel<PERSON>!", "accessActivated": "<PERSON><PERSON><PERSON> projek telah diaktifkan", "redirecting": "Mengalihkan ke gerbang pembayaran...", "viewProject": "<PERSON><PERSON>", "backToDashboard": "Ke<PERSON>li ke Papan Pem<PERSON>"}, "actions": {"back": "Kembali", "next": "Seterusnya", "cancel": "<PERSON><PERSON>", "completeSetup": "Lengkapkan Persediaan", "payNow": "<PERSON><PERSON>"}, "errors": {"setupFailed": "Persediaan pembayaran gagal. Sila cuba lagi.", "projectNotFound": "Projek tidak dijumpai atau akses di<PERSON>."}}, "subscription": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Uruskan langganan untuk {projectName}", "status": {"active": "Aktif", "grace_period": "<PERSON><PERSON><PERSON>", "suspended": "Digantung", "cancelled": "Di<PERSON><PERSON><PERSON>"}, "details": {"title": "<PERSON><PERSON><PERSON>", "monthlyFee": "<PERSON><PERSON>", "nextPayment": "Pembayaran Seterusnya", "status": "Status", "created": "<PERSON><PERSON><PERSON>", "lastPayment": "Pembayaran <PERSON>rak<PERSON>"}, "accessStatus": {"title": "Status Akses <PERSON>jek", "fullAccess": "<PERSON><PERSON><PERSON>", "restrictedAccess": "<PERSON><PERSON><PERSON>", "noAccess": "Tiada Akses", "gracePeriod": "Tempoh Kelonggaran Aktif"}, "paymentHistory": {"title": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "status": "Status", "method": "<PERSON><PERSON><PERSON>", "receipt": "Resit", "noPayments": "T<PERSON>da sejarah pembayaran tersedia"}, "gracePeriod": {"title": "<PERSON><PERSON><PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON> anda dalam tempoh kelo<PERSON>n", "daysRemaining": "{days} hari berbaki", "expired": "Tempoh kelonggaran tamat", "payToRestore": "Bayar sekarang untuk memulihkan akses penuh"}, "actions": {"payNow": "<PERSON><PERSON>", "downloadReceipt": "<PERSON><PERSON>", "cancelSubscription": "<PERSON><PERSON>", "reactivate": "Aktifkan Semula", "viewProject": "<PERSON><PERSON>", "retryPayment": "Cuba Pembayaran Semula"}, "cancellation": {"title": "<PERSON><PERSON>", "warning": "<PERSON><PERSON>h anda pasti mahu membatalkan langganan ini?", "consequences": "Ini akan menggantung akses projek dengan serta-merta", "confirm": "<PERSON>, <PERSON><PERSON>", "keep": "Kekalkan <PERSON>"}}, "payment": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Butiran pembayaran untuk {projectName}", "status": {"processing": "Memp<PERSON><PERSON>", "completed": "Pembayaran <PERSON>", "failed": "Pembayaran Gagal", "pending": "Pembayaran Tertangguh"}, "details": {"title": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON>", "reference": "Rujukan", "description": "Penerangan"}, "receipt": {"title": "Resit", "download": "<PERSON><PERSON>", "email": "E-mel Resit", "print": "Cetak Resit"}, "accessRestoration": {"title": "<PERSON><PERSON><PERSON><PERSON>", "restored": "<PERSON><PERSON><PERSON> projek telah dipulihkan", "processing": "Memproses pemulihan akses...", "failed": "<PERSON><PERSON> memuli<PERSON> akses"}, "actions": {"viewProject": "<PERSON><PERSON>", "backToBilling": "Kembali ke Bil", "retryPayment": "Cuba Pembayaran Semula", "contactSupport": "Hubungi <PERSON>"}, "retry": {"title": "Cuba Pembayaran Semula", "description": "Pembayaran sebelumnya gagal. Sila cuba lagi.", "newPayment": "Buat Pembayaran Baharu"}}, "suspended": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Uruskan projek digantung dan pulihkan akses", "overview": {"totalSuspended": "<PERSON><PERSON><PERSON>", "totalOwed": "<PERSON><PERSON><PERSON>", "oldestSuspension": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "projectsList": {"title": "<PERSON><PERSON><PERSON>", "project": "Projek", "suspendedDate": "<PERSON><PERSON><PERSON>", "amountOwed": "<PERSON><PERSON><PERSON>", "daysSuspended": "<PERSON>", "lastPayment": "Pembayaran <PERSON>rak<PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "bulkActions": {"title": "<PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON>", "selected": "{count} dipilih", "paySelected": "Bayar untuk Projek Terpilih", "totalSelected": "Jumlah: RM {amount}"}, "restoration": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bayar jumlah tertunggak untuk memulihkan akses projek", "priorityProjects": "<PERSON><PERSON><PERSON>", "paymentPlan": "<PERSON><PERSON><PERSON>", "contactSupport": "Hubungi Sokongan untuk Pelan Pembayaran"}, "actions": {"payNow": "<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON>", "payAll": "Bayar Semua <PERSON>", "requestExtension": "Minta Pelan<PERSON>", "contactSupport": "Hubungi <PERSON>"}, "empty": {"title": "Tiada Projek <PERSON>", "description": "<PERSON><PERSON><PERSON> pro<PERSON> anda mempunyai akses aktif", "backToDashboard": "Ke<PERSON>li ke Papan Pem<PERSON>"}}, "common": {"currency": "RM", "monthly": "bulanan", "yearly": "<PERSON><PERSON><PERSON>", "gracePeriod": "<PERSON><PERSON><PERSON>", "daysRemaining": "{days} hari berbaki", "accessLevel": {"full": "<PERSON><PERSON><PERSON>", "restricted": "Terhad", "suspended": "Digantung"}, "paymentMethods": {"billplz": "Bill<PERSON><PERSON><PERSON>", "card": "<PERSON><PERSON>", "bank": "<PERSON><PERSON><PERSON>"}, "loading": "Memuatkan maklumat bil...", "error": "<PERSON><PERSON> memuatkan data", "retry": "Cuba Lagi", "back": "Kembali", "next": "Seterusnya", "cancel": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "save": "Simpan"}}}