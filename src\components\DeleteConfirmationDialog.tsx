import { Alert<PERSON>riangle, Loader2 } from 'lucide-react';
import { Button } from './ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';

export interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel?: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  loading?: boolean;
}

export function DeleteConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  title = 'Delete Item',
  message = 'Are you sure you want to delete this item? This action cannot be undone.',
  confirmText = 'Delete',
  cancelText = 'Cancel',
  loading = false,
}: DeleteConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md rounded-2xl shadow-2xl p-0 overflow-hidden">
        <div className="flex flex-col items-center px-6 pt-8 pb-2">
          <span
            className="inline-flex items-center justify-center rounded-full bg-destructive/10 text-destructive mb-4 shadow-sm"
            style={{ boxShadow: '0 2px 16px 0 rgba(255, 72, 66, 0.08)' }}
          >
            <AlertTriangle className="h-10 w-10" />
          </span>
          <DialogHeader className="w-full items-center text-center mb-2">
            <DialogTitle className="text-2xl font-bold text-destructive mb-1 tracking-tight">
              {title}
            </DialogTitle>
            <DialogDescription className="text-base text-gray-600 max-w-xs mx-auto">
              {message}
            </DialogDescription>
          </DialogHeader>
        </div>
        <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 px-6 pb-6 gap-2">
          <Button
            variant="outline"
            onClick={() => {
              onOpenChange(false);
              onCancel?.();
            }}
            disabled={loading}
            className="w-full sm:w-auto border-gray-300 hover:border-gray-400"
          >
            {cancelText}
          </Button>
          <Button
            variant="destructive"
            onClick={() => {
              onConfirm();
              onOpenChange(false);
            }}
            disabled={loading}
            className="w-full sm:w-auto font-semibold shadow-sm flex items-center justify-center gap-2"
            aria-label={confirmText}
          >
            {loading && <Loader2 className="animate-spin h-4 w-4 mr-1" />}
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
