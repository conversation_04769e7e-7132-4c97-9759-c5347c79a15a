/**
 * Custom hook for project access control
 * This demonstrates how to implement the JKR state-based access control
 */

import { useUserWithProfile } from '@/hooks/use-auth';
import { supabase } from '@/lib/supabase';
import { AccessControl, Project } from '@/types/access-control';
import { useQuery } from '@tanstack/react-query';

/**
 * Hook to fetch projects based on user's access level
 * Includes projects owned by user's organization AND projects they're members of via invitations
 */
export function useAccessibleProjects() {
  const { data: user } = useUserWithProfile(); // Get current user with profile

  return useQuery({
    queryKey: [
      'accessible-projects',
      user?.id,
      user?.profile?.user_role,
      user?.profile?.monitoring_state,
      user?.profile?.contractor_id,
    ],
    queryFn: async (): Promise<Project[]> => {
      if (!user?.profile) return [];

      // Get project IDs where user is a member with accepted status
      const { data: memberProjectIds } = await supabase
        .from('project_users')
        .select('project_id')
        .eq('user_id', user.id)
        .eq('status', 'accepted')
        .eq('is_active', true);

      const memberProjectIdList =
        memberProjectIds?.map((p) => p.project_id) || [];

      // Build the main query
      let query = supabase
        .from('projects')
        .select(
          `
          id,
          name,
          code,
          state,
          location,
          start_date,
          end_date,
          status,
          contractor_id,
          agency_id
        `,
        )
        .is('deleted_at', null); // Only active projects

      // All users can only see projects where they are explicit members
      if (memberProjectIdList.length === 0) {
        // If user is not a member of any projects, return no results
        query = query.eq('id', '00000000-0000-0000-0000-000000000000'); // Never matches
        const { data, error } = await query.order('created_at', {
          ascending: false,
        });

        if (error) {
          throw new Error(`Failed to fetch projects: ${error.message}`);
        }

        return data || [];
      }

      // Filter to only include projects where user is an explicit member
      query = query.in('id', memberProjectIdList);

      // Apply additional role-based filtering
      if (user.profile.user_role === 'admin') {
        // State-based admins must also match their monitoring state
        if (
          user.profile.admin_access_mode === 'state' &&
          user.profile.monitoring_state
        ) {
          query = query.eq('state', user.profile.monitoring_state);
        }
        // Project-based admins can see all projects they are members of (no additional filtering)
      }
      // Contractors and viewers can see all projects they are members of (no additional filtering)

      const { data, error } = await query.order('created_at', {
        ascending: false,
      });

      if (error) {
        throw new Error(`Failed to fetch projects: ${error.message}`);
      }

      return data || [];
    },
    enabled: !!user,
  });
}

/**
 * Hook to check if current user can view a specific project
 */
export function useCanViewProject(projectId: string) {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['can-view-project', user?.id, projectId],
    queryFn: async (): Promise<boolean> => {
      if (!user?.profile || !projectId) return false;

      // Check if user is a member of this project with accepted status
      const { data: memberCheck } = await supabase
        .from('project_users')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .eq('status', 'accepted')
        .eq('is_active', true)
        .maybeSingle();

      const isProjectMember = !!memberCheck;

      // Fetch the project
      const { data: project, error } = await supabase
        .from('projects')
        .select(
          'id, name, code, state, location, start_date, end_date, status, contractor_id, agency_id',
        )
        .eq('id', projectId)
        .is('deleted_at', null)
        .single();

      if (error || !project) return false;

      // Transform user to match AccessControl interface
      const accessControlUser = {
        id: user.id,
        name: user.profile.name || '',
        email: user.email || '',
        user_role: user.profile.user_role,
        admin_access_mode: user.profile.admin_access_mode,
        monitoring_state: user.profile.monitoring_state,
        contractor_id: user.profile.contractor_id,
      };

      // Check access using our access control utility with project membership info
      return AccessControl.canUserViewProject(
        accessControlUser,
        project,
        isProjectMember,
      );
    },
    enabled: !!user && !!projectId,
  });
}

/**
 * Hook specifically for JKR users to get their monitoring scope
 */
export function useJKRMonitoringScope() {
  const { data: user } = useUserWithProfile();

  const scope = {
    canViewAllStates: false, // No user can view all states - all must be project members
    monitoringState:
      user?.profile?.user_role === 'admin' &&
      user?.profile?.admin_access_mode === 'state'
        ? user.profile.monitoring_state
        : null,
    isJKRUser: user?.profile?.user_role === 'admin',
  };

  return scope;
}
