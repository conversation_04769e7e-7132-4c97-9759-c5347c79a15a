-- Test script to verify RLS policies work without infinite recursion
-- Run this in Supabase Studio SQL Editor

-- Test 1: Create a test contractor
INSERT INTO contractors (id, name, contractor_type, is_active, code)
VALUES ('550e8400-e29b-41d4-a716-446655440000', 'Test Contractor', 'COMPETENT_FIRM', true, 'TEST001');

-- Test 2: Create a test user (contractor)
INSERT INTO users (id, name, email, user_role, contractor_id, onboarding_completed)
VALUES ('550e8400-e29b-41d4-a716-446655440001', 'Test User', '<EMAIL>', 'contractor', '550e8400-e29b-41d4-a716-446655440000', true);

-- Test 3: Create a test agency
INSERT INTO agencies (id, name, state)
VALUES ('550e8400-e29b-41d4-a716-446655440002', 'Test Agency', 'KL');

-- Test 4: Create a test project
INSERT INTO projects (id, contractor_id, agency_id, name, code, state, location, status)
VALUES ('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440002', 'Test Project', 'PROJ001', 'KL', 'Test Location', 'active');

-- Test 5: Create a project user membership
INSERT INTO project_users (id, project_id, user_id, role, status, is_active)
VALUES ('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', 'admin', 'accepted', true);

-- Test 6: Test the helper functions
SELECT 'Testing get_user_project_ids function:' as test_name;
SELECT * FROM get_user_project_ids('550e8400-e29b-41d4-a716-446655440001');

SELECT 'Testing can_user_access_project function:' as test_name;
SELECT can_user_access_project('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001');

-- Test 7: Test project selection (this should not cause infinite recursion)
SELECT 'Testing project selection with RLS:' as test_name;
SELECT id, name, code FROM projects WHERE id = '550e8400-e29b-41d4-a716-446655440003';

-- Test 8: Test project_users selection
SELECT 'Testing project_users selection with RLS:' as test_name;
SELECT id, project_id, user_id, role FROM project_users WHERE project_id = '550e8400-e29b-41d4-a716-446655440003';

-- Cleanup
DELETE FROM project_users WHERE id = '550e8400-e29b-41d4-a716-446655440004';
DELETE FROM projects WHERE id = '550e8400-e29b-41d4-a716-446655440003';
DELETE FROM agencies WHERE id = '550e8400-e29b-41d4-a716-446655440002';
DELETE FROM users WHERE id = '550e8400-e29b-41d4-a716-446655440001';
DELETE FROM contractors WHERE id = '550e8400-e29b-41d4-a716-446655440000';

SELECT 'All tests completed successfully!' as result;
