-- ================================
-- COMPLETE COMPETENT_PERSON MIGRATION
-- Migration to finalize the transition from users-based competent person to dedicated table
-- ================================

-- ================================
-- REPLACE COMPETENT_PERSON_ID COLUMN
-- ================================
-- Drop the old competent_person_id column that referenced users table
ALTER TABLE pma_certificates 
DROP COLUMN IF EXISTS competent_person_id;

-- Add the new competent_person_id column that references competent_person table
ALTER TABLE pma_certificates 
ADD COLUMN competent_person_id UUID;

-- ================================
-- UPDATE COMMENTS
-- ================================
COMMENT ON COLUMN pma_certificates.competent_person_id IS 
'Reference to competent_person table for the competent person associated with this PMA certificate';

-- ================================
-- ADD BUSINESS LOGIC CONSTRAINTS (Optional - can be handled in application)
-- ================================
-- Constraint to ensure cert_exp_date is in the future when status is valid
-- (Commented out as mentioned in original schema - better handled at application level)
/*
ALTER TABLE competent_person 
ADD CONSTRAINT check_cert_exp_date_future 
CHECK (
  cert_exp_date IS NULL OR 
  cert_exp_date > CURRENT_DATE
);
*/

-- ================================
-- ADD FOREIGN KEY CONSTRAINT
-- ================================
-- Add the foreign key constraint for the new competent_person_id column
ALTER TABLE pma_certificates 
ADD CONSTRAINT pma_certificates_competent_person_id_fkey 
FOREIGN KEY (competent_person_id) REFERENCES competent_person(id) ON DELETE SET NULL;

-- ================================
-- CREATE INDEX FOR NEW COLUMN
-- ================================
-- Create index for the new competent_person_id column
CREATE INDEX idx_pma_cert_competent_person_id ON pma_certificates(competent_person_id);

-- ================================
-- END OF MIGRATION
-- ================================
