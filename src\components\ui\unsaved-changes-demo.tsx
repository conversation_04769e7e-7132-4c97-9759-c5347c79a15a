'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UnsavedChangesDialog } from '@/components/ui/unsaved-changes-dialog';
import { useUnsavedChanges } from '@/hooks/use-unsaved-changes';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

interface DemoFormData {
  name: string;
  email: string;
  message: string;
}

/**
 * Demo component to showcase the unsaved changes warning functionality
 * This can be used for testing or as a reference for other forms
 */
export function UnsavedChangesDemo() {
  const form = useForm<DemoFormData>({
    defaultValues: {
      name: '',
      email: '',
      message: '',
    },
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  // Track if form has unsaved changes
  const hasUnsavedChanges = form.formState.isDirty && !isSubmitted;

  // Use the unsaved changes hook
  const { showConfirmDialog, confirmNavigation, cancelNavigation, markSaved } =
    useUnsavedChanges({
      hasUnsavedChanges,
      message: 'You have unsaved form data. Are you sure you want to leave?',
    });

  const onSubmit = (data: DemoFormData) => {
    console.log('Form submitted:', data);
    markSaved(); // Mark as saved to prevent warnings
    setIsSubmitted(true);

    // Reset after a delay to show the demo again
    setTimeout(() => {
      form.reset();
      setIsSubmitted(false);
    }, 2000);
  };

  const handleReset = () => {
    if (hasUnsavedChanges) {
      if (
        window.confirm(
          'Are you sure you want to reset? You will lose your unsaved changes.',
        )
      ) {
        form.reset();
        setIsSubmitted(false);
      }
    } else {
      form.reset();
      setIsSubmitted(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-card rounded-lg shadow-lg border">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Unsaved Changes Demo</h2>
        <p className="text-sm text-muted-foreground">
          Fill out the form and try to refresh the page or navigate away to see
          the warning.
        </p>
      </div>

      {isSubmitted ? (
        <div className="text-center py-8">
          <div className="text-green-600 mb-2">
            ✅ Form submitted successfully!
          </div>
          <p className="text-sm text-muted-foreground">
            Resetting form in 2 seconds...
          </p>
        </div>
      ) : (
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              placeholder="Enter your name"
              {...form.register('name')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              {...form.register('email')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Message</Label>
            <Input
              id="message"
              placeholder="Enter a message"
              {...form.register('message')}
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="submit" className="flex-1">
              Submit
            </Button>
            <Button type="button" variant="outline" onClick={handleReset}>
              Reset
            </Button>
          </div>

          {hasUnsavedChanges && (
            <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
              ⚠️ You have unsaved changes. Try refreshing the page to see the
              warning!
            </div>
          )}
        </form>
      )}

      {/* Confirmation Dialog */}
      <UnsavedChangesDialog
        open={showConfirmDialog}
        onConfirm={confirmNavigation}
        onCancel={cancelNavigation}
        title="Unsaved Changes"
        description="You have unsaved changes that will be lost if you continue. Are you sure?"
        confirmText="Leave Page"
        cancelText="Stay Here"
      />
    </div>
  );
}
