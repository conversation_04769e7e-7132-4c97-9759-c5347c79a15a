-- Add simplified subscription cancellation support
-- This migration implements a simple cancellation flow where:
-- 1. Cancel subscription -> status = 'cancelled', cancelled_at = now() (access continues)
-- 2. Monthly cron job on 27th -> changes 'cancelled' to 'suspended' (access ends)

-- Add cancellation timestamp field
ALTER TABLE public.pma_subscriptions 
ADD COLUMN cancelled_at timestamp with time zone null;

-- Add constraint for cancellation logic
-- cancelled_at can be set for both 'cancelled' and 'suspended' statuses
-- (suspended subscriptions can have cancelled_at if they were cancelled first)
ALTER TABLE public.pma_subscriptions
ADD CONSTRAINT chk_pma_cancellation_logic CHECK (
  -- If status is 'cancelled', cancelled_at must be set
  (
    (status = 'cancelled'::subscription_status)
    AND (cancelled_at IS NOT NULL)
  )
  -- If status is not 'cancelled' or 'suspended', cancelled_at must be null
  OR (
    (status NOT IN ('cancelled'::subscription_status, 'suspended'::subscription_status))
    AND (cancelled_at IS NULL)
  )
  -- If status is 'suspended', cancelled_at can be either null or set
  OR (status = 'suspended'::subscription_status)
);

-- Update the access_allowed computed column to include cancelled status
-- Drop the existing generated column
ALTER TABLE public.pma_subscriptions 
DROP COLUMN access_allowed;

-- Recreate with logic that allows access for cancelled subscriptions until cron job changes them to suspended
ALTER TABLE public.pma_subscriptions
ADD COLUMN access_allowed boolean GENERATED ALWAYS AS (
  status IN ('active', 'grace_period', 'trial', 'cancelled')
) STORED;

-- Add index for efficient querying of cancelled subscriptions
CREATE INDEX IF NOT EXISTS idx_pma_subscriptions_cancelled_at 
ON public.pma_subscriptions USING btree (cancelled_at) 
WHERE status = 'cancelled'::subscription_status;

-- Add index for access queries
CREATE INDEX IF NOT EXISTS idx_pma_subscriptions_access_simple 
ON public.pma_subscriptions USING btree (access_allowed, status) 
WHERE access_allowed = true;

-- Create function to process monthly cancellation suspensions (to be called by cron job on 27th)
CREATE OR REPLACE FUNCTION public.process_monthly_cancellation_suspensions()
RETURNS TABLE(updated_count integer, processed_subscriptions text[])
LANGUAGE plpgsql
AS $$
DECLARE
  subscription_ids text[];
  updated_rows integer;
BEGIN
  -- Get the subscription IDs that will be updated for logging
  SELECT array_agg(id::text) INTO subscription_ids
  FROM pma_subscriptions 
  WHERE status = 'cancelled'::subscription_status
    AND EXTRACT(day FROM now()) = 27;
  
  -- Update cancelled subscriptions to suspended on the 27th
  UPDATE pma_subscriptions 
  SET status = 'suspended'::subscription_status
  WHERE status = 'cancelled'::subscription_status
    AND EXTRACT(day FROM now()) = 27;
  
  GET DIAGNOSTICS updated_rows = ROW_COUNT;
  
  RETURN QUERY SELECT updated_rows, COALESCE(subscription_ids, ARRAY[]::text[]);
END;
$$;

-- Add comments for documentation
COMMENT ON COLUMN public.pma_subscriptions.cancelled_at IS 'Timestamp when subscription cancellation was requested';
COMMENT ON FUNCTION public.process_monthly_cancellation_suspensions IS 'Processes cancelled subscriptions on the 27th of each month, changing them to suspended status';