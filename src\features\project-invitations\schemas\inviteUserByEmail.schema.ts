import { z } from 'zod';

/**
 * Schema for creating a project invitation via email
 */
export const inviteUserByEmailSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .min(1, 'Email is required')
    .transform((email) => email.toLowerCase().trim()),
  role: z.enum(['technician'], {
    required_error: 'Role is required',
  }),
  projectId: z.string().uuid('Invalid project ID'),
  inviterId: z.string().uuid('Invalid inviter ID'),
});

export type InviteUserByEmailInput = z.infer<typeof inviteUserByEmailSchema>;

/**
 * Schema for validating invitation tokens
 */
export const invitationTokenSchema = z.object({
  token: z.string().uuid('Invalid invitation token format'),
});

export type InvitationTokenInput = z.infer<typeof invitationTokenSchema>;
