'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { TrendingDown, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';

export function StatusCard({
  title,
  value,
  description,
  icon,
  trend,
  variant = 'default',
  className,
  onClick,
}: {
  title: string;
  value: number | string;
  description: string;
  icon: React.ReactNode;
  trend?: number;
  variant: 'default' | 'success' | 'warning' | 'info';
  className?: string;
  onClick?: () => void;
}) {
  const [animatedValue, setAnimatedValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Animation for counting up numbers
  useEffect(() => {
    setIsVisible(true);
    const numericValue =
      typeof value === 'string' ? parseFloat(value.replace('%', '')) : value;
    if (typeof numericValue === 'number' && !isNaN(numericValue)) {
      const duration = 1000; // 1 second
      const steps = 30;
      const increment = numericValue / steps;
      let current = 0;

      const timer = setInterval(() => {
        current += increment;
        if (current >= numericValue) {
          setAnimatedValue(numericValue);
          clearInterval(timer);
        } else {
          setAnimatedValue(Math.floor(current));
        }
      }, duration / steps);

      return () => clearInterval(timer);
    }
  }, [value]);

  const variantStyles = {
    default:
      'bg-gradient-to-br from-red-50 to-red-100/50 border-red-200/60 hover:from-red-100 hover:to-red-200/50',
    success:
      'bg-gradient-to-br from-emerald-50 to-green-100/50 border-emerald-200/60 hover:from-emerald-100 hover:to-emerald-200/50',
    warning:
      'bg-gradient-to-br from-amber-50 to-yellow-100/50 border-amber-200/60 hover:from-amber-100 hover:to-amber-200/50',
    info: 'bg-gradient-to-br from-blue-50 to-indigo-100/50 border-blue-200/60 hover:from-blue-100 hover:to-blue-200/50',
  };

  const iconContainerStyles = {
    default: 'bg-gradient-to-br from-red-100 to-red-200 text-red-600',
    success: 'bg-gradient-to-br from-emerald-100 to-green-200 text-emerald-600',
    warning: 'bg-gradient-to-br from-amber-100 to-yellow-200 text-amber-600',
    info: 'bg-gradient-to-br from-blue-100 to-indigo-200 text-blue-600',
  };

  const progressColors = {
    default: 'bg-red-600',
    success: 'bg-emerald-600',
    warning: 'bg-amber-600',
    info: 'bg-blue-600',
  };

  const isPercentage = typeof value === 'string' && value.includes('%');
  const progressValue = isPercentage
    ? parseFloat(value.replace('%', ''))
    : undefined;

  return (
    <Card
      className={cn(
        'relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-black/5 hover:-translate-y-1 group cursor-pointer',
        'backdrop-blur-sm border border-white/20',
        variantStyles[variant],
        isVisible && 'animate-in fade-in slide-in-from-bottom-4 duration-500',
        className,
      )}
      onClick={onClick}
    >
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <CardContent className="relative p-8">
        <div className="flex items-start justify-between">
          <div className="space-y-4 flex-1">
            <div className="flex items-center gap-3">
              <div
                className={cn(
                  'p-4 rounded-xl shadow-sm transition-all duration-300 group-hover:scale-110',
                  iconContainerStyles[variant],
                )}
              >
                {icon}
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                  {title}
                </p>
                <div className="flex items-baseline gap-2">
                  <h3 className="text-3xl font-bold tracking-tight text-foreground">
                    {typeof value === 'string' && value.includes('%')
                      ? `${animatedValue}%`
                      : animatedValue}
                  </h3>
                  {trend !== undefined && (
                    <div
                      className={cn(
                        'flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium',
                        trend > 0
                          ? 'bg-emerald-100 text-emerald-700'
                          : 'bg-red-100 text-red-700',
                      )}
                    >
                      {trend > 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : (
                        <TrendingDown className="h-3 w-3" />
                      )}
                      {Math.abs(trend)}%
                    </div>
                  )}
                </div>
              </div>
            </div>

            {description && (
              <p className="text-sm text-muted-foreground font-medium">
                {description}
              </p>
            )}

            {/* Progress bar for percentage values */}
            {isPercentage && progressValue !== undefined && (
              <div className="space-y-2">
                <Progress value={animatedValue} className="h-2 bg-white/50" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0%</span>
                  <span>100%</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>

      {/* Subtle border highlight */}
      <div
        className={cn(
          'absolute inset-x-0 bottom-0 h-1 transition-all duration-300',
          progressColors[variant],
          'opacity-20 group-hover:opacity-40',
        )}
      />
    </Card>
  );
}
