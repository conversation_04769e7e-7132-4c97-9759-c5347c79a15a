-- ================================
-- PMA SUBSCRIPTION PRO-RATED BILLING SYSTEM
-- Migration to add daily pro-rated billing calculations with Supabase cron job
-- ================================

-- ================================
-- 1. ADD CALCULATED AMOUNT COLUMN
-- ================================

-- Add calculated_amount column to store daily pro-rated amounts
ALTER TABLE public.pma_subscriptions 
ADD COLUMN calculated_amount NUMERIC(10,2) DEFAULT 150.00 NOT NULL;

-- Add constraint to ensure calculated_amount is positive
ALTER TABLE public.pma_subscriptions 
ADD CONSTRAINT chk_pma_calculated_amount_positive CHECK (calculated_amount > 0);

-- Update existing records to populate calculated_amount
UPDATE public.pma_subscriptions 
SET calculated_amount = amount 
WHERE calculated_amount IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.pma_subscriptions.calculated_amount IS 'Daily calculated pro-rated amount based on current date and billing cycle, updated by cron job at 12AM';

-- ================================
-- 2. PRO-RATING CALCULATION FUNCTION
-- ================================

-- Function to calculate pro-rated amount based on subscription date and current date
CREATE OR REPLACE FUNCTION public.calculate_prorated_pma_amount(
    subscription_created_date TIMESTAMPTZ,
    calculation_date DATE DEFAULT CURRENT_DATE
) RETURNS NUMERIC(10,2) AS $$
DECLARE
    base_amount NUMERIC(10,2) := 150.00;
    current_day INTEGER;
    days_in_month INTEGER;
    days_until_27th INTEGER;
    prorated_amount NUMERIC(10,2);
BEGIN
    -- Get current day of the calculation date
    current_day := EXTRACT(DAY FROM calculation_date);
    
    -- Get total days in the current month
    days_in_month := EXTRACT(DAY FROM (
        DATE_TRUNC('MONTH', calculation_date) + INTERVAL '1 MONTH - 1 DAY'
    ));
    
    -- If subscription was created after 27th of current month or today is after 27th
    -- Return full amount for next month's billing cycle
    IF current_day > 27 OR EXTRACT(DAY FROM subscription_created_date) > 27 THEN
        RETURN base_amount;
    END IF;
    
    -- Calculate days remaining until 27th (inclusive)
    days_until_27th := 27 - current_day + 1;
    
    -- If no days left until 27th, return full amount for next cycle
    IF days_until_27th <= 0 THEN
        RETURN base_amount;
    END IF;
    
    -- Calculate pro-rated amount: (days_until_27th / days_in_month) * base_amount
    prorated_amount := ROUND(
        (days_until_27th::NUMERIC / days_in_month::NUMERIC) * base_amount, 
        2
    );
    
    -- Ensure minimum amount of RM1.00
    IF prorated_amount < 1.00 THEN
        prorated_amount := 1.00;
    END IF;
    
    RETURN prorated_amount;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add comment for documentation
COMMENT ON FUNCTION public.calculate_prorated_pma_amount IS 'Calculates pro-rated PMA subscription amount based on days remaining until 27th of month. Returns full amount (150.00) if after 27th or subscription created after 27th.';

-- ================================
-- 3. DAILY UPDATE FUNCTION
-- ================================

-- Function to update all pending payment subscriptions with pro-rated amounts
CREATE OR REPLACE FUNCTION public.update_prorated_pma_amounts() 
RETURNS TABLE(
    updated_count INTEGER,
    execution_time TIMESTAMPTZ,
    details TEXT
) AS $$
DECLARE
    update_count INTEGER := 0;
    start_time TIMESTAMPTZ := CURRENT_TIMESTAMP;
BEGIN
    -- Update calculated_amount for all pending_payment subscriptions
    UPDATE public.pma_subscriptions 
    SET 
        calculated_amount = public.calculate_prorated_pma_amount(created_at, CURRENT_DATE),
        updated_at = CURRENT_TIMESTAMP
    WHERE status = 'pending_payment';
    
    -- Get count of updated records
    GET DIAGNOSTICS update_count = ROW_COUNT;
    
    -- Return execution summary
    RETURN QUERY SELECT 
        update_count,
        start_time,
        CASE 
            WHEN update_count = 0 THEN 'No pending payment subscriptions found'
            ELSE format('Updated %s pending payment subscriptions with pro-rated amounts', update_count)
        END;
END;
$$ LANGUAGE plpgsql;

-- Add comment for documentation
COMMENT ON FUNCTION public.update_prorated_pma_amounts IS 'Daily function to update calculated_amount for all pending_payment PMA subscriptions. Called by cron job at 12:00 AM.';

-- ================================
-- 4. ENABLE PG_CRON EXTENSION
-- ================================

-- Enable pg_cron extension for scheduled jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- ================================
-- 5. SCHEDULE DAILY CRON JOB
-- ================================

-- Schedule daily pro-rate calculation at 12:00 AM (00:00) every day
SELECT cron.schedule(
    'pma-prorated-daily-update',
    '0 0 * * *',
    $$
    SELECT public.update_prorated_pma_amounts();
    $$
);

-- ================================
-- 6. ADD PERFORMANCE INDEXES
-- ================================

-- Index for calculated_amount queries
CREATE INDEX idx_pma_subscriptions_calculated_amount 
    ON public.pma_subscriptions(calculated_amount) 
    WHERE status = 'pending_payment';

-- Index for status and calculated_amount combination
CREATE INDEX idx_pma_subscriptions_status_calculated 
    ON public.pma_subscriptions(status, calculated_amount);

-- ================================
-- 7. CREATE CRON JOB MONITORING FUNCTION
-- ================================

-- Function to check cron job execution status
CREATE OR REPLACE FUNCTION public.get_pma_cron_job_status()
RETURNS TABLE(
    job_name TEXT,
    schedule TEXT,
    active BOOLEAN,
    last_run TIMESTAMPTZ,
    last_run_status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        j.jobname::TEXT,
        j.schedule::TEXT,
        j.active,
        MAX(jr.start_time) as last_run,
        CASE 
            WHEN MAX(jr.return_message) IS NULL THEN 'No runs yet'
            WHEN MAX(jr.return_message) LIKE '%ERROR%' THEN 'Failed'
            ELSE 'Success'
        END as last_run_status
    FROM cron.job j
    LEFT JOIN cron.job_run_details jr ON j.jobid = jr.jobid
    WHERE j.jobname = 'pma-prorated-daily-update'
    GROUP BY j.jobname, j.schedule, j.active;
END;
$$ LANGUAGE plpgsql;

-- Add comment for documentation
COMMENT ON FUNCTION public.get_pma_cron_job_status IS 'Monitor the status and execution history of the PMA pro-rated billing cron job';

-- ================================
-- 8. INITIAL DATA POPULATION
-- ================================

-- Run initial calculation for all existing pending_payment subscriptions
SELECT public.update_prorated_pma_amounts();

-- ================================
-- PRO-RATED BILLING SYSTEM COMPLETE
-- Migration adds:
-- 1. calculated_amount column for daily pro-rated amounts
-- 2. Pro-rating calculation function with 27th billing cycle logic
-- 3. Daily update function for batch processing
-- 4. Supabase cron job scheduled at 12:00 AM daily
-- 5. Performance indexes for efficient queries
-- 6. Monitoring functions for system health
-- 7. Initial data population for existing subscriptions
-- ================================