import { BillPlzClient } from '@/lib/billplz';
import { supabase } from '@/lib/supabase';
import type { BillPlzBill } from '@/types/billing';
import { convertMyrToCents } from '@/types/billing';
import type { Database, Json } from '@/types/database';
import type { BulkPaymentRequest } from '../types/contractor-bulk-payment';

export interface BulkPaymentResult {
  success: boolean;
  data?: {
    payment_url?: string; // Optional for direct card payments
    billplz_bill_id?: string; // Optional for direct card payments
    charge_id?: string; // For direct card charges
    payment_record_ids: string[];
    total_amount: number;
    processed_subscriptions: string[];
    payment_method: 'billplz_redirect' | 'card_charge';
  };
  error?: string;
  details?: unknown;
}

export interface CardTokenizationResult {
  success: boolean;
  data?: {
    token: string;
    fingerprint: string;
    last_four: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  };
  error?: string;
}

export interface CardCreationResult {
  success: boolean;
  data?: {
    card_id: string;
    redirect_url: string;
  };
  error?: string;
}

export interface SavedCardInfo {
  id: string;
  card_token: string;
  last_four: string;
  brand: string;
  exp_month: number;
  exp_year: number;
  is_default: boolean;
}

export interface ContractorInfo {
  contractor_id: string;
  contractor_name: string;
  user_email: string;
  user_name: string;
  user_phone?: string;
}

type PmaPaymentRecord =
  Database['public']['Tables']['pma_payment_records']['Row'];
type PmaPaymentRecordInsert =
  Database['public']['Tables']['pma_payment_records']['Insert'];
type ContractorPaymentMethodInsert =
  Database['public']['Tables']['contractor_payment_methods']['Insert'];

export class BulkPaymentService {
  private static billPlzClient: BillPlzClient | null = null;

  private static getBillPlzClient(): BillPlzClient {
    if (typeof window !== 'undefined') {
      throw new Error(
        'BulkPaymentService can only be used server-side. Use API routes for client-side operations.',
      );
    }

    if (!this.billPlzClient) {
      this.billPlzClient = new BillPlzClient();
    }
    return this.billPlzClient;
  }

  static async createConsolidatedPayment(
    request: BulkPaymentRequest,
  ): Promise<BulkPaymentResult> {
    try {
      const {
        contractor_id,
        subscription_ids,
        total_amount,
        payment_method,
        card_token,
        new_card_data,
      } = request;

      // 1. Fetch contractor and user information
      const contractorInfo = await this.getContractorInfo(contractor_id);

      if (!contractorInfo) {
        return {
          success: false,
          error: 'Contractor information not found',
        };
      }

      // 2. Validate subscription IDs belong to contractor and get subscription details
      const subscriptions = await this.validateAndGetSubscriptions(
        contractor_id,
        subscription_ids,
      );
      if (!subscriptions.success) {
        return {
          success: false,
          error: subscriptions.error,
          details: subscriptions.details,
        };
      }

      // 3. Verify total amount matches sum of subscription calculated amounts (pro-rated)
      const calculatedTotal = subscriptions.data!.reduce(
        (sum, sub) => sum + sub.calculated_amount,
        0,
      );
      if (Math.abs(calculatedTotal - total_amount) > 0.01) {
        return {
          success: false,
          error: `Amount mismatch. Expected: ${calculatedTotal}, Received: ${total_amount}`,
        };
      }

      // 4. Handle payment based on method
      if (payment_method === 'saved_card' && card_token) {
        return this.processCardPayment({
          contractor_info: contractorInfo,
          subscription_ids,
          subscriptions: subscriptions.data!,
          total_amount,
          card_token,
        });
      } else if (payment_method === 'new_card' && new_card_data) {
        return this.processNewCardPayment({
          contractor_info: contractorInfo,
          subscription_ids,
          subscriptions: subscriptions.data!,
          total_amount,
          new_card_data,
        });
      } else {
        return this.processBillPlzPayment({
          contractor_info: contractorInfo,
          subscription_ids,
          subscriptions: subscriptions.data!,
          total_amount,
        });
      }
    } catch (error) {
      console.error('Bulk payment creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Process payment using BillPlz redirect method
   */
  private static async processBillPlzPayment({
    contractor_info,
    subscription_ids,
    subscriptions,
    total_amount,
  }: {
    contractor_info: ContractorInfo;
    subscription_ids: string[];
    subscriptions: Array<{
      id: string;
      amount: number;
      calculated_amount: number;
    }>;
    total_amount: number;
  }): Promise<BulkPaymentResult> {
    try {
      // Create consolidated BillPlz bill
      const billResult = await this.createBillPlzBill(
        contractor_info,
        total_amount,
        subscription_ids,
      );
      if (!billResult.success) {
        return {
          success: false,
          error: billResult.error,
        };
      }

      // Create individual payment records for each subscription
      const paymentRecordsResult = await this.createPaymentRecords(
        subscription_ids,
        subscriptions,
        billResult.data!.id,
      );
      if (!paymentRecordsResult.success) {
        return {
          success: false,
          error: paymentRecordsResult.error,
        };
      }

      return {
        success: true,
        data: {
          payment_url: billResult.data!.url,
          billplz_bill_id: billResult.data!.id,
          payment_record_ids: paymentRecordsResult.data!,
          total_amount,
          processed_subscriptions: subscription_ids,
          payment_method: 'billplz_redirect',
        },
      };
    } catch (error) {
      console.error('BillPlz payment processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Process payment using saved card token
   */
  private static async processCardPayment({
    contractor_info,
    subscription_ids,
    subscriptions,
    total_amount,
    card_token,
  }: {
    contractor_info: ContractorInfo;
    subscription_ids: string[];
    subscriptions: Array<{
      id: string;
      amount: number;
      calculated_amount: number;
    }>;
    total_amount: number;
    card_token: string;
  }): Promise<BulkPaymentResult> {
    try {
      // Charge the card directly using BillPlz tokenization API
      const chargeResult = await this.getBillPlzClient().chargeCard({
        token: card_token,
        amount: total_amount,
        description: `Consolidated payment for ${subscription_ids.length} PMA subscription${
          subscription_ids.length > 1 ? 's' : ''
        }`,
        email: contractor_info.user_email,
        name: contractor_info.user_name,
        phone: contractor_info.user_phone,
      });

      if (!chargeResult.success || !chargeResult.data) {
        return {
          success: false,
          error: chargeResult.error || 'Failed to charge card',
        };
      }

      // Create payment records with charge ID instead of bill ID
      const paymentRecordsResult = await this.createPaymentRecords(
        subscription_ids,
        subscriptions,
        chargeResult.data.id, // Use charge ID as reference
      );

      if (!paymentRecordsResult.success) {
        return {
          success: false,
          error: paymentRecordsResult.error,
        };
      }

      // For successful card charges, immediately update payment status
      await this.updatePaymentRecordsStatus(
        chargeResult.data.id,
        'paid',
        chargeResult.data as unknown as BillPlzBill,
        new Date().toISOString(),
      );

      return {
        success: true,
        data: {
          charge_id: chargeResult.data.id,
          payment_record_ids: paymentRecordsResult.data!,
          total_amount,
          processed_subscriptions: subscription_ids,
          payment_method: 'card_charge',
        },
      };
    } catch (error) {
      console.error('Card payment processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Process payment using new card data (tokenize and charge)
   */
  private static async processNewCardPayment({
    contractor_info: _contractor_info,
    subscription_ids: _subscription_ids,
    subscriptions: _subscriptions,
    total_amount: _total_amount,
    new_card_data: _new_card_data,
  }: {
    contractor_info: ContractorInfo;
    subscription_ids: string[];
    subscriptions: Array<{
      id: string;
      amount: number;
      calculated_amount: number;
    }>;
    total_amount: number;
    new_card_data: {
      number: string;
      exp_month: number;
      exp_year: number;
      cvc: string;
      name: string;
      save_card?: boolean;
    };
  }): Promise<BulkPaymentResult> {
    try {
      // BillPlz V4 requires 3DS flow - direct tokenization not supported
      return {
        success: false,
        error:
          'Direct card processing not supported. Please use the 3DS flow via saved cards instead.',
      };
    } catch (error) {
      console.error('New card payment processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create a card for 3DS authentication (Step 1 of tokenization flow)
   * Returns redirect URL for user to complete 3DS authentication
   */
  static async createCard({
    email,
    name,
    callback_url,
    redirect_url,
    phone,
  }: {
    email: string;
    name: string;
    callback_url: string;
    redirect_url?: string;
    phone: string;
  }): Promise<CardCreationResult> {
    try {
      const createResult = await this.getBillPlzClient().createCard({
        email,
        name,
        callback_url,
        redirect_url,
        phone,
      });

      if (!createResult.success || !createResult.data) {
        return {
          success: false,
          error: createResult.error || 'Failed to create card',
        };
      }

      return {
        success: true,
        data: {
          card_id: createResult.data.id,
          redirect_url: createResult.data.url,
        },
      };
    } catch (error) {
      console.error('Card creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Save a tokenized card for a contractor
   */
  static async saveCardForContractor({
    contractor_id,
    token,
    fingerprint,
    last_four,
    brand,
    exp_month,
    exp_year,
    is_default = false,
  }: {
    contractor_id: string;
    token: string;
    fingerprint: string;
    last_four: string;
    brand: string;
    exp_month: number;
    exp_year: number;
    is_default?: boolean;
  }): Promise<{ success: boolean; card_id?: string; error?: string }> {
    try {
      // If this is the default card, unset all other defaults for this contractor
      if (is_default) {
        await supabase
          .from('contractor_payment_methods')
          .update({ is_default: false })
          .eq('contractor_id', contractor_id);
      }

      const cardData: ContractorPaymentMethodInsert = {
        contractor_id,
        card_token: token,
        card_fingerprint: fingerprint,
        last_four,
        brand,
        exp_month,
        exp_year,
        is_default,
      };

      const { data: savedCard, error } = await supabase
        .from('contractor_payment_methods')
        .insert(cardData)
        .select('id')
        .single();

      if (error || !savedCard) {
        return {
          success: false,
          error: error?.message || 'Failed to save card',
        };
      }

      return {
        success: true,
        card_id: savedCard.id,
      };
    } catch (error) {
      console.error('Error saving card for contractor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get saved cards for a contractor
   */
  static async getSavedCards(contractor_id: string): Promise<SavedCardInfo[]> {
    try {
      const { data: cards, error } = await supabase
        .from('contractor_payment_methods')
        .select('*')
        .eq('contractor_id', contractor_id)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Failed to fetch saved cards:', error);
        return [];
      }

      return (cards || []).map(
        (card): SavedCardInfo => ({
          id: card.id,
          card_token: card.card_token,
          last_four: card.last_four,
          brand: card.brand,
          exp_month: card.exp_month,
          exp_year: card.exp_year,
          is_default: card.is_default ?? false,
        }),
      );
    } catch (error) {
      console.error('Error fetching saved cards:', error);
      return [];
    }
  }

  /**
   * Delete a saved card for a contractor
   */
  static async deleteSavedCard({
    contractor_id,
    card_id,
  }: {
    contractor_id: string;
    card_id: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('contractor_payment_methods')
        .delete()
        .eq('id', card_id)
        .eq('contractor_id', contractor_id); // Ensure contractor owns the card

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Error deleting saved card:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Set a card as the default payment method
   */
  static async setDefaultCard({
    contractor_id,
    card_id,
  }: {
    contractor_id: string;
    card_id: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      // First, unset all defaults for this contractor
      await supabase
        .from('contractor_payment_methods')
        .update({ is_default: false })
        .eq('contractor_id', contractor_id);

      // Then set the specified card as default
      const { error } = await supabase
        .from('contractor_payment_methods')
        .update({ is_default: true })
        .eq('id', card_id)
        .eq('contractor_id', contractor_id); // Ensure contractor owns the card

      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Error setting default card:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private static async getContractorInfo(
    contractor_id: string,
  ): Promise<ContractorInfo | null> {
    try {
      // First get contractor info
      const { data: contractor, error: contractorError } = await supabase
        .from('contractors')
        .select('id, name')
        .eq('id', contractor_id)
        .single();

      if (contractorError || !contractor) {
        console.error(
          'Failed to fetch contractor:',
          contractorError?.message || 'No data',
        );
        return null;
      }

      // Then get user info for this contractor
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('email, name, phone_number')
        .eq('contractor_id', contractor_id)
        .single();

      if (userError || !user) {
        console.error(
          'Failed to fetch user for contractor:',
          userError?.message || 'No user found',
        );
        return null;
      }

      return {
        contractor_id: contractor.id,
        contractor_name: contractor.name,
        user_email: user.email,
        user_name: user.name,
        user_phone: user.phone_number || undefined,
      };
    } catch (error) {
      console.error('Error fetching contractor info:', error);
      return null;
    }
  }

  private static async validateAndGetSubscriptions(
    contractor_id: string,
    subscription_ids: string[],
  ): Promise<{
    success: boolean;
    data?: Array<{ id: string; amount: number; calculated_amount: number }>;
    error?: string;
    details?: unknown;
  }> {
    try {
      const { data: subscriptions, error } = await supabase
        .from('pma_subscriptions')
        .select('id, amount, calculated_amount, contractor_id, status')
        .in('id', subscription_ids)
        .eq('contractor_id', contractor_id);

      if (error) {
        return {
          success: false,
          error: 'Failed to fetch subscriptions',
          details: error,
        };
      }

      if (!subscriptions || subscriptions.length !== subscription_ids.length) {
        const foundIds = subscriptions?.map((s) => s.id) || [];
        const missingIds = subscription_ids.filter(
          (id) => !foundIds.includes(id),
        );
        const wrongContractor =
          subscriptions?.filter((s) => s.contractor_id !== contractor_id) || [];

        return {
          success: false,
          error: 'Some subscriptions not found or do not belong to contractor',
          details: {
            requested: subscription_ids,
            found: foundIds,
            missing: missingIds,
            wrongContractor: wrongContractor.map((s) => ({
              id: s.id,
              contractor_id: s.contractor_id,
            })),
          },
        };
      }

      // Check if all subscriptions require payment
      const invalidSubscriptions = subscriptions.filter(
        (sub) =>
          !['pending_payment', 'grace_period', 'suspended'].includes(
            sub.status,
          ),
      );

      if (invalidSubscriptions.length > 0) {
        return {
          success: false,
          error: 'Some subscriptions do not require payment',
          details: invalidSubscriptions.map((s) => ({
            id: s.id,
            status: s.status,
          })),
        };
      }

      return {
        success: true,
        data: subscriptions.map((sub) => ({
          id: sub.id,
          amount: sub.amount,
          calculated_amount: sub.calculated_amount,
        })),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private static async createBillPlzBill(
    contractorInfo: ContractorInfo,
    totalAmount: number,
    subscriptionIds: string[],
  ): Promise<{
    success: boolean;
    data?: BillPlzBill;
    error?: string;
  }> {
    try {
      const description = `Consolidated payment for ${subscriptionIds.length} PMA subscription${
        subscriptionIds.length > 1 ? 's' : ''
      }`;

      const billResult = await this.getBillPlzClient().createBill({
        contractorEmail: contractorInfo.user_email,
        contractorName: contractorInfo.user_name,
        contractorMobile: contractorInfo.user_phone,
        amount: totalAmount,
        description,
        contractorId: contractorInfo.contractor_id,
        callbackUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/api/billing/webhooks/billplz`,
        redirectUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/billing/payment-success`,
      });

      if (!billResult.success || !billResult.data) {
        return {
          success: false,
          error: billResult.error || 'Failed to create BillPlz bill',
        };
      }

      return {
        success: true,
        data: billResult.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private static async createPaymentRecords(
    subscriptionIds: string[],
    subscriptions: Array<{
      id: string;
      amount: number;
      calculated_amount: number;
    }>,
    billplzBillId: string,
  ): Promise<{
    success: boolean;
    data?: string[];
    error?: string;
  }> {
    try {
      // Prepare payment records data
      const paymentRecordsData: PmaPaymentRecordInsert[] = subscriptions.map(
        (subscription) => ({
          pma_subscription_id: subscription.id,
          billplz_bill_id: billplzBillId,
          amount: convertMyrToCents(subscription.calculated_amount),
          status: 'pending',
          created_at: new Date().toISOString(),
        }),
      );

      // Insert all payment records in a single transaction
      const { data: paymentRecords, error } = await supabase
        .from('pma_payment_records')
        .insert(paymentRecordsData)
        .select('id');

      if (error || !paymentRecords) {
        console.error('Failed to create payment records:', error);
        return {
          success: false,
          error: error?.message || 'Failed to create payment records',
        };
      }

      return {
        success: true,
        data: paymentRecords.map((record) => record.id),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get payment records by BillPlz bill ID (for webhook processing)
   */
  static async getPaymentRecordsByBillId(
    billplzBillId: string,
  ): Promise<PmaPaymentRecord[]> {
    try {
      const { data: paymentRecords, error } = await supabase
        .from('pma_payment_records')
        .select('*')
        .eq('billplz_bill_id', billplzBillId);

      if (error) {
        console.error('Failed to fetch payment records by bill ID:', error);
        return [];
      }

      return paymentRecords || [];
    } catch (error) {
      console.error('Error fetching payment records by bill ID:', error);
      return [];
    }
  }

  /**
   * Update multiple payment records status (for webhook processing)
   */
  static async updatePaymentRecordsStatus(
    billplzBillId: string,
    status: 'paid' | 'failed',
    billPlzData?: BillPlzBill,
    paidAt?: string,
    failureReason?: string,
  ): Promise<{ success: boolean; updatedCount: number }> {
    try {
      const updateData: Partial<PmaPaymentRecord> = {
        status,
        ...(billPlzData && {
          billplz_response: billPlzData as unknown as Json,
        }),
        ...(paidAt && { paid_at: paidAt }),
        ...(failureReason && { failure_reason: failureReason }),
      };

      const { data, error } = await supabase
        .from('pma_payment_records')
        .update(updateData)
        .eq('billplz_bill_id', billplzBillId)
        .select('id');

      if (error) {
        console.error('Failed to update payment records:', error);
        return { success: false, updatedCount: 0 };
      }

      return { success: true, updatedCount: data?.length || 0 };
    } catch (error) {
      console.error('Error updating payment records:', error);
      return { success: false, updatedCount: 0 };
    }
  }
}
