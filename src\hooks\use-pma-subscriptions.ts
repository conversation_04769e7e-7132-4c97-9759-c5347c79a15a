import {
  checkMultiplePmaSubscriptionAccess,
  checkPmaSubscriptionAccess,
  type SubscriptionCheckResult,
} from '@/lib/subscription-utils';
import { useQuery } from '@tanstack/react-query';

/**
 * Hook to check subscription access for a single PMA certificate
 */
export function usePmaSubscriptionAccess(pmaId: string | null) {
  return useQuery({
    queryKey: ['pma-subscription-access', pmaId],
    queryFn: async (): Promise<SubscriptionCheckResult | null> => {
      if (!pmaId) return null;
      return checkPmaSubscriptionAccess(pmaId);
    },
    enabled: !!pmaId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
  });
}

/**
 * Hook to check subscription access for multiple PMA certificates
 */
export function useMultiplePmaSubscriptionAccess(pmaIds: string[]) {
  return useQuery({
    queryKey: ['multiple-pma-subscription-access', pmaIds.sort()],
    queryFn: async (): Promise<Record<string, SubscriptionCheckResult>> => {
      if (!pmaIds.length) return {};
      return checkMultiplePmaSubscriptionAccess(pmaIds);
    },
    enabled: pmaIds.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
  });
}
