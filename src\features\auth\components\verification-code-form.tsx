'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  useAuthTranslations,
  useValidationTranslations,
} from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { CheckCircle, KeyRound } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import * as React from 'react';
import { Suspense, useState } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { toast } from 'sonner';
import { usePasswordReset, useVerifyOtp } from '../hooks/use-auth';
import {
  createVerificationCodeSchema,
  type VerificationCodeFormValues,
} from '../schemas';

interface VerificationCodeFormProps
  extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

function VerificationCodeFormContent({
  className,
  ...props
}: VerificationCodeFormProps) {
  const [isSuccess, setIsSuccess] = useState(false);
  const searchParams = useSearchParams();
  const verifyOtpMutation = useVerifyOtp();
  const passwordResetMutation = usePasswordReset();
  const auth = useAuthTranslations();
  const validation = useValidationTranslations();

  // Get email from URL params if provided
  const emailFromParams = searchParams?.get('email') || '';

  const formSchema = createVerificationCodeSchema(validation, auth);

  type FormValues = VerificationCodeFormValues;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: emailFromParams,
      code: '',
    },
  });

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading(auth('verifyingCode'));

      await verifyOtpMutation.mutateAsync({
        email: values.email,
        code: values.code,
      });

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success(auth('codeVerified'));
      setIsSuccess(true);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : auth('codeVerificationFailed');

      toast.error(errorMessage);
    }
  };

  const handleResendCode = async () => {
    const email = form.getValues('email');
    if (!email) {
      toast.error(validation('required', { field: auth('email') }));
      return;
    }

    try {
      const loadingToast = toast.loading(auth('sendingResetLink'));
      await passwordResetMutation.mutateAsync(email);
      toast.dismiss(loadingToast);
      toast.success(auth('resetLinkSent'));
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : auth('resetLinkFailed');
      toast.error(errorMessage);
    }
  };

  if (isSuccess) {
    return (
      <div className={cn('flex flex-col gap-6', className)} {...props}>
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-bold">{auth('codeVerified')}</h1>
            <p className="text-muted-foreground text-sm text-balance">
              Redirecting to password reset...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <div className="flex flex-col items-center gap-4 text-center">
        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
          <KeyRound className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">{auth('verifyCodeTitle')}</h1>
          <p className="text-muted-foreground text-sm text-balance">
            {auth('verifyCodeSubtitle')}
          </p>
        </div>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="grid gap-3">
                  <FormLabel>{auth('email')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('emailPlaceholder')}
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem className="grid gap-3">
                  <FormLabel>{auth('verificationCode')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('codePlaceholder')}
                      maxLength={6}
                      className="text-center text-lg tracking-wider font-mono"
                      {...field}
                      onChange={(e) => {
                        // Only allow numbers and limit to 6 digits
                        const value = e.target.value
                          .replace(/\D/g, '')
                          .slice(0, 6);
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="w-full"
              disabled={verifyOtpMutation.isPending}
            >
              {verifyOtpMutation.isPending
                ? auth('verifyingCode')
                : auth('verifyCode')}
            </Button>
          </div>
          <div className="text-center space-y-4">
            <div className="text-sm">
              <span className="text-muted-foreground">
                {auth('didNotReceiveCode')}{' '}
              </span>
              <button
                type="button"
                onClick={handleResendCode}
                disabled={passwordResetMutation.isPending}
                className="text-primary underline underline-offset-4 hover:no-underline disabled:opacity-50"
              >
                {passwordResetMutation.isPending
                  ? auth('sendingResetLink')
                  : auth('resendCode')}
              </button>
            </div>
            <div className="text-sm">
              <Link
                href="/admin/login"
                className="underline underline-offset-4 hover:no-underline"
              >
                {auth('backToLogin')}
              </Link>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}

export function VerificationCodeForm({
  className,
  ...props
}: VerificationCodeFormProps) {
  return (
    <Suspense
      fallback={
        <div className={cn('flex flex-col gap-6', className)} {...props}>
          <div className="flex flex-col items-center gap-4 text-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
              <KeyRound className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">Loading...</h1>
              <p className="text-muted-foreground text-sm text-balance">
                Preparing verification form...
              </p>
            </div>
          </div>
        </div>
      }
    >
      <VerificationCodeFormContent className={className} {...props} />
    </Suspense>
  );
}
