-- ================================
-- FIX INFINITE RECURSION IN RLS POLICIES - FINAL FIX
-- ================================

-- Create a function to safely check project membership without RLS recursion
CREATE OR REPLACE FUNCTION get_user_project_ids(user_uuid uuid)
RETURNS TABLE(project_id uuid)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Return project IDs where the user is an active member
  -- This function bypasses <PERSON><PERSON> to avoid recursion
  RETURN QUERY
  SELECT pu.project_id
  FROM project_users pu
  WHERE pu.user_id = user_uuid 
  AND pu.is_active = true
  AND pu.status = 'accepted'
  AND pu.deleted_at IS NULL;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_project_ids(uuid) TO authenticated;

-- ================================
-- CREATE HELPER FUNCTION FOR APPLICATION LAYER
-- ================================

-- Create a function to check if a user can access a specific project
-- This includes membership check and can be used by the application
CREATE OR REPLACE FUNCTION can_user_access_project(project_uuid uuid, user_uuid uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_contractor_id uuid;
  user_role_val user_role;
  user_admin_mode admin_access_mode;
  user_monitoring_state state_code;
  project_contractor_id uuid;
  project_state_val state_code;
BEGIN
  -- Get user details
  SELECT u.contractor_id, u.user_role, u.admin_access_mode, u.monitoring_state
  INTO user_contractor_id, user_role_val, user_admin_mode, user_monitoring_state
  FROM users u 
  WHERE u.id = user_uuid;
  
  -- Get project details
  SELECT p.contractor_id, p.state
  INTO project_contractor_id, project_state_val
  FROM projects p 
  WHERE p.id = project_uuid;
  
  -- Check if user is contractor owner
  IF user_role_val = 'contractor' AND user_contractor_id = project_contractor_id THEN
    RETURN true;
  END IF;
  
  -- Check if user is state admin for this project's state
  IF user_role_val = 'admin' AND user_admin_mode = 'state' AND user_monitoring_state = project_state_val THEN
    RETURN true;
  END IF;
  
  -- Check if user is project-level admin
  IF user_role_val = 'admin' AND user_admin_mode = 'project' THEN
    RETURN true;
  END IF;
  
  -- Check if user is a member of the project
  IF EXISTS (
    SELECT 1 FROM project_users pu
    WHERE pu.project_id = project_uuid 
    AND pu.user_id = user_uuid
    AND pu.is_active = true
    AND pu.status = 'accepted'
    AND pu.deleted_at IS NULL
  ) THEN
    RETURN true;
  END IF;
  
  RETURN false;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION can_user_access_project(uuid, uuid) TO authenticated;

-- ================================
-- ADD COMMENTS FOR DOCUMENTATION
-- ================================

COMMENT ON FUNCTION get_user_project_ids(uuid) IS 'Returns project IDs that a user is an active member of, bypassing RLS to avoid recursion';
COMMENT ON FUNCTION can_user_access_project(uuid, uuid) IS 'Checks if a user can access a specific project through any access method (ownership, admin rights, or membership)';
