'use client';

import { supabase } from '@/lib/supabase';
import { addDays, format, subDays } from 'date-fns';

// Type definitions for the return types
export interface PMACertificateStats {
  statusDistribution: Array<{ name: string; value: number; color: string }>;
  problematicLifts: Array<{
    location: string;
    cost: number;
    hours: string;
    incidents: number;
    pmaNumber: string;
  }>;
  total: number;
  validCount: number;
  expiredCount: number;
  expiringSoonCount: number;
}

export interface MaintenanceLogStats {
  statusDistribution: Array<{ name: string; value: number; color: string }>;
  byType: Array<{ name: string; count: number }>;
  total: number;
  completedCount: number;
  pendingCount: number;
  overdueCount: number;
  completedMaintenance: number; // PMA certs with any maintenance log for today
  totalMaintenance: number; // Total PMA certificates count
  overdueMaintenance: number; // Total maintenance logs for the current day
  pmaWithoutLogsToday: number; // PMA certs without maintenance logs for today
  logsAfter5pmSinceStart: number; // Total maintenance logs since project start
  maintenanceCalendarData: Array<{
    date: string;
    missingLogs: Array<{ pmaNumber: string; status: string }>;
    lateLogs: Array<{ pmaNumber: string; status: string }>;
  }>; // Calendar data for maintenance issues
}

export interface ComplaintStats {
  statusDistribution: Array<{ name: string; value: number; color: string }>;
  byCategory: Array<{ name: string; count: number }>;
  monthlyTrend: Array<{ month: string; received: number; resolved: number }>;
  responseTime: Array<{ category: string; avgTime: number }>;
  recent: Array<{
    id: string;
    title: string;
    category: string;
    status: string;
    date: string | null;
  }>;
  trendAnalysis: Array<{ month: string; total: number }>;
  trendGrowthRate: number;
  total: number;
  newCount: number;
  inProgressCount: number;
  resolvedCount: number;
  closedCount: number;
  openCount: number;
  resolutionRate: number;
}

export interface SafetyIncidentStats {
  last30Days: number;
  trend: Array<{ month: string; count: number }>;
}

export interface ComplianceScore {
  score: number;
  pmaCompliance: number;
  maintenanceCompliance: number;
}

/**
 * Dashboard service for retrieving data for the dashboard
 */
export const DashboardService = {
  /**
   * Get PMA certificates stats
   * @param projectId Optional project ID to filter by
   * @returns PMA certificates stats
   */
  async getPMACertificatesStats(
    projectId?: string,
  ): Promise<PMACertificateStats> {
    try {
      const now = new Date();

      // 1) Fetch everything in one go using type from database schema
      let q = supabase.from('pma_certificates').select(`
          id,
          expiry_date,
          status,
          pma_number,
          location,
          total_repair_cost,
          total_repair_time,
          created_at
        `);

      if (projectId) q = q.eq('project_id', projectId);

      const { data: allCerts = [], error } = await q;
      if (error) throw error;

      if (!allCerts || allCerts.length === 0) {
        return {
          statusDistribution: [
            { name: 'Valid', value: 0, color: '#A7F3D0' },
            { name: 'Expired', value: 0, color: '#FECACA' },
            { name: 'Expiring Soon', value: 0, color: '#FEF3C7' },
          ],
          problematicLifts: [],
          total: 0,
          validCount: 0,
          expiredCount: 0,
          expiringSoonCount: 0,
        };
      }

      // 2) Expiry buckets
      const validCount = allCerts.filter(
        (c) => c.expiry_date && new Date(c.expiry_date) > addDays(now, 30),
      ).length;

      const expiringSoonCount = allCerts.filter(
        (c) =>
          c.expiry_date &&
          new Date(c.expiry_date) <= addDays(now, 30) &&
          new Date(c.expiry_date) >= now,
      ).length;

      const expiredCount = allCerts.filter(
        (c) => c.expiry_date && new Date(c.expiry_date) < now,
      ).length;

      // 3) Top-3 problematic lifts
      const problematicLifts = allCerts
        .map((c) => {
          const cost =
            typeof c.total_repair_cost === 'number'
              ? c.total_repair_cost
              : parseFloat(c.total_repair_cost as unknown as string) || 0;

          let hours = '00:00:00';
          if (c.total_repair_time) {
            if (typeof c.total_repair_time === 'string') {
              hours = c.total_repair_time;
            } else if (typeof c.total_repair_time === 'number') {
              const h = Math.floor(c.total_repair_time);
              const m = Math.floor((c.total_repair_time - h) * 60);
              const s = Math.floor(((c.total_repair_time - h) * 60 - m) * 60);
              hours = `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}:${String(s).padStart(2, '0')}`;
            }
          }

          return {
            location: c.location || 'Unknown Location',
            cost,
            hours,
            incidents: 0,
            pmaNumber: c.pma_number || 'N/A',
          };
        })
        .filter((lift) => lift.cost > 0)
        .sort((a, b) => b.cost - a.cost)
        .slice(0, 3);

      // 5) Assemble and return
      return {
        statusDistribution: [
          { name: 'Valid', value: validCount, color: '#A7F3D0' },
          { name: 'Expired', value: expiredCount, color: '#FECACA' },
          { name: 'Expiring Soon', value: expiringSoonCount, color: '#FEF3C7' },
        ],
        problematicLifts,
        total: allCerts.length,
        validCount,
        expiredCount,
        expiringSoonCount,
      };
    } catch (err) {
      console.error('Error in getPMACertificatesStats:', err);
      return {
        statusDistribution: [
          { name: 'Valid', value: 0, color: '#4CAF50' },
          { name: 'Expired', value: 0, color: '#F44336' },
          { name: 'Expiring Soon', value: 0, color: '#FF9800' },
        ],
        problematicLifts: [],
        total: 0,
        validCount: 0,
        expiredCount: 0,
        expiringSoonCount: 0,
      };
    }
  },

  /**
   * Get maintenance logs stats
   * @param projectId Optional project ID to filter by
   * @returns Maintenance logs stats
   */
  async getMaintenanceLogsStats(
    projectId?: string,
  ): Promise<MaintenanceLogStats> {
    try {
      console.log('Getting maintenance logs stats for project:', projectId);
      // Define the shape of the maintenance logs data we expect
      interface MaintenanceLogData {
        id: string;
        status?: string;
        operation_type?: string;
        operation_log_type?: string;
        description?: string;
        log_date?: string;
        created_at?: string;
        pma_id?: string;
        created_at_time?: string;
      }

      // Base query builder
      let query = supabase
        .from('maintenance_logs')
        .select(
          'id, status, operation_type, operation_log_type, description, log_date, created_at, pma_id',
        );

      // Add project filter if provided
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      // Execute query
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching maintenance logs:', error);
        throw error;
      }

      // Cast data to expected shape
      const logs = (data || []) as unknown as MaintenanceLogData[];
      console.log(`Retrieved ${logs.length} maintenance logs`);

      // Calculate statistics
      const completed = logs.filter((log) => log.status === 'completed').length;

      const pending = logs.filter(
        (log) => log.status === 'pending' || log.status === 'in_progress',
      ).length;

      const now = new Date();
      const overdue = logs.filter(
        (log) =>
          (log.status === 'pending' || log.status === 'in_progress') &&
          log.log_date &&
          new Date(log.log_date) < now,
      ).length;

      console.log('Maintenance logs status counts:', {
        completed,
        pending,
        overdue,
      });

      // Get maintenance types
      const typesMap = new Map<string, number>();
      logs.forEach((log) => {
        if (log.operation_type) {
          const count = typesMap.get(log.operation_type) || 0;
          typesMap.set(log.operation_type, count + 1);
        }
      });

      const types = Array.from(typesMap.entries()).map(([name, count]) => ({
        name,
        count,
      }));

      // Monthly trend data has been removed as per request

      // Fetch PMA certificates to calculate maintenance metrics
      let pmaQuery = supabase
        .from('pma_certificates')
        .select('id, status')
        .gte('expiry_date', format(now, 'yyyy-MM-dd'));

      if (projectId) {
        pmaQuery = pmaQuery.eq('project_id', projectId);
      }

      const { data: pmaCerts, error: pmaError } = await pmaQuery;

      if (pmaError) {
        console.error('Error fetching PMA certificates:', pmaError);
        throw pmaError;
      }

      // Get current date in UTC YYYY-MM-DD format for today's logs
      const utcNow = new Date();
      const utcYear = utcNow.getUTCFullYear();
      const utcMonth = String(utcNow.getUTCMonth() + 1).padStart(2, '0');
      const utcDay = String(utcNow.getUTCDate()).padStart(2, '0');
      const today = `${utcYear}-${utcMonth}-${utcDay}`;

      // Fetch today's maintenance logs with timestamp and status
      const { data: todayLogs, error: todayLogsError } = await supabase
        .from('maintenance_logs')
        .select('id, pma_id, created_at, log_date, status')
        .eq('log_date', today);

      if (todayLogsError) {
        console.error(
          "Error fetching today's maintenance logs:",
          todayLogsError,
        );
        throw todayLogsError;
      }

      // Count all maintenance logs for today (no time cutoff)
      const overdueMaintenance = todayLogs ? todayLogs.length : 0;

      // Get list of PMA IDs that have maintenance logs for today
      const pmaIdsWithLogs = new Set(
        todayLogs?.map((log) => log.pma_id).filter(Boolean) || [],
      );

      // Count PMAs with any maintenance logs for today
      const completedMaintenance = pmaCerts
        ? pmaCerts.filter((cert) => pmaIdsWithLogs.has(cert.id)).length
        : 0;

      // Total maintenance is the total count of PMA certificates
      const totalMaintenance = pmaCerts ? pmaCerts.length : 0;

      // Count PMAs without logs for today
      const pmaWithoutLogsToday = pmaCerts
        ? pmaCerts.filter((cert) => !pmaIdsWithLogs.has(cert.id)).length
        : 0;

      // Fetch project start date if available
      let startDate = null;
      if (projectId) {
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('start_date')
          .eq('id', projectId)
          .single();

        if (projectError) {
          console.error('Error fetching project start date:', projectError);
        } else if (projectData && projectData.start_date) {
          startDate = new Date(projectData.start_date);
        }
      }

      // If no start date, use earliest log or cert date as fallback
      if (!startDate) {
        const { data: earliestLog, error: logError } = await supabase
          .from('maintenance_logs')
          .select('created_at')
          .order('created_at', { ascending: true })
          .limit(1);

        if (logError) {
          console.error('Error fetching earliest log date:', logError);
        } else if (
          earliestLog &&
          earliestLog.length > 0 &&
          earliestLog[0].created_at
        ) {
          startDate = new Date(earliestLog[0].created_at);
        }

        if (!startDate) {
          const { data: earliestCert, error: certError } = await supabase
            .from('pma_certificates')
            .select('created_at')
            .order('created_at', { ascending: true })
            .limit(1);

          if (certError) {
            console.error('Error fetching earliest cert date:', certError);
          } else if (
            earliestCert &&
            earliestCert.length > 0 &&
            earliestCert[0].created_at
          ) {
            startDate = new Date(earliestCert[0].created_at);
          }
        }
      }

      // Default to a year ago if no start date found
      if (!startDate) {
        startDate = subDays(now, 365);
      }

      // Fetch all maintenance logs since start date until yesterday
      const yesterday = subDays(now, 1);
      let allLogsQuery = supabase
        .from('maintenance_logs')
        .select('id, created_at, log_date, pma_id, status')
        .gte('log_date', format(startDate, 'yyyy-MM-dd'))
        .lte('log_date', format(yesterday, 'yyyy-MM-dd'));

      if (projectId) {
        allLogsQuery = allLogsQuery.eq('project_id', projectId);
      }

      const { data: allLogs, error: allLogsError } = await allLogsQuery;

      if (allLogsError) {
        console.error('Error fetching all maintenance logs:', allLogsError);
        throw allLogsError;
      }

      // Count all logs since project start (no time cutoff)
      const logsAfter5pmSinceStart = allLogs ? allLogs.length : 0;

      // Fetch all PMA certificates with details
      let allPmaQuery = supabase
        .from('pma_certificates')
        .select('id, pma_number, status, created_at');

      if (projectId) {
        allPmaQuery = allPmaQuery.eq('project_id', projectId);
      }

      const { data: allPmas, error: allPmaError } = await allPmaQuery;

      if (allPmaError) {
        console.error('Error fetching all PMA certificates:', allPmaError);
        throw allPmaError;
      }

      // Process calendar data: day-by-day analysis
      const maintenanceCalendarData = [];
      const pmaMap = new Map<string, { pmaNumber: string; status: string }>();
      allPmas?.forEach((pma) => {
        if (pma.id) {
          pmaMap.set(pma.id, {
            pmaNumber: pma.pma_number || pma.id,
            status: pma.status || 'Unknown',
          });
        }
      });

      // Group logs by date
      const logsByDate = new Map<
        string,
        Array<{ pmaId: string; createdAt: string; status: string }>
      >();
      allLogs?.forEach((log) => {
        if (log.log_date) {
          const dateStr = log.log_date.split('T')[0]; // Format as YYYY-MM-DD
          if (!logsByDate.has(dateStr)) {
            logsByDate.set(dateStr, []);
          }
          logsByDate.get(dateStr)?.push({
            pmaId: log.pma_id || '',
            createdAt: log.created_at || '',
            status: log.status || 'Unknown',
          });
        }
      });

      // Iterate from start date to yesterday to build calendar data
      let currentDate = new Date(startDate);
      while (currentDate <= yesterday) {
        const dateStr = format(currentDate, 'yyyy-MM-dd');
        const logsForDay = logsByDate.get(dateStr) || [];
        const pmaIdsWithLogsToday = new Set(
          logsForDay.map((log) => log.pmaId).filter(Boolean),
        );

        // PMA certificates without logs for this day
        const missingLogs = allPmas
          ? allPmas
              .filter((pma) => !pmaIdsWithLogsToday.has(pma.id))
              .map((pma) => ({
                pmaNumber: pma.pma_number || pma.id,
                status: pma.status || 'Unknown',
              }))
          : [];

        // PMA certificates with logs for this day (no time cutoff)
        const lateLogs = logsForDay
          .filter((log) => log.pmaId && pmaMap.has(log.pmaId))
          .map((log) => ({
            pmaNumber: pmaMap.get(log.pmaId)?.pmaNumber || log.pmaId,
            status: log.status,
          }));

        if (missingLogs.length > 0 || lateLogs.length > 0) {
          maintenanceCalendarData.push({
            date: dateStr,
            missingLogs,
            lateLogs,
          });
        }

        currentDate = addDays(currentDate, 1);
      }

      // Calculate counts for new status categories
      const fullyFunction = logs.filter(
        (log) => log.status === 'fully function',
      ).length;
      const broken = logs.filter((log) => log.status === 'broken').length;

      return {
        statusDistribution: [
          { name: 'Fully Function', value: fullyFunction, color: '#A7F3D0' }, // Pastel emerald
          { name: 'Broken', value: broken, color: '#FECACA' }, // Pastel red
        ],
        byType: types,
        total: logs.length,
        completedCount: completed,
        pendingCount: pending,
        overdueCount: overdue,
        completedMaintenance, // PMA certs with any maintenance log for today
        totalMaintenance, // Total PMA certificates count
        overdueMaintenance, // Total maintenance logs for the current day
        pmaWithoutLogsToday, // PMA certs without maintenance logs for today
        logsAfter5pmSinceStart, // Total maintenance logs since project start
        maintenanceCalendarData, // Calendar data for maintenance issues
      };
    } catch (error) {
      console.error('Error in getMaintenanceLogsStats:', error);
      // Return sample data structure on error for better UI experience
      return this.getSampleMaintenanceStats();
    }
  },

  /**
   * Get sample maintenance logs stats for development/testing
   * @returns Sample maintenance logs stats
   */
  getSampleMaintenanceStats(): MaintenanceLogStats {
    // Generate random counts for demo purposes
    const completedCount = Math.floor(Math.random() * 10) + 5;
    const pendingCount = Math.floor(Math.random() * 8) + 2;
    const overdueCount = Math.floor(Math.random() * 5);
    const total = completedCount + pendingCount + overdueCount;

    // Generate sample types
    const types = [
      { name: 'Preventive', count: Math.floor(Math.random() * 10) + 5 },
      { name: 'Corrective', count: Math.floor(Math.random() * 8) + 2 },
      { name: 'Emergency', count: Math.floor(Math.random() * 4) },
    ];

    return {
      statusDistribution: [
        { name: 'Completed', value: completedCount, color: '#4CAF50' },
        { name: 'Pending', value: pendingCount, color: '#FF9800' },
        { name: 'Overdue', value: overdueCount, color: '#F44336' },
      ],
      byType: types,
      total,
      completedCount,
      pendingCount,
      overdueCount,
      completedMaintenance: Math.floor(Math.random() * 10) + 5,
      totalMaintenance: Math.floor(Math.random() * 20) + 10,
      overdueMaintenance: Math.floor(Math.random() * 5),
      pmaWithoutLogsToday: Math.floor(Math.random() * 5),
      logsAfter5pmSinceStart: Math.floor(Math.random() * 10),
      maintenanceCalendarData: [], // Empty for sample data
    };
  },

  /**
   * Get complaints stats
   * @param projectId Optional project ID to filter by
   * @returns Complaints stats
   */
  async getComplaintsStats(projectId?: string): Promise<ComplaintStats> {
    // Define the shape of the complaints data we expect
    interface ComplaintData {
      id: string;
      description?: string;
      cause_of_damage?: string;
      status?: string;
      created_at?: string;
      actual_completion_date?: string;
      location?: string;
    }

    // Initialize variables needed in both try and catch blocks
    const now = new Date();
    const fullMonthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    try {
      // Base query builder
      let query = supabase
        .from('complaints')
        .select(
          'id, description, cause_of_damage, status, created_at, actual_completion_date, location',
        );

      // Add project filter if provided
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      // Execute query
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching complaints:', error);
        throw error;
      }

      // Cast data to expected shape
      const complaints = (data || []) as unknown as ComplaintData[];

      // Calculate statistics
      const newCount = complaints.filter(
        (complaint) => complaint.status === 'new',
      ).length;

      const inProgress = complaints.filter(
        (complaint) => complaint.status === 'in_progress',
      ).length;

      const resolved = complaints.filter(
        (complaint) => complaint.status === 'resolved',
      ).length;

      const closed = complaints.filter(
        (complaint) => complaint.status === 'closed',
      ).length;

      // Get categories
      const categoriesMap = new Map<string, number>();
      complaints.forEach((complaint) => {
        if (complaint.cause_of_damage) {
          const count = categoriesMap.get(complaint.cause_of_damage) || 0;
          categoriesMap.set(complaint.cause_of_damage, count + 1);
        }
      });

      const categories = Array.from(categoriesMap.entries()).map(
        ([name, count]) => ({
          name,
          count,
        }),
      );

      // Get monthly trend data
      const now = new Date();
      const twelveMonthsAgo = subDays(now, 365);
      const { data: monthlyData, error: monthlyError } = await supabase
        .from('complaints')
        .select('id, created_at, actual_completion_date, status')
        .eq('project_id', projectId ?? '')
        .gte('created_at', format(twelveMonthsAgo, 'yyyy-MM-dd'));

      if (monthlyError) {
        console.error('Error fetching monthly complaints data:', monthlyError);
        throw monthlyError;
      }

      // Cast monthly data to expected shape
      const monthlyItems = (monthlyData || []) as unknown as ComplaintData[];

      // Process monthly data for regular trend (6 months)
      const months = Array.from({ length: 6 }, (_, i) => {
        const date = subDays(now, 30 * (5 - i));
        return {
          month: format(date, 'MMM'),
          received: 0,
          resolved: 0,
        };
      });

      // Process monthly data for trend analysis (12 months)
      const fullMonthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];

      // Create a proper 12-month array starting from current month and going back 11 months
      const currentMonth = now.getMonth();
      const trendMonths = Array.from({ length: 12 }, (_, i) => {
        // Calculate month index, going backward from current month
        const monthIndex = (currentMonth - 11 + i + 12) % 12;
        return {
          month: fullMonthNames[monthIndex],
          total: 0,
        };
      });

      monthlyItems.forEach((complaint) => {
        const createdMonth = complaint.created_at
          ? format(new Date(complaint.created_at), 'MMM')
          : null;

        const resolvedMonth = complaint.actual_completion_date
          ? format(new Date(complaint.actual_completion_date), 'MMM')
          : null;

        // For regular monthly trend (6 months)
        if (createdMonth) {
          const monthIndex = months.findIndex((m) => m.month === createdMonth);
          if (monthIndex >= 0) {
            months[monthIndex].received += 1;
          }
        }

        if (resolvedMonth) {
          const monthIndex = months.findIndex((m) => m.month === resolvedMonth);
          if (monthIndex >= 0) {
            months[monthIndex].resolved += 1;
          }
        }

        // For trend analysis (12 months)
        if (complaint.created_at) {
          const date = new Date(complaint.created_at);
          // Only count complaints from the last 12 months
          const isWithinLast12Months =
            date >=
            new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());

          if (isWithinLast12Months) {
            const monthIndex = date.getMonth();
            const monthName = fullMonthNames[monthIndex];
            const trendMonthIndex = trendMonths.findIndex(
              (m) => m.month === monthName,
            );

            if (trendMonthIndex >= 0) {
              trendMonths[trendMonthIndex].total += 1;
            }
          }
        }
      });

      // Calculate growth rate for trend analysis
      let trendGrowthRate = 0;
      if (trendMonths.length >= 2) {
        const firstHalfTotal = trendMonths
          .slice(0, 6)
          .reduce((sum, month) => sum + month.total, 0);
        const secondHalfTotal = trendMonths
          .slice(6, 12)
          .reduce((sum, month) => sum + month.total, 0);

        if (firstHalfTotal > 0) {
          trendGrowthRate = Number(
            (
              ((secondHalfTotal - firstHalfTotal) / firstHalfTotal) *
              100
            ).toFixed(1),
          );
        }
      }

      // Calculate response time by category
      const responseTimeMap = new Map<string, number>();
      const responseTimeCountMap = new Map<string, number>();

      complaints.forEach((complaint) => {
        if (
          complaint.cause_of_damage &&
          complaint.created_at &&
          complaint.actual_completion_date &&
          complaint.status === 'resolved'
        ) {
          const createdDate = new Date(complaint.created_at);
          const resolvedDate = new Date(complaint.actual_completion_date);
          const daysToResolve = Math.ceil(
            (resolvedDate.getTime() - createdDate.getTime()) /
              (1000 * 3600 * 24),
          );

          const category = complaint.cause_of_damage;
          const currentTotal = responseTimeMap.get(category) || 0;
          const currentCount = responseTimeCountMap.get(category) || 0;

          responseTimeMap.set(category, currentTotal + daysToResolve);
          responseTimeCountMap.set(category, currentCount + 1);
        }
      });

      const responseTime = Array.from(responseTimeMap.entries()).map(
        ([category, totalDays]) => {
          const count = responseTimeCountMap.get(category) || 1;
          return {
            category,
            avgTime: Number((totalDays / count).toFixed(1)),
          };
        },
      );

      // Get recent complaints
      let recentQuery = supabase
        .from('complaints')
        .select(
          'id, description, cause_of_damage, status, created_at, location',
        )
        .order('created_at', { ascending: false })
        .limit(5);

      if (projectId) {
        recentQuery = recentQuery.eq('project_id', projectId);
      }

      const { data: recent, error: recentError } = await recentQuery;

      if (recentError) {
        console.error('Error fetching recent complaints:', recentError);
        throw recentError;
      }

      // Cast recent data to expected shape
      const recentItems = (recent || []) as unknown as ComplaintData[];

      // Calculate resolution rate
      const resolvedComplaints = complaints.filter(
        (c) => c.status === 'resolved' || c.status === 'closed',
      );
      const resolutionRate =
        complaints.length > 0
          ? Math.round((resolvedComplaints.length / complaints.length) * 100)
          : 0;

      return {
        statusDistribution: [
          { name: 'New', value: newCount, color: '#2196F3' },
          { name: 'In Progress', value: inProgress, color: '#FF9800' },
          { name: 'Resolved', value: resolved, color: '#4CAF50' },
          { name: 'Closed', value: closed, color: '#9E9E9E' },
        ],
        byCategory: categories,
        monthlyTrend: months,
        responseTime,
        trendAnalysis: trendMonths,
        trendGrowthRate,
        recent: recentItems.map((complaint) => ({
          id: complaint.id,
          title: complaint.description || 'Untitled Complaint',
          category:
            complaint.cause_of_damage || complaint.location || 'Uncategorized',
          status: complaint.status || 'Unknown',
          date: complaint.created_at
            ? format(new Date(complaint.created_at), 'yyyy-MM-dd')
            : null,
        })),
        total: complaints.length,
        newCount,
        inProgressCount: inProgress,
        resolvedCount: resolved,
        closedCount: closed,
        openCount: newCount + inProgress,
        resolutionRate,
      };
    } catch (error) {
      console.error('Error in getComplaintsStats:', error);
      // Return empty data structure on error
      // Create sample data when there's an error, for development/testing
      const sampleTrendAnalysis = Array.from({ length: 12 }, (_, i) => {
        const monthIndex = (now.getMonth() - 11 + i + 12) % 12;
        return {
          month: fullMonthNames[monthIndex],
          // Generate small random values to show the chart with sample data
          total: Math.floor(Math.random() * 5),
        };
      });

      return {
        statusDistribution: [
          { name: 'New', value: 0, color: '#2196F3' },
          { name: 'In Progress', value: 0, color: '#FF9800' },
          { name: 'Resolved', value: 0, color: '#4CAF50' },
          { name: 'Closed', value: 0, color: '#9E9E9E' },
        ],
        byCategory: [],
        monthlyTrend: [],
        responseTime: [],
        trendAnalysis: sampleTrendAnalysis, // Use sample data instead of empty array
        trendGrowthRate: 5.2, // Sample growth rate
        recent: [],
        total: 0,
        newCount: 0,
        inProgressCount: 0,
        resolvedCount: 0,
        closedCount: 0,
        openCount: 0,
        resolutionRate: 0,
      };
    }
  },

  /**
   * Get safety incident stats
   * @param _projectId Optional project ID to filter by
   * @returns Safety incident stats
   */
  async getSafetyIncidentStats(
    _projectId?: string,
  ): Promise<SafetyIncidentStats> {
    // This is a placeholder - you would need to implement this based on your actual safety incidents table
    // For now, returning mock data
    return {
      last30Days: 0,
      trend: [
        { month: 'Jan', count: 0 },
        { month: 'Feb', count: 0 },
        { month: 'Mar', count: 0 },
        { month: 'Apr', count: 0 },
        { month: 'May', count: 0 },
        { month: 'Jun', count: 0 },
      ],
    };
  },

  /**
   * Get compliance score
   * @param projectId Optional project ID to filter by
   * @returns Compliance score
   */
  async getComplianceScore(projectId?: string): Promise<ComplianceScore> {
    try {
      // Get PMA certificates
      const pmaStats = await this.getPMACertificatesStats(projectId);

      // Get maintenance logs
      const maintenanceStats = await this.getMaintenanceLogsStats(projectId);

      // Calculate compliance score based on:
      // 1. Percentage of valid PMA certificates
      // 2. Percentage of on-time maintenance logs

      const pmaCompliance =
        pmaStats.total > 0 ? (pmaStats.validCount / pmaStats.total) * 100 : 100;

      const maintenanceCompliance =
        maintenanceStats.total > 0
          ? ((maintenanceStats.total - maintenanceStats.overdueCount) /
              maintenanceStats.total) *
            100
          : 100;

      // Overall compliance score (weighted average)
      const complianceScore = Math.round(
        pmaCompliance * 0.6 + maintenanceCompliance * 0.4,
      );

      return {
        score: complianceScore,
        pmaCompliance: Math.round(pmaCompliance),
        maintenanceCompliance: Math.round(maintenanceCompliance),
      };
    } catch (error) {
      console.error('Error in getComplianceScore:', error);
      return {
        score: 0,
        pmaCompliance: 0,
        maintenanceCompliance: 0,
      };
    }
  },
};
