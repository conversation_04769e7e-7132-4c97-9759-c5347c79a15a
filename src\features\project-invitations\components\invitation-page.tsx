'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useUserWithProfile } from '@/hooks/use-auth';
import { supabase } from '@/lib/supabase';
import { CheckCircle2, Loader2, Users, XCircle } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { mapProjectRoleToUserRole } from '../utils/role-mapping';

interface InvitationPageProps {
  token: string;
}

interface InvitationData {
  id: string;
  project_id: string;
  role: 'technician' | 'competent_person' | 'admin' | 'viewer';
  invitee_email: string;
  inviter_user_id: string | null;
  supabase_user_id?: string | null;
  status: string;
  project?: {
    id: string;
    name: string;
  } | null;
  inviter?: {
    name: string;
  } | null;
}

/**
 * Component for displaying and accepting project invitations
 * Handles both logged-in and non-logged-in users with Supabase Auth
 */
export function InvitationPage({ token }: InvitationPageProps) {
  const router = useRouter();
  const { data: user } = useUserWithProfile();
  const t = useTranslations('invitation');
  const locale = useLocale();

  const [invitationState, setInvitationState] = useState<
    'loading' | 'valid' | 'invalid' | 'accepted' | 'redirecting' | 'error'
  >('loading');

  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [isAccepting, setIsAccepting] = useState(false);

  // Load invitation details
  useEffect(() => {
    if (!token) return;

    const loadInvitation = async () => {
      try {
        console.log('Loading invitation with token:', token);

        // First, let's check if the invitation exists at all
        const { data: invitationCheck, error: checkError } = await supabase
          .from('project_invitations')
          .select('*')
          .eq('token', token)
          .maybeSingle();

        console.log('Invitation check result:', {
          invitationCheck,
          checkError,
        });

        if (checkError) {
          console.error('Error checking invitation:', checkError);
          setInvitationState('error');
          return;
        }

        if (!invitationCheck) {
          console.log('No invitation found with this token');
          setInvitationState('invalid');
          return;
        }

        console.log('Invitation found, checking status and expiry:', {
          status: invitationCheck.status,
          expiry_date: invitationCheck.expiry_date,
          current_time: new Date().toISOString(),
        });

        // Now get the full invitation with relations
        const { data, error } = await supabase
          .from('project_invitations')
          .select(
            `
            *,
            project:projects(id, name),
            inviter:users!project_invitations_inviter_user_id_fkey(name)
          `,
          )
          .eq('token', token)
          .eq('status', 'pending')
          .gt('expiry_date', new Date().toISOString())
          .maybeSingle();

        console.log('Full invitation query result:', { data, error });

        if (error) {
          console.error('Error fetching invitation:', error);
          setInvitationState('error');
          return;
        }

        if (!data) {
          console.log(
            'Invitation validation failed - either not pending or expired',
          );
          setInvitationState('invalid');
          return;
        }

        setInvitation(data);
        setInvitationState('valid');
      } catch (error) {
        console.error('Error in loadInvitation:', error);
        setInvitationState('error');
      }
    };

    loadInvitation();
  }, [token]);

  const handleAcceptInvitation = useCallback(async () => {
    if (!invitation) return;

    // If user is not logged in, redirect to authentication
    if (!user) {
      const authUrl = `/${locale}/set-password?project=${token}`;
      router.push(authUrl);
      return;
    }

    // If logged in user doesn't match the invited user, show error
    if (
      invitation.supabase_user_id &&
      user.id !== invitation.supabase_user_id
    ) {
      toast.error(
        t('accept.error', {
          error:
            'This invitation is for a different user. Please log out and use the correct account.',
        }),
      );
      return;
    }

    setIsAccepting(true);

    try {
      // Step 1: Ensure user exists in the users table (create if needed)
      const { data: existingUser, error: userCheckError } = await supabase
        .from('users')
        .select('id')
        .eq('id', user.id)
        .maybeSingle();

      if (userCheckError && userCheckError.code !== 'PGRST116') {
        console.error('Error checking user existence:', userCheckError);
        toast.error(
          t('accept.error', {
            error: 'Failed to verify user account. Please try again.',
          }),
        );
        setIsAccepting(false);
        return;
      }

      // If user doesn't exist in users table, create them
      if (!existingUser) {
        console.log('Creating user profile in users table...');

        // Map project role to user role using centralized mapping
        const userRole = mapProjectRoleToUserRole(invitation.role);

        // Get contractor_id from the project for contractor users
        let contractorId: string | null = null;
        if (userRole === 'contractor') {
          const { data: project, error: projectError } = await supabase
            .from('projects')
            .select('contractor_id')
            .eq('id', invitation.project_id)
            .single();

          if (projectError) {
            console.error(
              'Error fetching project contractor_id:',
              projectError,
            );
            toast.error(
              t('accept.error', {
                error: 'Failed to get project details. Please try again.',
              }),
            );
            setIsAccepting(false);
            return;
          }

          contractorId = project?.contractor_id || null;
        }

        const { error: createUserError } = await supabase.from('users').insert({
          id: user.id,
          email: user.email || invitation.invitee_email,
          name:
            user.user_metadata?.full_name ||
            user.email?.split('@')[0] ||
            'User',
          phone_number: user.user_metadata?.phone_number || null,
          user_role: userRole,
          contractor_id: contractorId,
          onboarding_completed: false,
          created_at: new Date().toISOString(),
        });

        if (createUserError) {
          console.error('Error creating user profile:', createUserError);
          toast.error(
            t('accept.error', {
              error: 'Failed to create user profile. Please try again.',
            }),
          );
          setIsAccepting(false);
          return;
        }
        console.log('User profile created successfully');
      }

      // Step 2: Add user to project
      const { error: addUserError } = await supabase
        .from('project_users')
        .insert({
          project_id: invitation.project_id,
          user_id: user.id,
          role: invitation.role,
          assigned_date: new Date().toISOString().split('T')[0],
          is_active: true,
          created_by: invitation.inviter_user_id,
          created_at: new Date().toISOString(),
        });

      if (addUserError) {
        console.error('Error adding user to project:', addUserError);
        toast.error(
          t('accept.error', {
            error: 'Failed to join project. Please try again.',
          }),
        );
        setIsAccepting(false);
        return;
      }

      // Update invitation status
      const { error: updateError } = await supabase
        .from('project_invitations')
        .update({
          status: 'accepted',
          invitee_user_id: user.id,
          responded_at: new Date().toISOString(),
          responded_by: user.id,
        })
        .eq('token', token);

      if (updateError) {
        console.error('Error updating invitation status:', updateError);
        // Don't fail here - the user was already added to the project
      }

      setInvitationState('accepted');
      toast.success(
        t('accept.success', {
          projectName: invitation.project?.name || t('accepted.unknownProject'),
        }),
      ); // Check if user needs to complete onboarding
      const { data: userProfile } = await supabase
        .from('users')
        .select('onboarding_completed')
        .eq('id', user.id)
        .single();

      // Clear middleware cookies to ensure fresh data on redirect
      if (typeof document !== 'undefined') {
        document.cookie =
          'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie =
          'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      }

      // Redirect based on onboarding status after a short delay
      setTimeout(() => {
        setInvitationState('redirecting');
        if (!userProfile?.onboarding_completed) {
          // Redirect to onboarding/profile page if not completed
          // Add refresh flag to force cache invalidation
          router.push(
            `/${locale}/profile?from=invitation&project=${invitation.project_id}&refresh=true`,
          );
        } else {
          // Redirect to project dashboard if onboarding is complete
          router.push(`/${locale}/projects/${invitation.project_id}`);
        }
      }, 2000);
    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast.error(
        t('accept.error', {
          error: 'An unexpected error occurred. Please try again.',
        }),
      );
      setIsAccepting(false);
    }
  }, [invitation, user, token, router, t, locale]);

  if (invitationState === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
            <p className="text-lg font-medium">{t('loading')}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (invitationState === 'invalid') {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-red-600">{t('invalid.title')}</CardTitle>
            <CardDescription>{t('invalid.description')}</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (invitationState === 'error') {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-red-600">{t('error.title')}</CardTitle>
            <CardDescription>{t('error.description')}</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (invitationState === 'accepted') {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <CardTitle className="text-green-600">
              {t('accepted.title')}
            </CardTitle>
            <CardDescription>
              {t('accepted.description', {
                projectName:
                  invitation?.project?.name || t('accepted.unknownProject'),
              })}
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (invitationState === 'redirecting') {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
            <p className="text-lg font-medium">{t('redirecting')}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Valid invitation - show acceptance UI
  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="text-2xl font-bold text-blue-600 mb-2">SimPLE</div>
          <Users className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <CardTitle>{t('title')}</CardTitle>
          <CardDescription>
            {t('description', {
              inviterName: invitation?.inviter?.name || t('someone'),
            })}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">
              {t('projectDetails.title')}
            </h3>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium text-blue-800">
                  {t('projectDetails.project')}:
                </span>
                <span className="ml-2 text-blue-700">
                  {invitation?.project?.name ||
                    t('projectDetails.unknownProject')}
                </span>
              </div>
              <div>
                <span className="font-medium text-blue-800">
                  {t('projectDetails.role')}:
                </span>
                <span className="ml-2 text-blue-700 capitalize">
                  {invitation?.role || t('projectDetails.member')}
                </span>
              </div>
              <div>
                <span className="font-medium text-blue-800">
                  {t('projectDetails.invitedBy')}:
                </span>
                <span className="ml-2 text-blue-700">
                  {invitation?.inviter?.name || t('projectDetails.unknown')}
                </span>
              </div>
            </div>
          </div>

          {!user && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <p className="text-sm text-amber-800">
                <strong>{t('nextStep.title')}:</strong>{' '}
                {t('nextStep.description')}
              </p>
            </div>
          )}

          <Button
            onClick={handleAcceptInvitation}
            className="w-full"
            disabled={isAccepting}
            size="lg"
          >
            {isAccepting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isAccepting
              ? t('button.accepting')
              : user
                ? t('button.accept')
                : t('button.acceptAndSetup')}
          </Button>

          <p className="text-xs text-gray-500 text-center">{t('disclaimer')}</p>
        </CardContent>
      </Card>
    </div>
  );
}
