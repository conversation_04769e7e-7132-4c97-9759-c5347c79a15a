import type { Database } from '@/types/database';

// Database types
export type ProjectUser = Database['public']['Tables']['project_users']['Row'];
export type ProjectUserInsert =
  Database['public']['Tables']['project_users']['Insert'];
export type ProjectRole = Database['public']['Enums']['project_role'];

// Project Invitation types
export type ProjectInvitation =
  Database['public']['Tables']['project_invitations']['Row'];
export type ProjectInvitationInsert =
  Database['public']['Tables']['project_invitations']['Insert'];

// Invitation form types
export interface InvitationMember {
  email: string;
  role: ProjectRole;
}

// Invitation result types
export interface InvitationResult {
  email: string;
  status:
    | 'success'
    | 'already_added'
    | 'user_not_found'
    | 'invited'
    | 'error'
    | 'profile_incomplete';
  message: string;
  error?: string;
  token?: string; // For invited users
}

export interface BatchInvitationResult {
  results: InvitationResult[];
  totalProcessed: number;
  successCount: number;
  errorCount: number;
  alreadyAddedCount: number;
  userNotFoundCount: number;
  invitedCount: number; // New count for email invitations
  profileIncompleteCount?: number; // Count for users with incomplete profiles
}

// User existence check types
export interface UserExistenceCheck {
  email: string;
  exists: boolean;
  userId?: string;
  name?: string;
  profileCompleted?: boolean;
}

// Email invitation types
export interface InviteUserByEmailParams {
  email: string;
  role: ProjectRole;
  projectId: string;
  inviterId: string;
}

export interface EmailInvitationResult {
  success: boolean;
  token?: string;
  supabaseUserId?: string;
  error?: string;
  message?: string;
}
