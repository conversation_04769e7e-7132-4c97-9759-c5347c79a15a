#!/bin/bash

# <PERSON>ript to verify deployment package contains all necessary files
# Run this script to check if your deployment will include email templates

echo "🔍 Verifying deployment package..."

# Check if build directory exists
if [ ! -d "build/standalone" ]; then
    echo "❌ build/standalone directory not found. Run 'pnpm build' first."
    exit 1
fi

cd build/standalone

echo "📦 Checking deployment package contents..."

# Check essential files
echo "✅ Essential files:"
[ -f "server.js" ] && echo "  ✓ server.js" || echo "  ❌ server.js"
[ -f "package.json" ] && echo "  ✓ package.json" || echo "  ❌ package.json"
[ -f ".env.local" ] && echo "  ✓ .env.local" || echo "  ❌ .env.local"
[ -f "web.config" ] && echo "  ✓ web.config" || echo "  ❌ web.config"
[ -f "startup.sh" ] && echo "  ✓ startup.sh" || echo "  ❌ startup.sh"

# Check Supabase directory and templates
echo ""
echo "📧 Email templates:"
if [ -d "supabase" ]; then
    echo "  ✓ supabase/ directory exists"
    if [ -d "supabase/templates" ]; then
        echo "  ✓ supabase/templates/ directory exists"
        template_count=$(ls -1 supabase/templates/*.html 2>/dev/null | wc -l)
        echo "  ✓ Found $template_count email template(s)"
        ls supabase/templates/*.html 2>/dev/null | sed 's/^/    - /'
    else
        echo "  ❌ supabase/templates/ directory missing"
    fi
    
    if [ -f "supabase/config.toml" ]; then
        echo "  ✓ supabase/config.toml exists"
        # Check for correct path format in config
        if grep -q 'content_path = "supabase/templates/' supabase/config.toml; then
            echo "  ✓ Template paths correctly configured (without ./ prefix)"
        elif grep -q 'content_path = "./supabase/templates/' supabase/config.toml; then
            echo "  ⚠️  Template paths use ./ prefix - this may cause issues in deployment"
        else
            echo "  ❌ Template paths not found or incorrectly configured"
        fi
    else
        echo "  ❌ supabase/config.toml missing"
    fi
else
    echo "  ❌ supabase/ directory missing"
    echo "     Make sure your CI/CD includes: cp -r supabase/ build/standalone/"
fi

echo ""
echo "🏗️  Build artifacts:"
[ -d "build" ] && echo "  ✓ build/ directory" || echo "  ❌ build/ directory"

cd ../..

echo ""
echo "📋 Summary:"
if [ -d "build/standalone/supabase/templates" ] && [ -f "build/standalone/supabase/config.toml" ]; then
    echo "✅ Deployment package looks good for email templates!"
else
    echo "❌ Email templates will NOT work in deployment."
    echo "   Fix the issues above and rebuild."
fi
