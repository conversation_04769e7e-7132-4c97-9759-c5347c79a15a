'use client';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  useAuthTranslations,
  useValidationTranslations,
} from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import { useState } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { toast } from 'sonner';
import { useLogin } from '../hooks/use-auth';
import { createLoginSchema, type LoginFormValues } from '../schemas';

interface AdminLoginFormProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function AdminLoginForm({ className, ...props }: AdminLoginFormProps) {
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const loginMutation = useLogin();
  const auth = useAuthTranslations();
  const validation = useValidationTranslations();

  const formSchema = createLoginSchema(validation, auth);

  type FormValues = LoginFormValues;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    try {
      setErrorMessage('');

      // Show loading toast
      const loadingToast = toast.loading(auth('signingIn'));

      await loginMutation.mutateAsync(values);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success(auth('adminLoginSuccess'));
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : auth('loginFailed');

      setErrorMessage(errorMessage);
      toast.error(errorMessage);
    }
  };

  return (
    <div
      className={cn('flex flex-col gap-6 w-full max-w-md mx-auto', className)}
      {...props}
    >
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-xl sm:text-2xl font-bold">
          {auth('adminLoginTitle')}
        </h1>
        <p className="text-muted-foreground text-sm text-balance px-4 sm:px-0">
          {auth('adminLoginSubtitle')}
        </p>
      </div>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4 sm:space-y-6"
        >
          <div className="grid gap-4 sm:gap-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="grid gap-2">
                  <FormLabel className="text-sm sm:text-base">
                    {auth('email')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('emailPlaceholder')}
                      type="email"
                      className="h-10 sm:h-11"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="grid gap-2">
                  <div className="flex items-center">
                    <FormLabel className="text-sm sm:text-base">
                      {auth('password')}
                    </FormLabel>
                    <Link
                      href="/forgot-password"
                      className="ml-auto text-xs sm:text-sm underline-offset-4 hover:underline hover:text-primary"
                    >
                      {auth('forgotPassword')}
                    </Link>
                  </div>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        {...field}
                        className="pr-10 h-10 sm:h-11"
                        placeholder="••••••••"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 flex items-center px-3 text-muted-foreground hover:text-foreground"
                      >
                        {showPassword ? (
                          <Eye className="h-4 w-4 sm:h-5 sm:w-5" />
                        ) : (
                          <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="w-full h-10 sm:h-11 text-sm sm:text-base"
              disabled={loginMutation.isPending}
            >
              {loginMutation.isPending ? auth('signingIn') : auth('adminLogin')}
            </Button>
            {errorMessage && (
              <p className="text-sm text-red-600 text-center">{errorMessage}</p>
            )}
          </div>
          <div className="text-center text-xs sm:text-sm">
            {auth('noAdminAccount')}{' '}
            <Link
              href="/admin/register"
              className="underline underline-offset-4 hover:text-primary"
            >
              {auth('adminSignUp')}
            </Link>
          </div>
        </form>
      </Form>
    </div>
  );
}
