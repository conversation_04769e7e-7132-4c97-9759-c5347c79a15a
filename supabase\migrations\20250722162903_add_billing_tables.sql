-- ================================
-- COMPLETE PMA-BASED BILLING SYSTEM
-- Consolidated migration for PMA subscription billing with payment methods
-- ================================

-- ================================
-- ENUMS
-- ================================

-- Create subscription status enum
CREATE TYPE public.subscription_status AS ENUM (
    'active',
    'pending_payment', 
    'grace_period',
    'trial',
    'cancelled',
    'suspended'
);

-- Create payment status enum  
CREATE TYPE public.payment_status AS ENUM (
    'pending',
    'paid',
    'failed',
    'cancelled',
    'refunded'
);

-- ================================
-- UTILITY FUNCTIONS
-- ================================

-- Function to automatically update updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Specific function for contractor payment methods
CREATE OR REPLACE FUNCTION update_contractor_payment_methods_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- PMA SUBSCRIPTIONS TABLE
-- Core subscription management per PMA certificate
-- ================================

CREATE TABLE public.pma_subscriptions (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    pma_certificate_id uuid NOT NULL,
    contractor_id uuid NOT NULL,
    amount decimal(10,2) DEFAULT 150.00 NOT NULL,
    currency text DEFAULT 'MYR' NOT NULL,
    status public.subscription_status DEFAULT 'pending_payment' NOT NULL,
    billing_cycle text DEFAULT 'monthly' NOT NULL,
    next_billing_date timestamp with time zone,
    grace_period_ends timestamp with time zone,
    access_allowed boolean GENERATED ALWAYS AS (
        status IN ('active', 'grace_period', 'trial')
    ) STORED,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_pma_subscriptions_pma_certificate_id 
        FOREIGN KEY (pma_certificate_id) REFERENCES public.pma_certificates(id) ON DELETE CASCADE,
    CONSTRAINT fk_pma_subscriptions_contractor_id 
        FOREIGN KEY (contractor_id) REFERENCES public.contractors(id) ON DELETE CASCADE,
        
    -- Unique constraint: one subscription per PMA certificate
    CONSTRAINT uq_pma_subscriptions_pma_certificate_id UNIQUE (pma_certificate_id),
    
    -- Business logic constraints
    CONSTRAINT chk_pma_amount_positive CHECK (amount > 0),
    CONSTRAINT chk_pma_currency_valid CHECK (currency IN ('MYR')),
    CONSTRAINT chk_pma_billing_cycle_valid CHECK (billing_cycle IN ('monthly')),
    CONSTRAINT chk_pma_grace_period_logic CHECK (
        (status = 'grace_period' AND grace_period_ends IS NOT NULL) OR
        (status != 'grace_period' AND grace_period_ends IS NULL)
    )
);

-- ================================
-- PMA PAYMENT RECORDS TABLE
-- ================================

CREATE TABLE public.pma_payment_records (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    pma_subscription_id uuid NOT NULL,
    billplz_bill_id text,
    amount decimal(10,2) NOT NULL,
    status public.payment_status DEFAULT 'pending' NOT NULL,
    paid_at timestamp with time zone,
    failure_reason text,
    billplz_response jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_pma_payment_records_subscription_id 
        FOREIGN KEY (pma_subscription_id) REFERENCES public.pma_subscriptions(id) ON DELETE CASCADE,
        
    -- Business logic constraints
    CONSTRAINT chk_pma_payment_amount_positive CHECK (amount > 0),
    CONSTRAINT chk_pma_paid_at_logic CHECK (
        (status = 'paid' AND paid_at IS NOT NULL) OR
        (status != 'paid' AND paid_at IS NULL)
    )
);

-- ================================
-- CONTRACTOR PAYMENT METHODS TABLE
-- Stores tokenized payment card information
-- ================================

CREATE TABLE public.contractor_payment_methods (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  contractor_id uuid NOT NULL REFERENCES public.contractors(id) ON DELETE CASCADE,
  card_token text NOT NULL,
  card_fingerprint text NOT NULL,
  last_four text NOT NULL,
  brand text NOT NULL,
  exp_month integer NOT NULL,
  exp_year integer NOT NULL,
  is_default boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================

-- PMA subscriptions indexes
CREATE INDEX idx_pma_subscriptions_contractor_status 
    ON public.pma_subscriptions(contractor_id, status);
CREATE INDEX idx_pma_subscriptions_next_billing 
    ON public.pma_subscriptions(next_billing_date) 
    WHERE status = 'active';
CREATE INDEX idx_pma_subscriptions_grace_period 
    ON public.pma_subscriptions(grace_period_ends) 
    WHERE status = 'grace_period';

-- Computed field index for fast access control queries
CREATE INDEX idx_pma_subscriptions_access_allowed 
    ON public.pma_subscriptions(access_allowed, contractor_id)
    WHERE access_allowed = true;

-- PMA payment records indexes  
CREATE INDEX idx_pma_payment_records_subscription_status 
    ON public.pma_payment_records(pma_subscription_id, status);
CREATE INDEX idx_pma_payment_records_billplz_id 
    ON public.pma_payment_records(billplz_bill_id) 
    WHERE billplz_bill_id IS NOT NULL;
CREATE INDEX idx_pma_payment_records_created_at 
    ON public.pma_payment_records(created_at DESC);

-- Contractor payment methods indexes
CREATE INDEX idx_contractor_payment_methods_contractor_id 
    ON public.contractor_payment_methods(contractor_id);
CREATE INDEX idx_contractor_payment_methods_fingerprint 
    ON public.contractor_payment_methods(card_fingerprint);
CREATE INDEX idx_contractor_payment_methods_is_default 
    ON public.contractor_payment_methods(contractor_id, is_default) 
    WHERE is_default = true;

-- Unique constraint: only one default payment method per contractor
CREATE UNIQUE INDEX idx_contractor_payment_methods_unique_default 
    ON public.contractor_payment_methods(contractor_id) 
    WHERE is_default = true;

-- ================================
-- ROW LEVEL SECURITY (RLS)
-- ================================

-- Enable RLS on contractor payment methods
ALTER TABLE public.contractor_payment_methods ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own contractor's payment methods
CREATE POLICY "Users can manage their contractor payment methods" 
    ON public.contractor_payment_methods
    FOR ALL USING (
        contractor_id IN (
            SELECT contractor_id 
            FROM public.users 
            WHERE users.id = auth.uid()
        )
    );

-- Policy: Service role can access all payment methods (for API operations)
CREATE POLICY "Service role can manage all payment methods" 
    ON public.contractor_payment_methods
    FOR ALL USING (auth.role() = 'service_role');

-- ================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ================================

-- Trigger for pma_subscriptions
CREATE TRIGGER trigger_update_pma_subscriptions_updated_at 
    BEFORE UPDATE ON public.pma_subscriptions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Trigger for contractor payment methods
CREATE TRIGGER trigger_contractor_payment_methods_updated_at
    BEFORE UPDATE ON public.contractor_payment_methods
    FOR EACH ROW
    EXECUTE FUNCTION update_contractor_payment_methods_updated_at();

-- ================================
-- COMMENTS FOR DOCUMENTATION
-- ================================

-- Table comments
COMMENT ON TABLE public.pma_subscriptions IS 'Monthly subscription management per PMA certificate with access control';
COMMENT ON TABLE public.pma_payment_records IS 'BillPlz payment transaction history for PMA subscriptions';
COMMENT ON TABLE public.contractor_payment_methods IS 'Stores tokenized payment card information for contractors using BillPlz tokenization API';

-- PMA subscriptions column comments
COMMENT ON COLUMN public.pma_subscriptions.status IS 'Subscription status controlling PMA certificate access';
COMMENT ON COLUMN public.pma_subscriptions.access_allowed IS 'Computed field: true when status allows PMA access (active, grace_period, or trial)';
COMMENT ON COLUMN public.pma_subscriptions.grace_period_ends IS 'End of 7-day grace period after payment failure';

-- PMA payment records column comments
COMMENT ON COLUMN public.pma_payment_records.billplz_response IS 'Complete BillPlz API response for debugging';

-- Contractor payment methods column comments
COMMENT ON COLUMN public.contractor_payment_methods.card_token IS 'BillPlz tokenized card token for charging';
COMMENT ON COLUMN public.contractor_payment_methods.card_fingerprint IS 'BillPlz card fingerprint for duplicate detection';
COMMENT ON COLUMN public.contractor_payment_methods.last_four IS 'Last 4 digits of card number for display';
COMMENT ON COLUMN public.contractor_payment_methods.brand IS 'Card brand (Visa, Mastercard, etc.)';
COMMENT ON COLUMN public.contractor_payment_methods.exp_month IS 'Card expiry month (1-12)';
COMMENT ON COLUMN public.contractor_payment_methods.exp_year IS 'Card expiry year (YYYY)';
COMMENT ON COLUMN public.contractor_payment_methods.is_default IS 'Whether this is the default payment method for the contractor';

-- ================================
-- COMPLETE PMA BILLING SYSTEM READY
-- ================================

-- ================================
-- END OF MIGRATION  
-- ================================
