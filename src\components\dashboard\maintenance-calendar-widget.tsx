'use client';

import { Calendar } from '@/components/ui/calendar';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useMaintenanceLogsStats } from '@/features/dashboard/hooks';
import { format } from 'date-fns';
import { AlertCircle, Calendar as CalendarIcon, Clock } from 'lucide-react';
import { useMemo, useState } from 'react';

export function MaintenanceCalendarWidget() {
  const { data: maintenanceStats, isLoading } = useMaintenanceLogsStats();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const calendarData = useMemo(() => {
    return maintenanceStats?.maintenanceCalendarData || [];
  }, [maintenanceStats]);

  const { lateDates, missingDates } = useMemo(() => {
    const late = calendarData
      .filter((item) => item.lateLogs.length > 0)
      .map((item) => new Date(item.date));
    const missing = calendarData
      .filter((item) => item.missingLogs.length > 0)
      .map((item) => new Date(item.date));
    return { lateDates: late, missingDates: missing };
  }, [calendarData]);

  // Memoize the modifiers object itself to prevent unnecessary re-renders of the Calendar component
  const modifiers = useMemo(
    () => ({
      late: lateDates,
      missing: missingDates,
    }),
    [lateDates, missingDates],
  );

  const modifiersClassNames = {
    late: "relative after:content-[''] after:absolute after:top-1 after:left-1/2 after:-translate-x-1/2 after:w-1.5 after:h-1.5 after:rounded-full after:bg-red-500",
    missing:
      "relative before:content-[''] before:absolute before:bottom-1 before:left-1/2 before:-translate-x-1/2 before:w-1.5 before:h-1.5 before:rounded-full before:bg-amber-500",
  };

  const handleDayClick = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    const hasIssues = calendarData.some(
      (item) =>
        item.date === dateStr &&
        (item.lateLogs.length > 0 || item.missingLogs.length > 0),
    );
    if (hasIssues) {
      setIsDialogOpen(true);
      setSelectedDate(date);
    }
  };

  // Get data for the selected date
  const selectedData = useMemo(() => {
    if (!selectedDate) return undefined;
    const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');
    return calendarData.find((item) => item.date === selectedDateStr);
  }, [selectedDate, calendarData]);

  return (
    <div className="p-6 bg-white rounded-2xl shadow-lg border border-gray-100/80">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base font-medium flex items-center gap-2 text-slate-800">
          <CalendarIcon className="h-5 w-5 text-primary" />
          Maintenance Issues Calendar
        </h3>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-[300px] bg-slate-50/50 rounded-lg">
          <div className="flex flex-col items-center">
            <div className="animate-spin w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full mb-3"></div>
            <p className="text-sm text-muted-foreground">
              Loading calendar data...
            </p>
          </div>
        </div>
      ) : calendarData.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-10 px-6 text-center space-y-4 h-[300px] bg-slate-50/50 rounded-lg border-2 border-dashed border-slate-200">
          <div className="relative">
            <CalendarIcon className="h-16 w-16 text-slate-300" />
            <Clock className="h-6 w-6 text-amber-500 absolute bottom-0 right-0" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-slate-700 mb-1">
              No Calendar Data
            </h3>
            <p className="text-sm text-slate-500 mb-3">
              There are no maintenance issues recorded in the calendar.
            </p>
          </div>
        </div>
      ) : (
        <div className="flex justify-center">
          <Calendar
            mode="single"
            modifiers={modifiers}
            modifiersClassNames={modifiersClassNames}
            showOutsideDays={true}
            onDayClick={handleDayClick}
            fixedWeeks
            selected={selectedDate}
            className="p-0 [--cell-size:2.5rem]"
          />
          {selectedDate && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                    Maintenance Issues for {selectedDate?.toLocaleDateString()}
                  </DialogTitle>
                </DialogHeader>
                {selectedData ? (
                  <div className="space-y-4 py-2">
                    {selectedData.lateLogs.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-red-600 mb-1">
                          Late Logs
                        </h4>
                        <ul className="mt-2 space-y-2 text-sm text-slate-700">
                          {selectedData.lateLogs.map((log) => (
                            <li
                              key={`late-${log.pmaNumber}`}
                              className="flex items-center gap-2"
                            >
                              <span className="w-1.5 h-1.5 rounded-full bg-red-500"></span>
                              <span>{log.pmaNumber}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {selectedData.missingLogs.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-amber-600 mb-1">
                          Missing Logs
                        </h4>
                        <ul className="mt-2 space-y-2 text-sm text-slate-700">
                          {selectedData.missingLogs.map((log) => (
                            <li
                              key={`missing-${log.pmaNumber}`}
                              className="flex items-center gap-2"
                            >
                              <span className="w-1.5 h-1.5 rounded-full bg-amber-500"></span>
                              <span>{log.pmaNumber}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 py-4">
                    No issues for this date.
                  </p>
                )}
              </DialogContent>
            </Dialog>
          )}
        </div>
      )}
    </div>
  );
}
