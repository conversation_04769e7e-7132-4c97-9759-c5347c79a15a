/**
 * Format a date string to Malaysian locale
 */
export const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'No date';
  return new Date(dateString).toLocaleDateString('en-MY', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Calculate project duration between start and end dates
 */
export const calculateProjectDuration = (
  startDate: string | null,
  endDate: string | null,
): string => {
  if (!startDate || !endDate) return 'Duration unknown';

  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 30) {
    return `${diffDays} days`;
  } else if (diffDays < 365) {
    const months = Math.round(diffDays / 30);
    return `${months} month${months > 1 ? 's' : ''}`;
  } else {
    const years = Math.round(diffDays / 365);
    return `${years} year${years > 1 ? 's' : ''}`;
  }
};

/**
 * Get status color classes for UI display
 */
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'active':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'completed':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

/**
 * Get status icon based on project status
 */
export const getStatusIcon = (status: string) => {
  // Note: Icons are imported where this function is used
  switch (status) {
    case 'active':
      return 'Clock';
    case 'pending':
      return 'Calendar';
    case 'completed':
      return 'CheckCircle2';
    default:
      return 'Clock';
  }
};

/**
 * Validate project form data
 */
export const validateProjectForm = (data: {
  name: string;
  code: string;
  location: string;
  start_date: string;
  end_date?: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.name?.trim()) {
    errors.push('Project name is required');
  }

  if (!data.code?.trim()) {
    errors.push('Quotation number is required');
  }

  if (!data.location?.trim()) {
    errors.push('Project location is required');
  }

  if (!data.start_date) {
    errors.push('Start date is required');
  }

  if (data.end_date && new Date(data.end_date) <= new Date(data.start_date)) {
    errors.push('End date must be after start date');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Generate a quotation number suggestion based on name and location
 */
export const suggestQuotationNumber = (
  name: string,
  location: string,
): string => {
  const nameAbbr = name
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 3)
    .join('');

  const locationAbbr = location
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  const timestamp = Date.now().toString().slice(-3);

  return `${nameAbbr}${locationAbbr}-${timestamp}`;
};
