-- ================================
-- ADD FOLLOW-UP FIELD TO COMPLAINTS
-- ================================

-- Create follow_up enum type
DO $$ BEGIN 
  CREATE TYPE complaint_follow_up AS ENUM ('in_progress', 'pending_approval', 'verified'); 
EXCEPTION 
  WHEN duplicate_object THEN NULL; 
END $$;

-- Add follow_up column to complaints table
ALTER TABLE complaints 
ADD COLUMN IF NOT EXISTS follow_up complaint_follow_up NOT NULL DEFAULT 'in_progress';

-- Add comment for documentation
COMMENT ON COLUMN complaints.follow_up IS 'Follow-up status: in_progress (when complaint is created), pending_approval (when Section B is completed), verified (when admin approves)';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_complaints_follow_up ON complaints(follow_up);

-- Update existing complaints to have proper follow_up status based on current status
UPDATE complaints 
SET follow_up = CASE 
  WHEN status = 'open' THEN 'in_progress'::complaint_follow_up
  WHEN status = 'closed' THEN 'pending_approval'::complaint_follow_up
  WHEN status = 'on_hold' THEN 'in_progress'::complaint_follow_up
  ELSE 'in_progress'::complaint_follow_up
END
WHERE follow_up IS NULL OR follow_up = 'in_progress';
