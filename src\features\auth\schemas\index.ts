import * as z from 'zod';
import type { UserRole } from '../types/auth';

// Login form schema factory
export const createLoginSchema = (
  validation: (key: string, options?: { field?: string }) => string,
  auth: (key: string) => string,
) =>
  z.object({
    email: z
      .string()
      .min(1, validation('required', { field: auth('email') }))
      .email(validation('email'))
      .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, validation('email'))
      .refine((email) => !email.includes('+'), {
        message: validation('emailNoSubaddressing'),
      }),
    password: z
      .string()
      .min(1, validation('required', { field: auth('password') })),
  });

// Register form schema factory
export const createRegisterSchema = (
  validation: (key: string, options?: { field?: string }) => string,
  auth: (key: string) => string,
) =>
  z
    .object({
      email: z
        .string()
        .min(1, validation('required', { field: auth('email') }))
        .email(validation('email'))
        .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, validation('email'))
        .refine((email) => !email.includes('+'), {
          message: validation('emailNoSubaddressing'),
        }),
      password: z
        .string()
        .min(8, validation('passwordMin'))
        .regex(/^(?=.*[a-z])/, validation('passwordLowercase'))
        .regex(/^(?=.*[A-Z])/, validation('passwordUppercase'))
        .regex(/^(?=.*\d)/, validation('passwordNumber'))
        .regex(/^(?=.*[@$!%*?&])/, validation('passwordSpecial')),
      confirmPassword: z.string(),
      role: z.enum(['contractor', 'admin', 'viewer'] as const, {
        required_error: validation('roleRequired'),
      }) satisfies z.ZodType<UserRole>,
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: validation('passwordMatch'),
      path: ['confirmPassword'],
    });

// Forgot password form schema factory
export const createForgotPasswordSchema = (
  validation: (key: string, options?: { field?: string }) => string,
  auth: (key: string) => string,
) =>
  z.object({
    email: z
      .string()
      .min(1, validation('required', { field: auth('email') }))
      .email(validation('email'))
      .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, validation('email'))
      .refine((email) => !email.includes('+'), {
        message: validation('emailNoSubaddressing'),
      }),
  });

// Verification code form schema factory
export const createVerificationCodeSchema = (
  validation: (key: string, options?: { field?: string }) => string,
  auth: (key: string) => string,
) =>
  z.object({
    email: z
      .string()
      .min(1, validation('required', { field: auth('email') }))
      .email(validation('email'))
      .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, validation('email'))
      .refine((email) => !email.includes('+'), {
        message: validation('emailNoSubaddressing'),
      }),
    code: z
      .string()
      .min(1, validation('required', { field: auth('verificationCode') }))
      .length(6, 'Verification code must be exactly 6 digits')
      .regex(/^\d{6}$/, 'Verification code must contain only numbers'),
  });

// Reset password form schema factory
export const createResetPasswordSchema = (
  validation: (key: string, options?: { field?: string }) => string,
  _auth: (key: string) => string,
) =>
  z
    .object({
      password: z
        .string()
        .min(8, validation('passwordMin'))
        .regex(/^(?=.*[a-z])/, validation('passwordLowercase'))
        .regex(/^(?=.*[A-Z])/, validation('passwordUppercase'))
        .regex(/^(?=.*\d)/, validation('passwordNumber'))
        .regex(/^(?=.*[@$!%*?&])/, validation('passwordSpecial')),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: validation('passwordMatch'),
      path: ['confirmPassword'],
    });

// Type inference helpers
export type LoginFormValues = z.infer<ReturnType<typeof createLoginSchema>>;
export type RegisterFormValues = z.infer<
  ReturnType<typeof createRegisterSchema>
>;
export type ForgotPasswordFormValues = z.infer<
  ReturnType<typeof createForgotPasswordSchema>
>;
export type VerificationCodeFormValues = z.infer<
  ReturnType<typeof createVerificationCodeSchema>
>;
export type ResetPasswordFormValues = z.infer<
  ReturnType<typeof createResetPasswordSchema>
>;
