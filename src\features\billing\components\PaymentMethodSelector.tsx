'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { CreditCard, Globe, Plus, AlertTriangle } from 'lucide-react';
import type { PaymentMethodType } from '../types';
import { SavedCardsList } from './SavedCardsList';
import { NewCard3DSForm } from './NewCard3DSForm';
import { useSavedCards } from '../hooks/useSavedCards';

export interface PaymentMethodSelection {
  type: PaymentMethodType;
  cardId?: string;
  cardToken?: string;
}

export interface PaymentMethodSelectorProps {
  contractorId: string;
  onSelectionChange: (selection: PaymentMethodSelection | null) => void;
  className?: string;
  disabled?: boolean;
}

export function PaymentMethodSelector({
  contractorId,
  onSelectionChange,
  className,
  disabled = false,
}: PaymentMethodSelectorProps) {
  const [selectedMethod, setSelectedMethod] =
    useState<PaymentMethodType>('billplz_redirect');
  const [selectedCardId, setSelectedCardId] = useState<string>('');
  const currentSelectionRef = useRef<PaymentMethodSelection | null>(null);

  const {
    savedCards,
    defaultCard,
    isLoading: isLoadingCards,
    error: cardsError,
    deleteCard,
    setDefaultCard,
    isDeleting,
    isSettingDefault,
  } = useSavedCards(contractorId);

  // Memoized function to calculate current selection
  const calculateSelection = useCallback((): PaymentMethodSelection | null => {
    switch (selectedMethod) {
      case 'billplz_redirect':
        return { type: 'billplz_redirect' };
      case 'saved_card':
        if (selectedCardId) {
          const selectedCard = savedCards.find(
            (card) => card.id === selectedCardId,
          );
          if (selectedCard) {
            return {
              type: 'saved_card',
              cardId: selectedCard.id,
              cardToken: selectedCard.card_token,
            };
          }
        }
        return null;
      case 'new_card':
        // Selection will be set when form is submitted
        return null;
      default:
        return null;
    }
  }, [selectedMethod, selectedCardId, savedCards]);

  // Auto-select default card if available and no selection made
  useEffect(() => {
    if (defaultCard && !selectedCardId && savedCards.length > 0) {
      setSelectedCardId(defaultCard.id);
    }
  }, [defaultCard, selectedCardId, savedCards.length]);

  // Update selection when method or card changes
  useEffect(() => {
    const newSelection = calculateSelection();

    // Only call onSelectionChange if the selection actually changed
    if (
      JSON.stringify(newSelection) !==
      JSON.stringify(currentSelectionRef.current)
    ) {
      currentSelectionRef.current = newSelection;
      onSelectionChange(newSelection);
    }
  }, [calculateSelection, onSelectionChange]);

  const handleMethodChange = (method: PaymentMethodType) => {
    setSelectedMethod(method);

    // Auto-select default card when switching to saved cards
    if (method === 'saved_card' && defaultCard && !selectedCardId) {
      setSelectedCardId(defaultCard.id);
    }
  };

  const handle3DSCardSuccess = (result: { card_id: string; token: string }) => {
    // Select the newly created card
    setSelectedCardId(result.card_id);

    // Update selection with new card data
    const newSelection: PaymentMethodSelection = {
      type: 'new_card',
      cardId: result.card_id,
      cardToken: result.token,
    };

    currentSelectionRef.current = newSelection;
    onSelectionChange(newSelection);
  };

  const hasSavedCards = savedCards.length > 0;
  const hasValidSavedCards = savedCards.some(
    (card) => !isCardExpired(card.exp_month, card.exp_year),
  );

  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <Label className="text-base font-medium">Payment Method</Label>
        <p className="text-sm text-muted-foreground mt-1">
          Choose how you&apos;d like to pay for your PMAs
        </p>
      </div>

      <RadioGroup
        value={selectedMethod}
        onValueChange={handleMethodChange}
        disabled={disabled}
        className="space-y-4"
      >
        {/* Saved Cards Option */}
        {hasSavedCards && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="saved_card"
                id="saved_card"
                disabled={disabled || !hasValidSavedCards}
              />
              <Label
                htmlFor="saved_card"
                className="flex items-center gap-2 cursor-pointer"
              >
                <CreditCard className="h-4 w-4" />
                Saved Cards
                {!hasValidSavedCards && (
                  <span className="text-xs text-muted-foreground">
                    (All expired)
                  </span>
                )}
              </Label>
            </div>

            {selectedMethod === 'saved_card' && (
              <div className="ml-6 space-y-3">
                <SavedCardsList
                  savedCards={savedCards}
                  selectedCardId={selectedCardId}
                  onCardSelect={setSelectedCardId}
                  onDeleteCard={deleteCard}
                  onSetDefaultCard={setDefaultCard}
                  isLoading={isLoadingCards}
                  isDeleting={isDeleting}
                  isSettingDefault={isSettingDefault}
                  error={cardsError}
                />
              </div>
            )}
          </div>
        )}

        {/* New Card Option */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value="new_card"
              id="new_card"
              disabled={disabled}
            />
            <Label
              htmlFor="new_card"
              className="flex items-center gap-2 cursor-pointer"
            >
              <Plus className="h-4 w-4" />
              {hasSavedCards ? 'Add New Card' : 'Add Card'}
            </Label>
          </div>

          {selectedMethod === 'new_card' && (
            <div className="ml-6">
              <NewCard3DSForm
                contractorId={contractorId}
                onSuccess={handle3DSCardSuccess}
                disabled={disabled}
              />
            </div>
          )}
        </div>

        {/* BillPlz Redirect Option */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value="billplz_redirect"
              id="billplz_redirect"
              disabled={disabled}
            />
            <Label
              htmlFor="billplz_redirect"
              className="flex items-center gap-2 cursor-pointer"
            >
              <Globe className="h-4 w-4" />
              BillPlz Payment Gateway
            </Label>
          </div>

          {selectedMethod === 'billplz_redirect' && (
            <div className="ml-6 p-3 bg-muted/30 rounded-lg">
              <p className="text-sm text-muted-foreground">
                You will be redirected to BillPlz&apos;s secure payment gateway
                where you can pay using:
              </p>
              <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                <li>• Online Banking (FPX)</li>
                <li>• Credit/Debit Cards</li>
                <li>• E-wallets (GrabPay, Touch &lsquo;n Go, etc.)</li>
                <li>• PayPal</li>
              </ul>
            </div>
          )}
        </div>
      </RadioGroup>

      {/* Error Display */}
      {cardsError && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {cardsError?.message || 'An error occurred'}
          </AlertDescription>
        </Alert>
      )}

      {/* Info Alert for First Time Users */}
      {!hasSavedCards && selectedMethod !== 'new_card' && (
        <Alert>
          <CreditCard className="h-4 w-4" />
          <AlertDescription>
            Save your card details for faster payments in the future. Your card
            information is securely stored by BillPlz.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

// Helper function to check if card is expired
function isCardExpired(month: number, year: number): boolean {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;

  if (year < currentYear) return true;
  if (year === currentYear && month < currentMonth) return true;
  return false;
}
