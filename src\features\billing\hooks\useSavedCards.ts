import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { SavedCardInfo } from '../types';

/**
 * API response interface for saved cards
 */
interface SavedCardsResponse {
  success: boolean;
  data: SavedCardInfo[];
  error?: string;
}

/**
 * API response interface for card operations
 */
interface CardOperationResponse {
  success: boolean;
  data?: { card_id?: string };
  error?: string;
  details?: unknown;
}

/**
 * API response interface for card creation (3DS flow)
 */
interface CreateCardResponse {
  success: boolean;
  data?: {
    card_id: string;
    redirect_url: string;
    message: string;
  };
  error?: string;
  details?: unknown;
}

/**
 * API response interface for card result retrieval
 */
interface CardResultResponse {
  success: boolean;
  data?: {
    card_id: string;
    token: string;
    last_four: string;
    brand: string;
    exp_month: number;
    exp_year: number;
    fingerprint: string;
  };
  error?: string;
}

/**
 * Hook for managing contractor's saved cards
 */
export function useSavedCards(contractorId: string) {
  const queryClient = useQueryClient();

  // Fetch saved cards
  const {
    data: savedCards = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['saved-cards', contractorId],
    queryFn: async (): Promise<SavedCardInfo[]> => {
      const response = await fetch(
        `/api/billing/saved-cards?contractor_id=${contractorId}`,
      );
      const result: SavedCardsResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch saved cards');
      }

      return result.data || [];
    },
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create card with 3DS authentication
  const createCard = useMutation({
    mutationFn: async (params: {
      email: string;
      name: string;
      phone: string;
      contractor_id: string;
      save_card?: boolean;
    }): Promise<CreateCardResponse['data']> => {
      const response = await fetch('/api/billing/create-card', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const result: CreateCardResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create card');
      }

      return result.data;
    },
  });

  // Get card result after 3DS authentication
  const getCardResult = useMutation({
    mutationFn: async (cardId: string): Promise<CardResultResponse['data']> => {
      const response = await fetch(
        `/api/billing/card-result?card_id=${cardId}`,
      );
      const result: CardResultResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to retrieve card result');
      }

      return result.data;
    },
  });

  // Save tokenized card
  const saveCard = useMutation({
    mutationFn: async (params: {
      contractor_id: string;
      token: string;
      fingerprint: string;
      last_four: string;
      brand: string;
      exp_month: number;
      exp_year: number;
      is_default?: boolean;
    }): Promise<string> => {
      const response = await fetch('/api/billing/saved-cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const result: CardOperationResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save card');
      }

      return result.data?.card_id || '';
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['saved-cards', contractorId],
      });
    },
  });

  // Delete card
  const deleteCard = useMutation({
    mutationFn: async (cardId: string): Promise<void> => {
      const response = await fetch('/api/billing/saved-cards', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contractor_id: contractorId,
          card_id: cardId,
        }),
      });

      const result: CardOperationResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete card');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['saved-cards', contractorId],
      });
    },
  });

  // Set default card
  const setDefaultCard = useMutation({
    mutationFn: async (cardId: string): Promise<void> => {
      const response = await fetch('/api/billing/saved-cards', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contractor_id: contractorId,
          card_id: cardId,
        }),
      });

      const result: CardOperationResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to set default card');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['saved-cards', contractorId],
      });
    },
  });

  // Create card with 3DS and save after authentication
  const createAndSaveCard = useMutation({
    mutationFn: async (params: {
      email: string;
      name: string;
      phone: string;
      contractor_id: string;
      save_card?: boolean;
    }): Promise<{ card_id: string; redirect_url: string }> => {
      const createResult = await createCard.mutateAsync(params);

      if (!createResult) {
        throw new Error('Failed to create card');
      }

      return {
        card_id: createResult.card_id,
        redirect_url: createResult.redirect_url,
      };
    },
  });

  // Get default card
  const defaultCard = savedCards.find((card) => card.is_default);

  return {
    // Data
    savedCards,
    defaultCard,
    isLoading,
    error,

    // Actions
    refetch,
    createCard: createCard.mutateAsync,
    getCardResult: getCardResult.mutateAsync,
    createAndSaveCard: createAndSaveCard.mutateAsync,
    saveCard: saveCard.mutateAsync,
    deleteCard: deleteCard.mutateAsync,
    setDefaultCard: setDefaultCard.mutateAsync,

    // Loading states
    isCreatingCard: createCard.isPending,
    isGettingCardResult: getCardResult.isPending,
    isCreatingAndSaving: createAndSaveCard.isPending,
    isSaving: saveCard.isPending,
    isDeleting: deleteCard.isPending,
    isSettingDefault: setDefaultCard.isPending,

    // Errors
    createCardError: createCard.error,
    getCardResultError: getCardResult.error,
    createAndSaveError: createAndSaveCard.error,
    saveError: saveCard.error,
    deleteError: deleteCard.error,
    setDefaultError: setDefaultCard.error,
  };
}
