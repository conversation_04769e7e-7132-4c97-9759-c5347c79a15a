import type { PermissionMenuitem } from '@/types/rbac';
import { FolderO<PERSON>, Settings, User } from 'lucide-react';

// Lift Management System menu items with their permission requirements
export const MENU_ITEMS: PermissionMenuitem[] = [
  {
    title: 'projects',
    url: '/projects',
    icon: FolderOpen,
    permission: 'projects.view',
    roles: ['contractor'],
    description: 'descriptions.projects',
  },
  {
    title: 'profile',
    url: '/profile',
    icon: User,
    permission: 'profile.view',
    roles: ['admin', 'contractor', 'viewer'],
    description: 'descriptions.profile',
  },
  {
    title: 'settings',
    url: '/settings',
    icon: Settings,
    permission: 'settings.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.settings',
  },
];
