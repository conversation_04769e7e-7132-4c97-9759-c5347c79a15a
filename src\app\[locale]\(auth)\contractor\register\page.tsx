import { ContractorRegisterForm } from '@/features/auth';
import { GalleryVerticalEnd } from 'lucide-react';
import Image from 'next/image';

export default function ContractorRegisterPage() {
  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium">
            <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-4" />
            </div>
            SimPLE Contractor
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-md">
            <ContractorRegisterForm />
          </div>
        </div>
      </div>
      <div className="bg-muted relative hidden lg:block">
        <Image
          src="https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2031&q=80"
          alt="Modern construction project with blueprints"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-orange-900/60 via-orange-600/20 to-transparent" />
        <div className="absolute bottom-6 left-6 text-white">
          <h2 className="text-2xl font-bold mb-2">Join as Contractor</h2>
          <p className="text-lg opacity-90">
            Register to start managing your projects
          </p>
        </div>
      </div>
    </div>
  );
}
