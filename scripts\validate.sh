#!/bin/bash

# Pre-deployment validation script
# Run this before pushing to ensure everything works

set -e  # Exit on any error

echo "🚀 Running comprehensive validation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "📦 Installing dependencies..."
pnpm install --frozen-lockfile

echo "🔍 Type checking..."
if pnpm type-check; then
    print_status "TypeScript check passed"
else
    print_error "TypeScript check failed"
    exit 1
fi

echo "🧹 Linting..."
if pnpm lint; then
    print_status "Linting passed"
else
    print_error "Linting failed"
    exit 1
fi

echo "🔨 Building..."
if pnpm build; then
    print_status "Build successful"
else
    print_error "Build failed"
    exit 1
fi

echo "🧪 Running any tests (if available)..."
if pnpm test --passWithNoTests 2>/dev/null || true; then
    print_status "Tests passed"
else
    print_warning "No tests found or test script not configured"
fi

print_status "All validation checks passed! 🎉"
echo "Safe to push to repository."
