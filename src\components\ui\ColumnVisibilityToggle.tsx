'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { Columns3, Eye, EyeOff } from 'lucide-react';

interface Column {
  key: string;
  label: string;
  canHide?: boolean;
}

interface PresetViews {
  [key: string]: {
    label: string;
    description: string;
    columns: string[];
  };
}

interface ColumnVisibilityToggleProps {
  columns: Column[];
  columnVisibility: Record<string, boolean>;
  onColumnVisibilityChange: (columnVisibility: Record<string, boolean>) => void;
  presetViews?: PresetViews;
}

const defaultPresetViews = {
  essential: {
    label: 'Essential',
    description: 'Show only the most important columns',
    columns: ['log_date', 'operation_log_type', 'contractor_name', 'actions'],
  },
  detailed: {
    label: 'Detailed',
    description: 'Show all available columns',
    columns: [
      'log_date',
      'operation_log_type',
      'contractor_name',
      'person_in_charge_name',
      'description',
      'created_at',
      'actions',
    ],
  },
  compact: {
    label: 'Compact',
    description: 'Optimized for smaller screens',
    columns: ['log_date', 'operation_log_type', 'description', 'actions'],
  },
};

export function ColumnVisibilityToggle({
  columns,
  columnVisibility,
  onColumnVisibilityChange,
  presetViews,
}: ColumnVisibilityToggleProps) {
  const visibleCount = Object.values(columnVisibility).filter(Boolean).length;

  const views = presetViews || defaultPresetViews;

  const handleColumnToggle = (columnKey: string, checked: boolean) => {
    onColumnVisibilityChange({
      ...columnVisibility,
      [columnKey]: checked,
    });
  };

  const handleSelectAll = () => {
    const newVisibility = { ...columnVisibility };
    columns.forEach((column) => {
      if (column.canHide !== false) {
        newVisibility[column.key] = true;
      }
    });
    onColumnVisibilityChange(newVisibility);
  };

  const handleDeselectAll = () => {
    const newVisibility = { ...columnVisibility };
    columns.forEach((column) => {
      if (column.canHide !== false) {
        newVisibility[column.key] = false;
      }
    });
    onColumnVisibilityChange(newVisibility);
  };

  const applyPreset = (presetKey: keyof typeof views) => {
    const preset = views[presetKey];
    const newVisibility: Record<string, boolean> = {};

    columns.forEach((column) => {
      newVisibility[column.key] = preset.columns.includes(column.key);
    });

    onColumnVisibilityChange(newVisibility);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="h-11 px-4 border-gray-300 hover:border-gray-400 rounded-xl bg-white/50 backdrop-blur-sm"
        >
          <Columns3 className="h-4 w-4 mr-2" />
          Columns
          <span className="ml-2 h-5 px-2 text-xs bg-gray-100 text-gray-700 rounded-full flex items-center">
            {visibleCount}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-64 bg-white/95 backdrop-blur-sm border-white/20"
      >
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Toggle Columns</span>
          <span className="text-xs text-muted-foreground">
            {visibleCount} of {columns.length} visible
          </span>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {/* Quick Actions */}
        <div className="px-2 py-1 space-y-1">
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs flex-1"
              onClick={handleSelectAll}
            >
              <Eye className="h-3 w-3 mr-1" />
              All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs flex-1"
              onClick={handleDeselectAll}
            >
              <EyeOff className="h-3 w-3 mr-1" />
              None
            </Button>
          </div>
        </div>

        <DropdownMenuSeparator />

        {/* Column Toggles */}
        <div className="px-2 py-1 space-y-1">
          {columns.map((column) => (
            <div
              key={column.key}
              className="flex items-center space-x-2 rounded-sm px-2 py-1.5 hover:bg-slate-50 transition-colors"
            >
              <Checkbox
                id={`column-${column.key}`}
                checked={columnVisibility[column.key] ?? true}
                onCheckedChange={(checked) =>
                  handleColumnToggle(column.key, checked as boolean)
                }
                disabled={column.canHide === false}
                className="border-slate-300"
              />
              <label
                htmlFor={`column-${column.key}`}
                className={cn(
                  'text-sm font-medium cursor-pointer flex-1',
                  column.canHide === false && 'text-muted-foreground',
                )}
              >
                {column.label}
                {column.canHide === false && (
                  <span className="text-xs text-muted-foreground ml-1">
                    (always visible)
                  </span>
                )}
              </label>
            </div>
          ))}
        </div>

        <DropdownMenuSeparator />

        {/* Preset Views */}
        <DropdownMenuLabel className="text-xs">Quick Views</DropdownMenuLabel>
        {Object.entries(views).map(([key, preset]) => (
          <DropdownMenuItem
            key={key}
            onClick={() => applyPreset(key as keyof typeof views)}
            className="flex flex-col items-start py-2"
          >
            <div className="font-medium text-sm">{preset.label}</div>
            <div className="text-xs text-muted-foreground">
              {preset.description}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
