import {
  WebhookService,
  billPlzWebhookSchema,
} from '@/features/billing/services/webhook.service';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

export async function POST(request: NextRequest) {
  try {
    // Get raw body for signature verification
    const rawBody = await request.text();
    const signature = request.headers.get('x-signature');

    // Verify BillPlz webhook signature
    if (!WebhookService.verifyBillPlzSignature(rawBody, signature)) {
      console.error('Invalid BillPlz webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    // Parse webhook payload
    const webhookData = JSON.parse(rawBody);
    const validatedData = billPlzWebhookSchema.parse(webhookData);

    // Process webhook using the service
    const result = await WebhookService.processBillPlzWebhook(validatedData);

    if (!result.success) {
      console.error('Webhook processing failed:', result.error);
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully',
      data: {
        paymentRecordId: result.paymentRecordId,
        subscriptionId: result.subscriptionId,
        projectId: result.projectId,
        contractorId: result.contractorId,
        billState: result.billState,
        accessRestored: result.accessRestored,
        processedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('BillPlz webhook validation error:', error.errors);
      return NextResponse.json(
        {
          error: 'Invalid webhook payload',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('BillPlz webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 },
    );
  }
}
