import { pmaAccessControlService } from '@/features/billing/services/pma-access-control.service';
import { getUser } from '@/lib/supabase-server';
import type { Database } from '@/types/database';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

type UserRole = Database['public']['Enums']['user_role'];

const validateAccessSchema = z.object({
  userId: z.string().uuid(),
  projectId: z.string().uuid(),
  userRole: z.enum(['admin', 'contractor', 'viewer']).optional(),
});

const bulkValidateSchema = z.object({
  validations: z
    .array(
      z.object({
        id: z.string(),
        userId: z.string().uuid(),
        projectId: z.string().uuid(),
        userRole: z.enum(['admin', 'contractor', 'viewer']).optional(),
      }),
    )
    .max(50),
});

type ValidationResult = {
  success: boolean;
  data?: {
    accessDetails: {
      hasAccess: boolean;
      reason: string;
      subscription: string | null;
    };
  };
  error?: string;
};

/**
 * POST /api/billing/access/validate
 * Validate PMA access for projects
 */
export async function POST(request: NextRequest) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const validationType = url.searchParams.get('type');

    if (validationType === 'bulk') {
      // Handle bulk validation
      const body = await request.json();
      const { data: bulkData, error: bulkValidationError } =
        bulkValidateSchema.safeParse(body);

      if (bulkValidationError) {
        return NextResponse.json(
          {
            error: 'Invalid bulk validation data',
            details: bulkValidationError,
          },
          { status: 400 },
        );
      }

      const results: Record<string, ValidationResult> = {};

      // Process each validation
      for (const validation of bulkData.validations) {
        try {
          const pmaAccess = await pmaAccessControlService.checkProjectPmaAccess(
            validation.userId,
            validation.projectId,
            validation.userRole as UserRole,
          );

          results[validation.id] = {
            success: true,
            data: {
              accessDetails: {
                hasAccess: pmaAccess.overallAccess,
                reason: pmaAccess.overallAccess ? 'active' : 'no_subscription',
                subscription:
                  pmaAccess.activeSubscriptions > 0 ? 'active' : null,
              },
            },
          };
        } catch {
          results[validation.id] = {
            success: false,
            error: 'Access check failed',
          };
        }
      }

      return NextResponse.json({ results });
    } else {
      // Handle single validation
      const body = await request.json();
      const { data: validatedData, error: validationError } =
        validateAccessSchema.safeParse(body);

      if (validationError) {
        return NextResponse.json(
          { error: 'Invalid validation data', details: validationError },
          { status: 400 },
        );
      }

      const pmaAccess = await pmaAccessControlService.checkProjectPmaAccess(
        validatedData.userId,
        validatedData.projectId,
        validatedData.userRole as UserRole,
      );

      return NextResponse.json({
        success: true,
        data: {
          hasAccess: pmaAccess.overallAccess,
          reason: pmaAccess.overallAccess ? 'active' : 'no_subscription',
          subscription: pmaAccess.activeSubscriptions > 0 ? 'active' : null,
          userProjectAccess: {
            projectId: validatedData.projectId,
            hasAccess: pmaAccess.overallAccess,
            subscription: pmaAccess.activeSubscriptions > 0 ? 'active' : null,
          },
        },
      });
    }
  } catch (error) {
    console.error('POST /api/billing/access/validate error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
