/**
 * Client-side API service for PMA subscriptions operations
 * This handles HTTP communication with the PMA subscriptions API endpoints
 */

import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import type { PmaSubscriptionWithDetails } from '../types/pma-subscriptions';
import type { PmaAccessResult } from './pma-access-control.service';

type PmaSubscription = Database['public']['Tables']['pma_subscriptions']['Row'];

interface PmaSubscriptionAccessResponse {
  subscriptionId: string;
  pmaId?: string;
  projectId?: string;
  access: PmaAccessResult;
  subscription: PmaSubscription;
}

interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  [key: string]: unknown;
}

interface PmaSubscriptionsResponse {
  subscriptions: PmaSubscriptionWithDetails[];
  total: number;
}

export interface CreatePmaSubscriptionRequest {
  pmaId: string;
  contractorId?: string;
  amount?: number;
  billingCycle?: 'monthly';
  status?:
    | 'cancelled'
    | 'active'
    | 'pending_payment'
    | 'grace_period'
    | 'trial'
    | 'suspended';
}

export interface UpdatePmaSubscriptionRequest {
  action?: 'activate' | 'suspend' | 'cancel' | 'reactivate' | 'grace_period';
  reason?: string;
  gracePeriodDays?: number;
  [key: string]: unknown;
}

export interface PmaSubscriptionFilters {
  contractorId?: string;
  pmaId?: string;
  projectId?: string;
  status?: string;
  accessAllowed?: boolean;
}

interface PmaSubscriptionAccessControlRequest {
  action:
    | 'check'
    | 'restore'
    | 'revoke'
    | 'suspend'
    | 'cancel'
    | 'reactivate'
    | 'grace_period';
  reason?: string;
  gracePeriodDays?: number;
}

/**
 * Get PMA subscriptions with optional filters
 */
export async function getPmaSubscriptions(
  filters: PmaSubscriptionFilters = {},
): Promise<PmaSubscriptionsResponse> {
  let query = supabase.from('pma_subscriptions').select(`
      *,
      pma_certificates (
        id,
        project_id,
        pma_number,
        status,
        expiry_date,
        location,
        projects (
          id,
          name,
          location
        )
      ),
      contractors (
        id,
        name
      )
    `);

  // Apply filters
  if (filters.contractorId) {
    query = query.eq('contractor_id', filters.contractorId);
  }
  if (filters.pmaId) {
    query = query.eq('pma_certificate_id', filters.pmaId);
  }
  if (filters.projectId) {
    query = query.eq('pma_certificates.project_id', filters.projectId);
  }
  if (filters.status) {
    query = query.eq(
      'status',
      filters.status as
        | 'cancelled'
        | 'active'
        | 'pending_payment'
        | 'grace_period'
        | 'trial'
        | 'suspended',
    );
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(error.message || 'Failed to fetch PMA subscriptions');
  }

  return {
    subscriptions: (data as PmaSubscriptionWithDetails[]) || [],
    total: data?.length || 0,
  };
}

/**
 * Create a new PMA subscription
 */
export async function createPmaSubscription(
  data: CreatePmaSubscriptionRequest,
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  if (!data.contractorId) {
    throw new Error('Contractor ID is required');
  }

  const insertData = {
    pma_certificate_id: data.pmaId,
    contractor_id: data.contractorId,
    amount: data.amount || 100, // Default amount
    billing_cycle: data.billingCycle || 'monthly',
    status: (data.status || 'active') as
      | 'cancelled'
      | 'active'
      | 'pending_payment'
      | 'grace_period'
      | 'trial'
      | 'suspended',
  };

  const { data: subscription, error } = await supabase
    .from('pma_subscriptions')
    .insert(insertData)
    .select(
      `
      *,
      pma_certificates (
        id,
        project_id,
        pma_number,
        status,
        expiry_date,
        location,
        projects (
          id,
          name,
          location
        )
      ),
      contractors (
        id,
        name
      )
    `,
    )
    .single();

  if (error) {
    throw new Error(error.message || 'Failed to create PMA subscription');
  }

  return { subscription: subscription as PmaSubscriptionWithDetails };
}

/**
 * Get a single PMA subscription by ID
 */
export async function getPmaSubscription(
  subscriptionId: string,
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  const { data: subscription, error } = await supabase
    .from('pma_subscriptions')
    .select(
      `
      *,
      pma_certificates (
        id,
        project_id,
        pma_number,
        status,
        expiry_date,
        location,
        projects (
          id,
          name,
          location
        )
      ),
      contractors (
        id,
        name
      )
    `,
    )
    .eq('id', subscriptionId)
    .single();

  if (error) {
    throw new Error(error.message || 'Failed to fetch PMA subscription');
  }

  return { subscription: subscription as PmaSubscriptionWithDetails };
}

/**
 * Update a PMA subscription
 */
export async function updatePmaSubscription(
  subscriptionId: string,
  data: UpdatePmaSubscriptionRequest,
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  const updateData: Record<string, unknown> = {};

  // Handle special actions
  if (data.action) {
    switch (data.action) {
      case 'activate':
        updateData.status = 'active';
        break;
      case 'suspend':
        updateData.status = 'suspended';
        break;
      case 'cancel':
        updateData.status = 'cancelled';
        break;
      case 'reactivate':
        updateData.status = 'active';
        break;
      case 'grace_period':
        updateData.status = 'grace_period';
        if (data.gracePeriodDays) {
          updateData.grace_period_days = data.gracePeriodDays;
        }
        break;
    }
  }

  // Add other fields
  Object.keys(data).forEach((key) => {
    if (key !== 'action' && key !== 'gracePeriodDays') {
      updateData[key] = data[key as keyof UpdatePmaSubscriptionRequest];
    }
  });

  const { data: subscription, error } = await supabase
    .from('pma_subscriptions')
    .update(updateData)
    .eq('id', subscriptionId)
    .select(
      `
      *,
      pma_certificates (
        id,
        project_id,
        pma_number,
        status,
        expiry_date,
        location,
        projects (
          id,
          name,
          location
        )
      ),
      contractors (
        id,
        name
      )
    `,
    )
    .single();

  if (error) {
    throw new Error(error.message || 'Failed to update PMA subscription');
  }

  return { subscription: subscription as PmaSubscriptionWithDetails };
}

/**
 * Delete/cancel a PMA subscription
 */
export async function deletePmaSubscription(
  subscriptionId: string,
): Promise<{ success: boolean; message: string }> {
  // Soft delete - update status to cancelled instead of actually deleting
  const { error } = await supabase
    .from('pma_subscriptions')
    .update({ status: 'cancelled' })
    .eq('id', subscriptionId);

  if (error) {
    throw new Error(error.message || 'Failed to cancel PMA subscription');
  }

  return { success: true, message: 'PMA subscription cancelled successfully' };
}

/**
 * Get PMA subscription access control status
 */
export async function getPmaSubscriptionAccess(
  subscriptionId: string,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  const response = await fetch(
    `/api/billing/pma-subscriptions/${subscriptionId}/access`,
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to check subscription access');
  }

  return response.json();
}

/**
 * Perform access control action on PMA subscription
 */
export async function performPmaSubscriptionAccessAction(
  subscriptionId: string,
  data: PmaSubscriptionAccessControlRequest,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  const response = await fetch(
    `/api/billing/pma-subscriptions/${subscriptionId}/access`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    },
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to perform access control action');
  }

  return response.json();
}

/**
 * Convenience functions for common subscription actions
 */
export async function activateSubscription(
  subscriptionId: string,
  data?: { reason?: string },
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  return updatePmaSubscription(subscriptionId, { action: 'activate', ...data });
}

export async function suspendSubscription(
  subscriptionId: string,
  reason?: string,
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  return updatePmaSubscription(subscriptionId, { action: 'suspend', reason });
}

export async function cancelSubscription(
  subscriptionId: string,
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  return updatePmaSubscription(subscriptionId, { action: 'cancel' });
}

export async function reactivateSubscription(
  subscriptionId: string,
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  return updatePmaSubscription(subscriptionId, { action: 'reactivate' });
}

export async function setSubscriptionGracePeriod(
  subscriptionId: string,
  gracePeriodDays: number = 7,
): Promise<{ subscription: PmaSubscriptionWithDetails }> {
  return updatePmaSubscription(subscriptionId, {
    action: 'grace_period',
    gracePeriodDays,
  });
}

/**
 * Convenience functions for access control actions
 */
export async function checkSubscriptionAccess(
  subscriptionId: string,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  return performPmaSubscriptionAccessAction(subscriptionId, {
    action: 'check',
  });
}

export async function restoreSubscriptionAccess(
  subscriptionId: string,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  return performPmaSubscriptionAccessAction(subscriptionId, {
    action: 'restore',
  });
}

export async function revokeSubscriptionAccess(
  subscriptionId: string,
  reason?: string,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  return performPmaSubscriptionAccessAction(subscriptionId, {
    action: 'revoke',
    reason,
  });
}

export async function suspendSubscriptionAccess(
  subscriptionId: string,
  reason?: string,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  return performPmaSubscriptionAccessAction(subscriptionId, {
    action: 'suspend',
    reason,
  });
}

export async function reactivateSubscriptionAccess(
  subscriptionId: string,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  return performPmaSubscriptionAccessAction(subscriptionId, {
    action: 'reactivate',
  });
}

export async function setSubscriptionAccessGracePeriod(
  subscriptionId: string,
  gracePeriodDays: number = 7,
): Promise<PmaSubscriptionAccessResponse | ApiResponse> {
  return performPmaSubscriptionAccessAction(subscriptionId, {
    action: 'grace_period',
    gracePeriodDays,
  });
}
