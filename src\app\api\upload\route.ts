import { NextResponse } from 'next/server';
import axios from 'axios';
import crypto from 'crypto';

// Configure the API route to handle large files
export const config = {
  api: {
    bodyParser: false,
  },
};

export async function POST(request: Request) {
  try {
    // Get the raw body stream
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = (formData.get('folder') as string) || '';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    const endpoint = process.env.TMONE_SERVER;
    const bucket = process.env.TMONE_BUCKET;
    const accessKeyId = process.env.TMONE_ACCESS_KEY_ID;
    const secretAccessKey = process.env.TMONE_SECRET_ACCESS_KEY;
    const storageUrl = process.env.TMONE_OBS_STORAGE;

    if (
      !endpoint ||
      !bucket ||
      !accessKeyId ||
      !secretAccessKey ||
      !storageUrl
    ) {
      return NextResponse.json(
        { error: 'Missing OBS configuration' },
        { status: 500 },
      );
    }

    // Generate object key (file path in the bucket)
    const objectKey = folder ? `${folder}/${file.name}` : file.name;

    // Convert File to Buffer for upload
    const buffer = Buffer.from(await file.arrayBuffer());

    const contentType = file.type || 'application/octet-stream';
    const date = new Date().toUTCString();

    // Calculate MD5 hash of the content for integrity
    const md5Hash = crypto.createHash('md5').update(buffer).digest('base64');

    // AWS S3 Signature Version 2 format
    // StringToSign = HTTP-Verb + "\n" +
    //                Content-MD5 + "\n" +
    //                Content-Type + "\n" +
    //                Date + "\n" +
    //                CanonicalizedAmzHeaders +
    //                CanonicalizedResource

    // IMPORTANT: The canonical resource must be URL-encoded for the signature
    const encodedObjectKey = encodeURIComponent(objectKey).replace(/%2F/g, '/'); // Keep forward slashes unencoded
    const canonicalResource = `/${bucket}/${encodedObjectKey}`;
    const stringToSign = `PUT\n${md5Hash}\n${contentType}\n${date}\n${canonicalResource}`;

    const signature = crypto
      .createHmac('sha1', secretAccessKey)
      .update(stringToSign, 'utf-8')
      .digest('base64');

    // Construct the request URL - use encoded object key
    const requestUrl = `${endpoint}/${bucket}/${encodedObjectKey}`;
    // Add retries for better reliability
    const maxRetries = 3;
    let lastError;

    for (let i = 0; i < maxRetries; i++) {
      try {
        const _response = await axios.put(requestUrl, buffer, {
          headers: {
            Authorization: `AWS ${accessKeyId}:${signature}`,
            'Content-Type': contentType,
            'Content-MD5': md5Hash,
            'Content-Length': file.size.toString(),
            Date: date,
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        });
        break;
      } catch (error: unknown) {
        lastError = error;

        // If it's a 403 on first attempt, try virtual-hosted-style URL
        if (
          i === 0 &&
          error &&
          typeof error === 'object' &&
          'response' in error &&
          error.response &&
          typeof error.response === 'object' &&
          'status' in error.response &&
          error.response.status === 403
        ) {
          try {
            const _host = new URL(endpoint).host;
            const virtualHostedUrl = `${endpoint.replace('://', `://${bucket}.`)}/${encodedObjectKey}`;

            // For virtual-hosted-style, canonical resource is just the object key
            const virtualCanonicalResource = `/${bucket}/${encodedObjectKey}`;
            const virtualStringToSign = `PUT\n${md5Hash}\n${contentType}\n${date}\n${virtualCanonicalResource}`;
            const virtualSignature = crypto
              .createHmac('sha1', secretAccessKey)
              .update(virtualStringToSign, 'utf-8')
              .digest('base64');

            const _virtualResponse = await axios.put(virtualHostedUrl, buffer, {
              headers: {
                Authorization: `AWS ${accessKeyId}:${virtualSignature}`,
                'Content-Type': contentType,
                'Content-MD5': md5Hash,
                'Content-Length': file.size.toString(),
                Date: date,
              },
              maxContentLength: Infinity,
              maxBodyLength: Infinity,
            });
            break;
          } catch {}
        }

        if (i < maxRetries - 1) {
          // Wait before retrying (exponential backoff)
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, i) * 1000),
          );
        }
      }
    }

    // If all retries failed, throw the last error
    if (lastError) {
      throw lastError;
    }

    // Use the original (unencoded) object key for the public URL
    const publicUrl = `${storageUrl}${objectKey}`;

    return NextResponse.json({ url: publicUrl });
  } catch (error: unknown) {
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Upload failed' },
      { status: 500 },
    );
  }
}
