import { createServerSupabaseClient } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ projectId: string; memberId: string }> },
) {
  try {
    const params = await context.params;
    const { projectId, memberId } = params;

    if (!projectId || !memberId) {
      return NextResponse.json(
        { error: 'Missing required parameters: projectId, memberId' },
        { status: 400 },
      );
    }

    // Create server Supabase client with access to cookies
    const supabase = await createServerSupabaseClient();

    // Get current user from session with automatic refresh
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();
    let currentSessionData = sessionData;

    // If session is invalid or expired, try to refresh
    if (sessionError || !currentSessionData.session?.user) {
      const { data: refreshData, error: refreshError } =
        await supabase.auth.refreshSession();

      if (refreshError || !refreshData.session?.user) {
        console.error('Session validation failed:', {
          sessionError,
          refreshError,
        });
        return NextResponse.json(
          { error: 'Unauthorized: No valid session' },
          { status: 401 },
        );
      }

      currentSessionData = refreshData;
    }

    const currentUserId = currentSessionData.session!.user.id;

    // Check if current user has permission to remove members (admin or competent_person)
    const { data: currentUserProject, error: permissionError } = await supabase
      .from('project_users')
      .select('role')
      .eq('project_id', projectId)
      .eq('user_id', currentUserId)
      .eq('status', 'accepted')
      .eq('is_active', true)
      .single();

    if (
      permissionError ||
      !currentUserProject ||
      !['admin', 'competent_person'].includes(currentUserProject.role)
    ) {
      return NextResponse.json(
        {
          error:
            'Unauthorized: You do not have permission to remove members from this project',
        },
        { status: 403 },
      );
    }

    // Check if member exists and is active
    const { data: memberToRemove, error: memberError } = await supabase
      .from('project_users')
      .select('user_id, role, is_active')
      .eq('id', memberId)
      .eq('project_id', projectId)
      .eq('is_active', true)
      .single();

    if (memberError || !memberToRemove) {
      return NextResponse.json(
        { error: 'Member not found or already removed' },
        { status: 404 },
      );
    }

    // Prevent self-removal
    if (memberToRemove.user_id === currentUserId) {
      return NextResponse.json(
        { error: 'Cannot remove yourself from the project' },
        { status: 400 },
      );
    }

    // Check if removing this member would leave no admins
    const { data: adminCount, error: adminCountError } = await supabase
      .from('project_users')
      .select('id')
      .eq('project_id', projectId)
      .eq('role', 'admin')
      .eq('status', 'accepted')
      .eq('is_active', true);

    if (adminCountError) {
      console.error('Error checking admin count:', adminCountError);
      return NextResponse.json(
        { error: 'Database error while validating removal' },
        { status: 500 },
      );
    }

    // If the member being removed is an admin and they're the last admin, prevent removal
    if (memberToRemove.role === 'admin' && adminCount.length <= 1) {
      return NextResponse.json(
        { error: 'Cannot remove the last admin from the project' },
        { status: 400 },
      );
    }

    // Perform soft delete
    const { error: removeError } = await supabase
      .from('project_users')
      .update({
        is_active: false,
        deleted_at: new Date().toISOString(),
        deleted_by: currentUserId,
        updated_at: new Date().toISOString(),
        updated_by: currentUserId,
      })
      .eq('id', memberId)
      .eq('project_id', projectId);

    if (removeError) {
      console.error('Error removing member:', removeError);
      return NextResponse.json(
        { error: 'Failed to remove member' },
        { status: 500 },
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Member removed successfully',
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Remove member error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
