import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'simple-obs.obs.my-kualalumpur-1.alphaedge.tmone.com.my',
        port: '',
        pathname: '/**',
      },
    ],
  },
  reactStrictMode: true,
  distDir: 'build',
  output: 'standalone',
  outputFileTracingIncludes: {
    '*': [
      './node_modules/styled-jsx/**/*',
      './node_modules/@swc/**/*',
      './node_modules/@next/**/*',
      './node_modules/next-intl/**/*',
    ],
  },
};

export default withNextIntl(nextConfig);
