import { authenticateWithPermission, canAccessUserData } from '@/features/auth';
import { pmaAccessControlService } from '@/features/billing/services/pma-access-control.service';
import type { UserRole } from '@/types/auth';
import { NextRequest, NextResponse } from 'next/server';

interface SingleAccessRequest {
  userId?: string;
  pmaId: string;
  requireActiveSubscription?: boolean;
}

interface BulkAccessRequest {
  userId?: string;
  pmaIds: string[];
  requireActiveSubscription?: boolean;
}

interface MiddlewareAccessRequest {
  userId?: string;
  pmaId: string;
  path: string;
}

/**
 * POST /api/billing/pma-access/validate
 * Validate PMA access with various modes
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user and check billing permissions
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Parse request body
    const body = await request.json();
    const { mode } = body;

    if (!mode) {
      return NextResponse.json(
        { error: 'Validation mode is required' },
        { status: 400 },
      );
    }

    const targetUserId = body.userId || user.id;

    // Role-based access control: contractors can only validate their own access
    if (!canAccessUserData(user, targetUserId)) {
      return NextResponse.json(
        { error: 'Access denied: can only validate own access' },
        { status: 403 },
      );
    }

    switch (mode) {
      case 'single':
        return await handleSingleAccessValidation(
          body,
          targetUserId,
          user.user_role,
        );

      case 'bulk':
        return await handleBulkAccessValidation(
          body,
          targetUserId,
          user.user_role,
        );

      case 'middleware':
        return await handleMiddlewareAccessValidation(
          body,
          targetUserId,
          user.user_role,
        );

      default:
        return NextResponse.json(
          { error: `Unknown validation mode: ${mode}` },
          { status: 400 },
        );
    }
  } catch (error) {
    console.error('POST /api/billing/pma-access/validate error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * Handle single PMA access validation
 */
async function handleSingleAccessValidation(
  body: SingleAccessRequest,
  userId: string,
  userRole: UserRole,
) {
  const { pmaId, requireActiveSubscription = true } = body;

  if (!pmaId) {
    return NextResponse.json(
      { error: 'PMA ID is required for single validation' },
      { status: 400 },
    );
  }

  // Validate PMA access
  const validationResult = await pmaAccessControlService.validatePmaAccess({
    userId,
    userRole,
    pmaId,
    requireActiveSubscription,
  });

  return NextResponse.json({
    mode: 'single',
    pmaId,
    userId,
    validation: validationResult,
  });
}

/**
 * Handle bulk PMA access validation
 */
async function handleBulkAccessValidation(
  body: BulkAccessRequest,
  userId: string,
  userRole: UserRole,
) {
  const { pmaIds, requireActiveSubscription = true } = body;

  if (!pmaIds || !Array.isArray(pmaIds)) {
    return NextResponse.json(
      { error: 'PMA IDs array is required for bulk validation' },
      { status: 400 },
    );
  }

  if (pmaIds.length > 50) {
    return NextResponse.json(
      { error: 'Maximum 50 PMAs per bulk request' },
      { status: 400 },
    );
  }

  // Validate access for each PMA
  const validationPromises = pmaIds.map(async (pmaId) => {
    try {
      const result = await pmaAccessControlService.validatePmaAccess({
        userId,
        userRole,
        pmaId,
        requireActiveSubscription,
      });

      return {
        pmaId,
        ...result,
      };
    } catch (error) {
      return {
        pmaId,
        isValid: false,
        userPmaAccess: {
          userId,
          userRole,
          pmaId,
          hasAccess: false,
          accessType: 'denied' as const,
        },
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  });

  const validationResults = await Promise.all(validationPromises);

  // Aggregate results
  const validCount = validationResults.filter((r) => r.isValid).length;
  const invalidCount = validationResults.filter((r) => !r.isValid).length;

  return NextResponse.json({
    mode: 'bulk',
    userId,
    totalRequests: pmaIds.length,
    validCount,
    invalidCount,
    results: validationResults,
  });
}

/**
 * Handle middleware access validation with redirect logic
 */
async function handleMiddlewareAccessValidation(
  body: MiddlewareAccessRequest,
  userId: string,
  userRole: UserRole,
) {
  const { pmaId, path } = body;

  if (!pmaId) {
    return NextResponse.json(
      { error: 'PMA ID is required for middleware validation' },
      { status: 400 },
    );
  }

  if (!path) {
    return NextResponse.json(
      { error: 'Path is required for middleware validation' },
      { status: 400 },
    );
  }

  // Validate PMA access
  const validationResult = await pmaAccessControlService.validatePmaAccess({
    userId,
    userRole,
    pmaId,
    requireActiveSubscription: true,
  });

  // Determine redirect behavior
  let redirectPath: string | null = null;
  let shouldRedirect = false;

  if (!validationResult.isValid) {
    shouldRedirect = true;
    redirectPath = validationResult.redirectPath || '/dashboard';
  }

  // Check if current path is already a billing path to avoid redirect loops
  const isBillingPath =
    path.startsWith('/billing/') || path.startsWith('/api/billing/');

  if (shouldRedirect && isBillingPath) {
    shouldRedirect = false;
    redirectPath = null;
  }

  return NextResponse.json({
    mode: 'middleware',
    pmaId,
    userId,
    path,
    validation: validationResult,
    shouldRedirect,
    redirectPath,
    middleware: {
      allowAccess: validationResult.isValid,
      requiresRedirect: shouldRedirect,
      targetPath: redirectPath,
    },
  });
}

/**
 * GET /api/billing/pma-access/validate
 * Quick access check for current user (for frontend use)
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user (no specific permission check needed for own data access)
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const pmaId = searchParams.get('pmaId');
    const projectId = searchParams.get('projectId');

    if (pmaId) {
      // Single PMA access check
      const accessResult = await pmaAccessControlService.checkPmaAccess(
        user.id,
        pmaId,
        user.user_role,
      );

      return NextResponse.json({
        userId: user.id,
        userRole: user.user_role,
        pmaId,
        access: accessResult,
      });
    } else if (projectId) {
      // Project-level PMA access check
      const projectAccessResult =
        await pmaAccessControlService.checkProjectPmaAccess(
          user.id,
          projectId,
          user.user_role,
        );

      return NextResponse.json({
        userId: user.id,
        userRole: user.user_role,
        projectId,
        projectAccess: projectAccessResult,
      });
    } else {
      // Get user's accessible PMAs
      const accessiblePmasResult =
        await pmaAccessControlService.getUserAccessiblePmas(
          user.id,
          user.user_role,
        );

      if (accessiblePmasResult.error) {
        return NextResponse.json(
          { error: accessiblePmasResult.error },
          { status: 500 },
        );
      }

      return NextResponse.json({
        userId: user.id,
        userRole: user.user_role,
        accessiblePmas: accessiblePmasResult.pmaIds,
        totalAccessiblePmas: accessiblePmasResult.pmaIds.length,
      });
    }
  } catch (error) {
    console.error('GET /api/billing/pma-access/validate error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
