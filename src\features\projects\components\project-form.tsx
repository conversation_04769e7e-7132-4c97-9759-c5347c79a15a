'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Building2, Calendar, MapPin, Save } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { BasicProjectFormData, BasicProjectFormProps } from '../types/project';
import { validateProjectForm } from '../utils/project-utils';

/**
 * Reusable project form component for create/edit operations
 */
export const ProjectForm: React.FC<BasicProjectFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const t = useTranslations('pages.projects');
  const common = useTranslations('common');

  const [formData, setFormData] = useState<BasicProjectFormData>({
    name: initialData?.name || '',
    code: initialData?.code || '',
    location: initialData?.location || '',
    start_date: initialData?.start_date || '',
    end_date: initialData?.end_date || '',
    status: initialData?.status || 'pending',
  });

  const handleInputChange = (
    field: keyof BasicProjectFormData,
    value: string,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validation = validateProjectForm(formData);
    if (!validation.isValid) {
      // Handle validation errors
      console.error('Validation errors:', validation.errors);
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          {t('create.form.title')}
        </CardTitle>
        <CardDescription>{t('create.form.description')}</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name" className="required">
                {t('create.form.fields.name.label')}
              </Label>
              <Input
                id="name"
                placeholder={t('create.form.fields.name.placeholder')}
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="code" className="required">
                {t('create.form.fields.code.label')}
              </Label>
              <Input
                id="code"
                placeholder={t('create.form.fields.code.placeholder')}
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value)}
                required
              />
            </div>
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label
              htmlFor="location"
              className="required flex items-center gap-2"
            >
              <MapPin className="h-4 w-4" />
              {t('create.form.fields.location.label')}
            </Label>
            <Input
              id="location"
              placeholder={t('create.form.fields.location.placeholder')}
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              required
            />
          </div>

          {/* Dates */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label
                htmlFor="start_date"
                className="required flex items-center gap-2"
              >
                <Calendar className="h-4 w-4" />
                {t('create.form.fields.startDate.label')}
              </Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) =>
                  handleInputChange('start_date', e.target.value)
                }
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {t('create.form.fields.endDate.label')}
              </Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                min={formData.start_date}
              />
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">
              {t('create.form.fields.status.label')}
            </Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={t('create.form.fields.status.placeholder')}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">
                  {t('create.form.fields.status.options.pending')}
                </SelectItem>
                <SelectItem value="active">
                  {t('create.form.fields.status.options.active')}
                </SelectItem>
                <SelectItem value="completed">
                  {t('create.form.fields.status.options.completed')}
                </SelectItem>
                <SelectItem value="cancelled">
                  {t('create.form.fields.status.options.cancelled')}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Actions */}
          <div className="flex gap-4 pt-6">
            <Button type="submit" disabled={isLoading} className="min-w-32">
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  {t('create.form.actions.creating')}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {t('create.form.actions.create')}
                </>
              )}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                {common('cancel')}
              </Button>
            )}
          </div>
        </form>
      </CardContent>

      <style jsx>{`
        .required::after {
          content: ' *';
          color: red;
        }
      `}</style>
    </Card>
  );
};
