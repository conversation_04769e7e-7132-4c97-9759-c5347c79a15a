'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useProject } from '@/features/projects';
import { AlertTriangle, ArrowLeft, CheckCircle, FileText } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

/**
 * Payment setup wizard for new projects
 * Project summary with subscription details
 * BillPlz payment form integration
 * Success flow with access activation
 * Cancellation handling (project remains in pending state)
 */
export default function PaymentSetupPage() {
  const t = useTranslations('billing.setup');
  const router = useRouter();
  const params = useParams();
  const projectId = params.projectId as string;

  const [isSetupComplete, setIsSetupComplete] = useState(false);
  const [setupResult, setSetupResult] = useState<{
    success: boolean;
    paymentUrl?: string;
    error?: string;
  } | null>(null);

  // Fetch project and subscription data
  const {
    data: project,
    isLoading: projectLoading,
    error: projectError,
  } = useProject(projectId);

  const isLoading = projectLoading;
  const hasError = projectError;

  const handleCancel = () => {
    router.push('/billing');
  };

  const handleBackToBilling = () => {
    router.push('/billing');
  };

  const handleViewProject = () => {
    if (project) {
      router.push(`/projects/${project.id}`);
    }
  };

  // Loading state
  if (isLoading) {
    return <PaymentSetupSkeleton />;
  }

  // Error state
  if (hasError || !project) {
    return (
      <div className="max-w-2xl mx-auto py-8">
        <Card>
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <CardTitle>Setup Error</CardTitle>
            <CardDescription>
              {projectError
                ? t('errors.projectNotFound')
                : 'Failed to load subscription details'}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={handleBackToBilling}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Billing
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Success state
  if (isSetupComplete && setupResult?.success) {
    return (
      <div className="max-w-2xl mx-auto py-8">
        <Card>
          <CardHeader className="text-center">
            <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
            <CardTitle>{t('confirmation.title')}</CardTitle>
            <CardDescription>
              {setupResult.paymentUrl
                ? t('confirmation.redirecting')
                : t('confirmation.success')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                {t('confirmation.accessActivated')}
              </p>
              <Badge variant="secondary" className="text-green-600">
                {project.name}
              </Badge>
            </div>

            <div className="flex gap-3 justify-center">
              <Button onClick={handleViewProject}>
                {t('confirmation.viewProject')}
              </Button>
              <Button variant="outline" onClick={handleBackToBilling}>
                {t('confirmation.backToDashboard')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error in setup
  if (isSetupComplete && !setupResult?.success) {
    return (
      <div className="max-w-2xl mx-auto py-8">
        <Card>
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <CardTitle>Setup Failed</CardTitle>
            <CardDescription>
              {setupResult?.error || t('errors.setupFailed')}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <Button
              onClick={() => {
                setIsSetupComplete(false);
                setSetupResult(null);
              }}
            >
              Try Again
            </Button>
            <Button variant="outline" onClick={handleCancel}>
              Cancel Setup
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
          <p className="text-muted-foreground">
            {t('subtitle', { projectName: project.name })}
          </p>
        </div>
        <Button variant="outline" onClick={handleCancel}>
          {t('actions.cancel')}
        </Button>
      </div>

      {/* Project Summary Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t('projectDetails.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t('projectDetails.name')}
                </label>
                <p className="text-lg font-semibold">{project.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Project Code
                </label>
                <p className="text-sm">{project.code || 'Not assigned'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Location
                </label>
                <p className="text-sm">{project.location || 'Not specified'}</p>
              </div>
            </div>

            {/* Basic project billing info */}
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t('projectDetails.monthlyFee')}
                </label>
                <p className="text-lg font-semibold">
                  RM 150.00 {/* Standard PMA fee */}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Setup - PMA System */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            PMA System Active
          </CardTitle>
          <CardDescription>
            This project is now part of the new consolidated PMA billing system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            PMA billing is handled through bulk payments. Please contact your
            admin for payment setup and processing.
          </p>
          <div className="flex gap-4">
            <Button onClick={handleViewProject}>View Project</Button>
            <Button variant="outline" onClick={handleBackToBilling}>
              Back to Billing
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Loading skeleton for payment setup page
 */
function PaymentSetupSkeleton() {
  return (
    <div className="max-w-4xl mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Skeleton className="h-9 w-64 mb-2" />
          <Skeleton className="h-5 w-96" />
        </div>
        <Skeleton className="h-10 w-24" />
      </div>

      <Card className="mb-6">
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div>
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-6 w-48" />
              </div>
              <div>
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-4 w-full" />
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <Skeleton className="h-4 w-24 mb-1" />
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-6 w-16" />
                </div>
              </div>
              <div>
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-6 w-24" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-2 w-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
            <Skeleton className="h-48 w-full" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
