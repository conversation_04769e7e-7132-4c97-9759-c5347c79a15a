import type { PmaSubscriptionWithAccess } from '@/types/billing';

/**
 * Project with associated PMA subscriptions and aggregated billing data
 */
export interface ProjectWithPmas {
  project_id: string;
  project_name: string;
  project_location?: string;
  pma_count: number;
  active_pmas: number;
  suspended_pmas: number;
  grace_period_pmas: number;
  total_monthly_amount: number;
  pma_subscriptions: PmaSubscriptionWithAccess[];
  has_urgent_payments: boolean;
  next_billing_date?: string;
}

/**
 * Contractor with all their projects and PMA billing data
 */
export interface ContractorBillingData {
  contractor_id: string;
  contractor_name: string;
  projects: ProjectWithPmas[];
  total_projects: number;
  total_pmas: number;
  total_active_pmas: number;
  total_suspended_pmas: number;
  total_monthly_amount: number;
  has_urgent_payments: boolean;
  urgent_subscriptions_count: number;
}

/**
 * Aggregated billing statistics for overview cards
 */
export interface BillingStats {
  total_subscriptions: number;
  total_projects: number;
  total_monthly_amount: number;
  active_subscriptions: number;
  suspended_subscriptions: number;
  grace_period_subscriptions: number;
  urgent_payments_count: number;
}

/**
 * Project PMA summary for cards
 */
export interface ProjectPmaSummary {
  project_id: string;
  project_name: string;
  project_location?: string;
  pma_count: number;
  active_count: number;
  suspended_count: number;
  grace_period_count: number;
  monthly_amount: number;
  status: 'healthy' | 'warning' | 'critical';
  next_billing_date?: string;
  has_urgent_payments: boolean;
}

/**
 * Filters for project-grouped billing queries
 */
export interface ProjectBillingFilters {
  contractorId?: string;
  projectId?: string;
  status?: 'active' | 'suspended' | 'grace_period' | 'all';
  hasUrgentPayments?: boolean;
}
