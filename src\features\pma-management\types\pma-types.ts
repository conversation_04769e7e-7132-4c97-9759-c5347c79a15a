export interface PmaFormData {
  location: string;
  pmaNumber: string;
  inspectionDate: string;
  competentPersonId: string;
  pmaExpiryDate: string;
  certificateFile?: File;
  supervisor?: string;
  customer?: string;
  agency?: string;
  type?: string;
}

export interface PmaEntry extends PmaFormData {
  id: string;
  status: 'ready' | 'draft';
  createdAt: string;
}

export interface PmaFormState {
  entries: PmaEntry[];
  currentForm: Partial<PmaFormData>;
}
