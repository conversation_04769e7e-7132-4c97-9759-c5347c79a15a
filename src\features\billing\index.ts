// ================================
// BILLING FEATURE EXPORTS
// PMA (Consolidated) Billing System
// ================================

// ================================
// SERVICES
// ================================

export {
  BulkPaymentService,
  PmaAccessControlService,
  pmaAccessControlService,
  ProjectIntegrationService,
  projectIntegrationService,
  WebhookService,
  webhookService,
} from './services';

// Service types
export type {
  IntegrateProjectBillingParams,
  MigrationResult,
  ProjectWithBilling,
  WebhookProcessingResult,
} from './services';

// ================================
// COMPONENTS
// ================================

export {
  AccessStateManager,
  ContractorBulkPaymentModal,
  ContractorProjectOverview,
  ManagePaymentModal,
  ProjectAccessIndicator,
  ProjectPmaCard,
  SubscriptionCard,
} from './components';

// Component types
export type {
  AccessState,
  AccessStateManagerProps,
  ContractorBulkPaymentModalProps,
  ContractorProjectOverviewProps,
  ManagePaymentModalProps,
  ProjectAccessIndicatorProps,
  ProjectPmaCardProps,
  SubscriptionCardProps,
} from './components';

// ================================
// HOOKS
// ================================

export {
  BILLING_QUERY_KEYS,
  // Query utilities
  BILLING_QUERY_OPTIONS,
  // Contractor Bulk Payment
  createContractorPaymentSummary,
  prepareContractorBulkPaymentData,
  // Project Access
  useAccessGuard,
  useBillingQueryKeys,
  // Project Creation
  useCanCreateProject,
  useCheckProjectAccess,
  useContractorBulkPayment,
  useCreateProjectWithBilling,
  useInvalidateProjectAccess,
  useMultipleProjectsAccess,
  useProjectCreationAnalytics,
  useProjectCreationCost,
  useProjectCreationFlow,
  useProjectCreationNavigation,
} from './hooks';

// Hook types
export type {
  AccessDeniedReason,
  ProjectCreationFlowState,
  ProjectCreationResult,
  ProjectCreationStep,
} from './hooks';

// ================================
// BILLING TYPES
// ================================

export type {
  BillingStats,
  BulkPaymentRequest,
  BulkPaymentResult,
  ContractorBillingData,
  // Bulk payment types
  ContractorBulkPaymentData,
  ContractorPaymentSummary,
  // PMA Subscription types
  PmaSubscriptionWithDetails,
  ProjectBillingFilters,
  ProjectPmaSummary,
  // Project billing types
  ProjectWithPmas,
} from './types';

// ================================
// CORE BILLING TYPES (from main types)
// ================================

export type {
  // Utility functions
  convertCentsToMyr,
  convertMyrToCents,
  getDaysRemainingInGracePeriod,
  PaymentStatus,
  // PMA Database types
  PmaSubscription,
  PmaSubscriptionInsert,
  PmaSubscriptionUpdate,
  PmaSubscriptionWithAccess,

  // Enum types
  SubscriptionStatus,
} from '@/types/billing';
