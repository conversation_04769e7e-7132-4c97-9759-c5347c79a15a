import { BulkPaymentService } from '@/features/billing/services/bulk-payment.service';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema for card creation (3DS flow)
const CreateCardSchema = z.object({
  email: z.string().email('Invalid email format'),
  name: z.string().min(1).max(100, 'Name must be between 1-100 characters'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  contractor_id: z.string().uuid('Invalid contractor ID format'),
  save_card: z.boolean().optional().default(true),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = CreateCardSchema.parse(body);

    // Generate callback and redirect URLs
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const callbackUrl = `${baseUrl}/api/billing/card-callback?contractor_id=${validatedData.contractor_id}&save_card=${validatedData.save_card}`;
    const redirectUrl = `${baseUrl}/billing/card-success?contractor_id=${validatedData.contractor_id}`;

    // Create card with 3DS authentication
    const result = await BulkPaymentService.createCard({
      email: validatedData.email,
      name: validatedData.name,
      phone: validatedData.phone,
      callback_url: callbackUrl,
      redirect_url: redirectUrl,
    });

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to create card',
        },
        { status: 400 },
      );
    }

    // No need to store pending data - callback will handle everything

    return NextResponse.json({
      success: true,
      data: {
        card_id: result.data!.card_id,
        redirect_url: result.data!.redirect_url,
        message: 'Redirect user to complete 3DS authentication',
      },
    });
  } catch (error) {
    console.error('Card creation API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}
