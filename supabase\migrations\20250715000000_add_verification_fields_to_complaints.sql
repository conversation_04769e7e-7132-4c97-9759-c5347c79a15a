-- ================================
-- ADD VERIFICATION FIELDS TO COMPLAINTS
-- ================================

-- Add verification fields to track who verified the complaint and when
ALTER TABLE complaints 
ADD COLUMN IF NOT EXISTS verified_by text,
ADD COLUMN IF NOT EXISTS verified_date date;

-- Add comments for documentation
COMMENT ON COLUMN complaints.verified_by IS 'Name of the admin who verified the complaint';
COMMENT ON COLUMN complaints.verified_date IS 'Date when the complaint was verified by admin';

-- Create index for better query performance on verification status
CREATE INDEX IF NOT EXISTS idx_complaints_verified_by ON complaints(verified_by);
CREATE INDEX IF NOT EXISTS idx_complaints_verified_date ON complaints(verified_date);
