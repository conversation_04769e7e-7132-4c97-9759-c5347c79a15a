/**
 * BillPlz API Client
 * Comprehensive BillPlz payment gateway integration with error handling and logging
 */

import {
  BILLPLZ_API_VERSIONS,
  BILLPLZ_ENDPOINTS,
  getApiEndpoint,
  getBillPlzConfigSingleton,
  LOGGING_CONFIG,
  RATE_LIMIT_CONFIG,
} from './config';
import type {
  BillPlzApiError,
  BillPlzApiResponse,
  BillPlzBill,
  BillPlzChargeCardRequest,
  BillPlzChargeCardResponse,
  BillPlzCollection,
  BillPlzCreateBillRequest,
  BillPlzCreateCardRequest,
  BillPlzCreateCardResponse,
  BillPlzCreateCollectionRequest,
  BillPlzError,
  BillPlzNetworkError,
  BillPlzRateLimitError,
  BillPlzTransaction,
  BillPlzValidationError,
  ErrorLog,
  HttpMethod,
  RateLimitState,
  RequestOptions,
  ResponseLog,
} from './types';
import { HTTP_STATUS_CODES } from './types';
import {
  calculateDueDate,
  convertMyrToCents,
  formatDateForBillPlz,
  formatMalaysianMobile,
  generateBillReference,
  generateRequestId,
  retryWithBackoff,
  validateAmount,
  validateEmail,
  validateMalaysianMobile,
} from './utils';

// ================================
// BILLPLZ CLIENT CLASS
// ================================

export class BillPlzClient {
  private readonly config = getBillPlzConfigSingleton();
  private readonly rateLimitState: RateLimitState = {
    requests: [],
    circuitBreakerFailures: 0,
    isCircuitOpen: false,
    lastFailureTime: 0,
  };

  constructor() {
    // Client initialization completed
  }

  // ================================
  // BILL MANAGEMENT METHODS
  // ================================

  /**
   * Create a new BillPlz bill
   */
  async createBill(params: {
    contractorEmail: string;
    contractorName: string;
    contractorMobile?: string;
    amount: number; // In MYR
    description: string;
    projectId?: string;
    subscriptionId?: string;
    contractorId?: string;
    dueInDays?: number;
    callbackUrl?: string;
    redirectUrl?: string;
  }): Promise<BillPlzApiResponse<BillPlzBill>> {
    const requestId = generateRequestId();

    try {
      // Validate inputs
      this.validateBillCreationParams(params);

      // Generate bill reference
      const reference = generateBillReference({
        projectId: params.projectId,
        subscriptionId: params.subscriptionId,
        contractorId: params.contractorId,
      });

      // Convert amount to cents
      const amountInCents = convertMyrToCents(params.amount);

      // Calculate due date
      const dueDate = calculateDueDate(params.dueInDays);

      // Prepare request payload - only include defined fields
      const billRequest: BillPlzCreateBillRequest = {
        collection_id: this.config.collectionId,
        email: params.contractorEmail,
        name: params.contractorName,
        amount: amountInCents,
        description: params.description,
        due_at: formatDateForBillPlz(dueDate),
        reference_1_label: 'Subscription Ref',
        reference_1: reference,
      };

      // Add optional fields only if they have valid values
      if (params.contractorMobile) {
        billRequest.mobile = formatMalaysianMobile(params.contractorMobile);
      }

      // Only add callback URL if it's a valid HTTPS URL (not localhost for production)
      const callbackUrl = params.callbackUrl || this.config.webhookUrl;
      if (
        callbackUrl &&
        (callbackUrl.startsWith('https://') || this.config.sandboxMode)
      ) {
        billRequest.callback_url = callbackUrl;
      }

      // Only add redirect URL if it's provided and valid
      if (
        params.redirectUrl &&
        (params.redirectUrl.startsWith('https://') || this.config.sandboxMode)
      ) {
        billRequest.redirect_url = params.redirectUrl;
      }

      if (params.projectId) {
        billRequest.reference_2_label = 'Project ID';
        billRequest.reference_2 = params.projectId;
      }

      this.logRequest(requestId, 'POST', BILLPLZ_ENDPOINTS.BILLS, billRequest);

      // Make API request
      const response = await this.makeRequest<BillPlzBill>(
        'POST',
        BILLPLZ_ENDPOINTS.BILLS,
        {
          body: billRequest,
          apiVersion: BILLPLZ_API_VERSIONS.BILLS,
        },
      );

      return response;
    } catch (error) {
      this.logError(requestId, 'POST', BILLPLZ_ENDPOINTS.BILLS, error);
      throw error;
    }
  }

  /**
   * Get bill details by ID
   */
  async getBill(billId: string): Promise<BillPlzApiResponse<BillPlzBill>> {
    const requestId = generateRequestId();

    try {
      if (!billId || typeof billId !== 'string') {
        throw this.createValidationError(
          'Bill ID is required and must be a string',
        );
      }

      const endpoint = BILLPLZ_ENDPOINTS.BILL_BY_ID(billId);
      this.logRequest(requestId, 'GET', endpoint);

      const response = await this.makeRequest<BillPlzBill>('GET', endpoint, {
        apiVersion: BILLPLZ_API_VERSIONS.BILLS,
      });

      this.logInfo(`Bill retrieved: ${billId}`, {
        requestId,
        billId,
        state: response.data?.state,
        paid: response.data?.paid,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'GET',
        BILLPLZ_ENDPOINTS.BILL_BY_ID(billId),
        error,
      );
      throw error;
    }
  }

  /**
   * Delete (cancel) a bill
   */
  async deleteBill(billId: string): Promise<BillPlzApiResponse<void>> {
    const requestId = generateRequestId();

    try {
      if (!billId || typeof billId !== 'string') {
        throw this.createValidationError(
          'Bill ID is required and must be a string',
        );
      }

      const endpoint = BILLPLZ_ENDPOINTS.BILL_BY_ID(billId);
      this.logRequest(requestId, 'DELETE', endpoint);

      const response = await this.makeRequest<void>('DELETE', endpoint, {
        apiVersion: BILLPLZ_API_VERSIONS.BILLS,
      });

      this.logInfo(`Bill deleted: ${billId}`, {
        requestId,
        billId,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'DELETE',
        BILLPLZ_ENDPOINTS.BILL_BY_ID(billId),
        error,
      );
      throw error;
    }
  }

  /**
   * Get bill transactions
   */
  async getBillTransactions(
    billId: string,
  ): Promise<BillPlzApiResponse<BillPlzTransaction[]>> {
    const requestId = generateRequestId();

    try {
      if (!billId || typeof billId !== 'string') {
        throw this.createValidationError(
          'Bill ID is required and must be a string',
        );
      }

      const endpoint = BILLPLZ_ENDPOINTS.TRANSACTIONS(billId);
      this.logRequest(requestId, 'GET', endpoint);

      const response = await this.makeRequest<BillPlzTransaction[]>(
        'GET',
        endpoint,
        {
          apiVersion: BILLPLZ_API_VERSIONS.TRANSACTIONS,
        },
      );

      this.logInfo(`Bill transactions retrieved: ${billId}`, {
        requestId,
        billId,
        transactionCount: response.data?.length || 0,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'GET',
        BILLPLZ_ENDPOINTS.TRANSACTIONS(billId),
        error,
      );
      throw error;
    }
  }

  // ================================
  // COLLECTION MANAGEMENT METHODS
  // ================================

  /**
   * Create a new collection (admin only) - v4 API (logo removed)
   */
  async createCollection(params: {
    title: string;
    split_header?: boolean;
    split_payments?: Array<{
      split_bank_account_id: string;
      fixed_cut?: number;
      variable_cut?: number;
    }>;
  }): Promise<BillPlzApiResponse<BillPlzCollection>> {
    const requestId = generateRequestId();

    try {
      if (!params.title || typeof params.title !== 'string') {
        throw this.createValidationError('Collection title is required');
      }

      // Validate split_payments array length (max 2 recipients in v4)
      if (params.split_payments && params.split_payments.length > 2) {
        throw this.createValidationError(
          'Maximum 2 split payment recipients allowed in v4',
        );
      }

      const collectionRequest: BillPlzCreateCollectionRequest = {
        title: params.title,
        split_header: params.split_header,
        split_payments: params.split_payments,
      };

      this.logRequest(
        requestId,
        'POST',
        BILLPLZ_ENDPOINTS.COLLECTIONS,
        collectionRequest,
      );

      const response = await this.makeRequest<BillPlzCollection>(
        'POST',
        BILLPLZ_ENDPOINTS.COLLECTIONS,
        {
          body: collectionRequest,
          apiVersion: BILLPLZ_API_VERSIONS.COLLECTIONS,
        },
      );

      this.logInfo(`Collection created: ${response.data?.id}`, {
        requestId,
        collectionId: response.data?.id,
        title: params.title,
      });

      return response;
    } catch (error) {
      this.logError(requestId, 'POST', BILLPLZ_ENDPOINTS.COLLECTIONS, error);
      throw error;
    }
  }

  /**
   * Get collection details
   */
  async getCollection(
    collectionId: string,
  ): Promise<BillPlzApiResponse<BillPlzCollection>> {
    const requestId = generateRequestId();

    try {
      if (!collectionId || typeof collectionId !== 'string') {
        throw this.createValidationError('Collection ID is required');
      }

      const endpoint = BILLPLZ_ENDPOINTS.COLLECTION_BY_ID(collectionId);
      this.logRequest(requestId, 'GET', endpoint);

      const response = await this.makeRequest<BillPlzCollection>(
        'GET',
        endpoint,
        {
          apiVersion: BILLPLZ_API_VERSIONS.COLLECTIONS,
        },
      );

      this.logInfo(`Collection retrieved: ${collectionId}`, {
        requestId,
        collectionId,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'GET',
        BILLPLZ_ENDPOINTS.COLLECTION_BY_ID(collectionId),
        error,
      );
      throw error;
    }
  }

  // ================================
  // TOKENIZATION AND CARD PAYMENT METHODS
  // ================================

  /**
   * Create a card for 3DS authentication (Step 1 of tokenization flow)
   * This initiates the 3DS flow and returns a redirect URL
   */
  async createCard(params: {
    callback_url: string;
    redirect_url?: string;
    email: string;
    name: string;
    phone: string;
  }): Promise<BillPlzApiResponse<BillPlzCreateCardResponse>> {
    const requestId = generateRequestId();

    try {
      // Validate required parameters
      if (
        !params.callback_url ||
        !params.email ||
        !params.name ||
        !params.phone
      ) {
        throw this.createValidationError(
          'Callback URL, email, name, and phone are required for card creation',
        );
      }

      // Validate callback URL
      try {
        new URL(params.callback_url);
      } catch {
        throw this.createValidationError('Invalid callback URL format');
      }

      // Validate redirect URL if provided
      if (params.redirect_url) {
        try {
          new URL(params.redirect_url);
        } catch {
          throw this.createValidationError('Invalid redirect URL format');
        }
      }

      // Validate email
      if (!validateEmail(params.email)) {
        throw this.createValidationError('Invalid email format');
      }

      // Validate phone number (Malaysian format)
      if (!validateMalaysianMobile(params.phone)) {
        throw this.createValidationError(
          'Invalid Malaysian phone number format. Expected format: +60XXXXXXXXX',
        );
      }

      const endpoint = BILLPLZ_ENDPOINTS.CREATE_CARD;
      this.logRequest(requestId, 'POST', endpoint);

      const requestBody: BillPlzCreateCardRequest = {
        callback_url: params.callback_url,
        email: params.email,
        name: params.name,
        phone: formatMalaysianMobile(params.phone).replace('+60', '60'), // Remove +60 prefix to match API example
        // Note: redirect_url is optional and not in the official example, so we'll exclude it for now
      };

      const response = await this.makeRequest<BillPlzCreateCardResponse>(
        'POST',
        endpoint,
        {
          body: requestBody,
          apiVersion: BILLPLZ_API_VERSIONS.CARDS,
        },
      );

      this.logInfo('Card creation initiated successfully', {
        requestId,
        card_id: response.data?.id,
        redirect_url: response.data?.url,
      });

      return response;
    } catch (error) {
      this.logError(requestId, 'POST', BILLPLZ_ENDPOINTS.CREATE_CARD, error);
      throw error;
    }
  }

  /**
   * Charge a tokenized card directly
   */
  async chargeCard(params: {
    token: string;
    amount: number;
    description: string;
    email: string;
    name: string;
    phone?: string;
    reference_1?: string;
    reference_2?: string;
  }): Promise<BillPlzApiResponse<BillPlzChargeCardResponse>> {
    const requestId = generateRequestId();

    try {
      // Validate required parameters
      if (
        !params.token ||
        !params.amount ||
        !params.description ||
        !params.email ||
        !params.name
      ) {
        throw this.createValidationError(
          'Token, amount, description, email, and name are required for card charging',
        );
      }

      // Validate amount
      try {
        validateAmount(params.amount);
      } catch (error) {
        throw this.createValidationError(
          error instanceof Error ? error.message : 'Invalid amount',
        );
      }

      // Validate email
      if (!validateEmail(params.email)) {
        throw this.createValidationError('Invalid email format');
      }

      const endpoint = BILLPLZ_ENDPOINTS.CHARGE_CARD;
      this.logRequest(requestId, 'POST', endpoint);

      const requestBody: BillPlzChargeCardRequest = {
        token: params.token,
        amount: convertMyrToCents(params.amount),
        description: params.description,
        email: params.email,
        name: params.name,
        ...(params.phone && { phone: formatMalaysianMobile(params.phone) }),
        ...(params.reference_1 && { reference_1: params.reference_1 }),
        ...(params.reference_2 && { reference_2: params.reference_2 }),
      };

      const response = await this.makeRequest<BillPlzChargeCardResponse>(
        'POST',
        endpoint,
        {
          body: requestBody,
          apiVersion: BILLPLZ_API_VERSIONS.TOKENIZATION,
        },
      );

      this.logInfo('Card charged successfully', {
        requestId,
        charge_id: response.data?.id,
        amount: params.amount,
        status: response.data?.status,
      });

      return response;
    } catch (error) {
      this.logError(requestId, 'POST', BILLPLZ_ENDPOINTS.CHARGE_CARD, error);
      throw error;
    }
  }

  // ================================
  // UTILITY METHODS
  // ================================

  /**
   * Check if BillPlz service is healthy
   */
  async healthCheck(): Promise<
    BillPlzApiResponse<{ status: string; timestamp: string }>
  > {
    const requestId = generateRequestId();

    try {
      this.logRequest(requestId, 'GET', '/health');

      // Try to get collections as a health check
      await this.makeRequest<unknown>('GET', BILLPLZ_ENDPOINTS.COLLECTIONS, {
        apiVersion: BILLPLZ_API_VERSIONS.COLLECTIONS,
      });

      return {
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
        },
        meta: {
          requestId,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logError(requestId, 'GET', '/health', error);

      return {
        success: false,
        error: 'BillPlz service unavailable',
        meta: {
          requestId,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get current rate limit status
   */
  getRateLimitStatus(): {
    requestsInLastMinute: number;
    isCircuitOpen: boolean;
    circuitBreakerFailures: number;
  } {
    const oneMinuteAgo = Date.now() - 60000;
    const recentRequests = this.rateLimitState.requests.filter(
      (req) => req.timestamp > oneMinuteAgo,
    );

    return {
      requestsInLastMinute: recentRequests.length,
      isCircuitOpen: this.rateLimitState.isCircuitOpen,
      circuitBreakerFailures: this.rateLimitState.circuitBreakerFailures,
    };
  }

  // ================================
  // PRIVATE HELPER METHODS
  // ================================

  private async makeRequest<T>(
    method: HttpMethod,
    endpoint: string,
    options: Omit<RequestOptions, 'method'> & { apiVersion?: string } = {},
  ): Promise<BillPlzApiResponse<T>> {
    const startTime = Date.now();
    const url = getApiEndpoint(endpoint, options.apiVersion);

    // Check circuit breaker
    await this.checkCircuitBreaker();

    // Check rate limiting
    await this.checkRateLimit();

    try {
      const response = await retryWithBackoff(
        () => this.executeRequest<T>(method, url, options),
        RATE_LIMIT_CONFIG.MAX_RETRIES,
        RATE_LIMIT_CONFIG.BASE_DELAY_MS,
        RATE_LIMIT_CONFIG.MAX_DELAY_MS,
      );

      // Record successful request
      this.recordRequest(true);

      return response;
    } catch (error) {
      // Record failed request
      this.recordRequest(false);

      // Handle specific error types
      if (error instanceof Error) {
        throw this.enhanceError(error, method, url, startTime);
      }

      throw error;
    }
  }

  private async executeRequest<T>(
    method: HttpMethod,
    url: string,
    options: Omit<RequestOptions, 'method'>,
  ): Promise<BillPlzApiResponse<T>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Basic ${Buffer.from(this.config.apiKey + ':').toString('base64')}`,
      'User-Agent': 'SimPLE-BillPlz-Client/1.0.0',
      ...options.headers,
    };

    const fetchOptions: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(options.timeout || this.config.timeout),
    };

    if (
      options.body &&
      (method === 'POST' || method === 'PUT' || method === 'PATCH')
    ) {
      fetchOptions.body = JSON.stringify(options.body);
    }

    const response = await fetch(url, fetchOptions);

    // Handle rate limiting
    if (response.status === HTTP_STATUS_CODES.TOO_MANY_REQUESTS) {
      const retryAfter = response.headers.get('Retry-After');
      throw this.createRateLimitError(
        'Rate limit exceeded',
        retryAfter ? parseInt(retryAfter) : undefined,
      );
    }

    // Parse response
    let responseData: unknown;
    const contentType = response.headers.get('content-type');

    if (contentType?.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    // Handle error responses
    if (!response.ok) {
      // Log the full error response for debugging
      console.error('BillPlz API Error Response:', {
        status: response.status,
        statusText: response.statusText,
        responseData: JSON.stringify(responseData, null, 2),
        url,
        method,
        requestId: generateRequestId(),
      });

      throw this.createApiError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response.statusText,
        responseData,
        { url, method },
      );
    }

    return {
      success: true,
      data: responseData as T,
      meta: {
        requestId: generateRequestId(),
        timestamp: new Date().toISOString(),
        rateLimitRemaining: response.headers.get('X-RateLimit-Remaining')
          ? parseInt(response.headers.get('X-RateLimit-Remaining')!)
          : undefined,
        rateLimitReset: response.headers.get('X-RateLimit-Reset') || undefined,
      },
    };
  }

  private async checkCircuitBreaker(): Promise<void> {
    const now = Date.now();

    // Check if circuit should be reset
    if (
      this.rateLimitState.isCircuitOpen &&
      now - this.rateLimitState.lastFailureTime >
        RATE_LIMIT_CONFIG.RECOVERY_TIMEOUT_MS
    ) {
      this.rateLimitState.isCircuitOpen = false;
      this.rateLimitState.circuitBreakerFailures = 0;
      this.logInfo('Circuit breaker reset');
    }

    // Throw error if circuit is open
    if (this.rateLimitState.isCircuitOpen) {
      throw this.createNetworkError(
        `Circuit breaker is open. Service temporarily unavailable. Will retry after ${new Date(
          this.rateLimitState.lastFailureTime +
            RATE_LIMIT_CONFIG.RECOVERY_TIMEOUT_MS,
        ).toISOString()}`,
      );
    }
  }

  private async checkRateLimit(): Promise<void> {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Clean old requests
    this.rateLimitState.requests = this.rateLimitState.requests.filter(
      (req) => req.timestamp > oneMinuteAgo,
    );

    // Check rate limit
    if (
      this.rateLimitState.requests.length >=
      RATE_LIMIT_CONFIG.MAX_REQUESTS_PER_MINUTE
    ) {
      const oldestRequest = this.rateLimitState.requests[0];
      const waitTime = Math.ceil(
        (oldestRequest.timestamp + 60000 - now) / 1000,
      );

      throw this.createRateLimitError(
        `Rate limit exceeded. ${this.rateLimitState.requests.length} requests in the last minute.`,
        waitTime,
      );
    }
  }

  private recordRequest(success: boolean): void {
    const now = Date.now();

    // Record request
    this.rateLimitState.requests.push({
      timestamp: now,
      success,
    });

    // Update circuit breaker
    if (!success) {
      this.rateLimitState.circuitBreakerFailures++;
      this.rateLimitState.lastFailureTime = now;

      // Open circuit if failure threshold exceeded
      if (
        this.rateLimitState.circuitBreakerFailures >=
        RATE_LIMIT_CONFIG.FAILURE_THRESHOLD
      ) {
        this.rateLimitState.isCircuitOpen = true;
        this.logWarn(
          `Circuit breaker opened after ${this.rateLimitState.circuitBreakerFailures} failures`,
        );
      }
    } else {
      // Reset failure count on successful request
      this.rateLimitState.circuitBreakerFailures = Math.max(
        0,
        this.rateLimitState.circuitBreakerFailures - 1,
      );
    }
  }

  private validateBillCreationParams(params: {
    contractorEmail: string;
    contractorName: string;
    contractorMobile?: string;
    amount: number;
    description: string;
  }): void {
    const errors: string[] = [];

    // Email validation
    if (!params.contractorEmail || !validateEmail(params.contractorEmail)) {
      errors.push('Valid email address is required');
    }

    // Name validation
    if (
      !params.contractorName ||
      typeof params.contractorName !== 'string' ||
      params.contractorName.trim().length < 2
    ) {
      errors.push('Contractor name must be at least 2 characters');
    }

    // Mobile validation (if provided)
    if (
      params.contractorMobile &&
      !validateMalaysianMobile(params.contractorMobile)
    ) {
      errors.push('Invalid Malaysian mobile number format');
    }

    // Amount validation
    try {
      validateAmount(params.amount);
    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Invalid amount');
    }

    // Description validation
    if (
      !params.description ||
      typeof params.description !== 'string' ||
      params.description.trim().length < 5
    ) {
      errors.push('Description must be at least 5 characters');
    }

    if (errors.length > 0) {
      throw this.createValidationError(
        `Validation failed: ${errors.join(', ')}`,
      );
    }
  }

  // ================================
  // ERROR CREATION METHODS
  // ================================

  private createApiError(
    message: string,
    status: number,
    statusText: string,
    responseData?: unknown,
    request?: { url: string; method: string },
  ): BillPlzApiError {
    const error = new Error(message) as BillPlzApiError;
    error.name = 'BillPlzApiError';
    error.status = status;
    error.statusText = statusText;

    if (
      responseData &&
      typeof responseData === 'object' &&
      'error' in (responseData as object)
    ) {
      error.billplzError = responseData as BillPlzError;
    }

    if (request) {
      error.request = request;
    }

    return error;
  }

  private createNetworkError(
    message: string,
    cause?: unknown,
  ): BillPlzNetworkError {
    const error = new Error(message) as BillPlzNetworkError;
    error.name = 'BillPlzNetworkError';
    error.cause = cause;
    return error;
  }

  private createValidationError(
    message: string,
    field?: string,
    value?: unknown,
  ): BillPlzValidationError {
    const error = new Error(message) as BillPlzValidationError;
    error.name = 'BillPlzValidationError';
    error.field = field;
    error.value = value;
    return error;
  }

  private createRateLimitError(
    message: string,
    retryAfter?: number,
  ): BillPlzRateLimitError {
    const error = new Error(message) as BillPlzRateLimitError;
    error.name = 'BillPlzRateLimitError';
    error.retryAfter = retryAfter;
    return error;
  }

  private enhanceError(
    error: Error,
    _method: string,
    _url: string,
    _startTime: number,
  ): Error {
    if (error.name === 'AbortError') {
      return this.createNetworkError(
        `Request timeout after ${this.config.timeout}ms`,
        error,
      );
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return this.createNetworkError(
        'Network error: Unable to connect to BillPlz',
        error,
      );
    }

    return error;
  }

  // ================================
  // LOGGING METHODS
  // ================================

  private logRequest(
    _requestId: string,
    _method: string,
    _endpoint: string,
    _body?: unknown,
  ): void {
    // Request logging disabled for production
    return;
  }

  private logResponse(
    requestId: string,
    method: string,
    endpoint: string,
    status: number,
    body?: unknown,
    responseTime?: number,
  ): void {
    if (!LOGGING_CONFIG.LOG_RESPONSES) return;

    const log: ResponseLog = {
      type: 'response',
      requestId,
      method,
      url: endpoint,
      timestamp: new Date().toISOString(),
      status,
      statusText: 'OK',
      responseTime: responseTime || 0,
      headers: {},
      body: body,
    };

    console.info('BillPlz Response:', log);
  }

  private logError(
    requestId: string,
    method: string,
    endpoint: string,
    error: unknown,
  ): void {
    if (!LOGGING_CONFIG.LOG_ERRORS) return;

    const log: ErrorLog = {
      type: 'error',
      requestId,
      method,
      url: endpoint,
      timestamp: new Date().toISOString(),
      error: {
        name: error instanceof Error ? error.name : 'UnknownError',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      },
    };

    console.error('BillPlz Error:', log);
  }

  private logInfo(message: string, context?: Record<string, unknown>): void {
    if (LOGGING_CONFIG.LOG_REQUESTS) {
      console.info(`BillPlz: ${message}`, context);
    }
  }

  private logWarn(message: string, context?: Record<string, unknown>): void {
    console.warn(`BillPlz: ${message}`, context);
  }
}

// ================================
// SINGLETON INSTANCE
// ================================

let clientInstance: BillPlzClient | null = null;

/**
 * Get singleton BillPlz client instance
 */
export function getBillPlzClient(): BillPlzClient {
  if (!clientInstance) {
    clientInstance = new BillPlzClient();
  }
  return clientInstance;
}

// ================================
// EXPORTS
// ================================

export default getBillPlzClient;
