'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { PmaSubscriptionWithAccess } from '@/types/billing';
import {
  Calendar,
  CreditCard,
  DollarSign,
  History,
  RefreshCw,
  Settings,
} from 'lucide-react';

export interface ManagePaymentModalProps {
  subscription: PmaSubscriptionWithAccess;
  projectName?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdatePaymentMethod?: (subscriptionId: string) => void;
  onViewPaymentHistory?: (subscriptionId: string) => void;
  onUpdateBilling?: (subscriptionId: string) => void;
  onCancelSubscription?: (subscriptionId: string) => void;
  _onDownloadReceipt?: (subscriptionId: string) => void;
}

export function ManagePaymentModal({
  subscription,
  projectName,
  open,
  onOpenChange,
  onUpdatePaymentMethod,
  onViewPaymentHistory,
  onUpdateBilling,
  onCancelSubscription,
  _onDownloadReceipt,
}: ManagePaymentModalProps) {
  const monthlyAmount = subscription.calculated_amount || 0;
  const nextBillingDate = subscription.next_billing_date
    ? new Date(subscription.next_billing_date)
    : null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'suspended':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl">Manage Payment</DialogTitle>
          <DialogDescription className="mt-1">
            {projectName || 'Unnamed Project'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Subscription Overview */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Subscription Details</h3>
              <Badge
                className={cn('text-xs', getStatusColor(subscription.status))}
              >
                {subscription.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <p className="text-muted-foreground">Monthly Amount</p>
                <div className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">
                    RM {monthlyAmount.toFixed(2)}
                  </span>
                </div>
              </div>

              {nextBillingDate && subscription.status === 'active' && (
                <div className="space-y-1">
                  <p className="text-muted-foreground">Next Billing</p>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-semibold">
                      {nextBillingDate.toLocaleDateString('en-MY', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric',
                      })}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Grace Period Info */}
            {subscription.isInGracePeriod && (
              <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4 text-yellow-600" />
                  <div>
                    <p className="font-medium text-yellow-800 dark:text-yellow-200 text-sm">
                      Payment Required
                    </p>
                    <p className="text-xs text-yellow-700 dark:text-yellow-300">
                      Your subscription is in grace period. Pay now to maintain
                      access.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Payment Management Actions */}
          <div className="space-y-3">
            <h3 className="font-semibold">Payment Management</h3>

            <div className="space-y-2">
              {onUpdatePaymentMethod && (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => onUpdatePaymentMethod(subscription.id)}
                >
                  <CreditCard className="h-4 w-4 mr-3" />
                  Update Payment Method
                </Button>
              )}

              {onViewPaymentHistory && (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => onViewPaymentHistory(subscription.id)}
                >
                  <History className="h-4 w-4 mr-3" />
                  View Payment History
                </Button>
              )}
            </div>
          </div>

          <Separator />

          {/* Subscription Management */}
          <div className="space-y-3">
            <h3 className="font-semibold">Subscription Management</h3>

            <div className="space-y-2">
              {onUpdateBilling && (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => onUpdateBilling(subscription.id)}
                >
                  <Settings className="h-4 w-4 mr-3" />
                  Update Billing Information
                </Button>
              )}

              {subscription.status === 'active' && onCancelSubscription && (
                <Button
                  variant="outline"
                  className="w-full justify-start text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 hover:bg-red-50"
                  onClick={() => onCancelSubscription(subscription.id)}
                >
                  <Button className="h-4 w-4 mr-3" />
                  Cancel Subscription
                </Button>
              )}
            </div>
          </div>

          {/* Additional Info */}
          {subscription.created_at && (
            <div className="pt-2 border-t text-xs text-muted-foreground text-center">
              Subscription started on{' '}
              {new Date(subscription.created_at).toLocaleDateString('en-MY', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              })}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
