// Server-side billing services - DO NOT USE ON CLIENT SIDE
// Use API routes for client-side billing operations

// Add runtime check to prevent client-side imports
if (typeof window !== 'undefined') {
  throw new Error(
    'Billing services cannot be imported on the client side. Use API routes instead:\n' +
      '- /api/billing/subscriptions\n' +
      '- /api/billing/projects/[id]/access\n' +
      '- /api/billing/access/validate',
  );
}

// Export all billing services (SERVER-SIDE ONLY)
export {
  ProjectIntegrationService,
  projectIntegrationService,
} from './project-integration.service';
export { WebhookService, webhookService } from './webhook.service';

// PMA services (new system)
export { BulkPaymentService } from './bulk-payment.service';
export {
  PmaAccessControlService,
  pmaAccessControlService,
} from './pma-access-control.service';

// Export service types
export type {
  IntegrateProjectBillingParams,
  MigrationResult,
  ProjectWithBilling,
} from './project-integration.service';

export type { WebhookProcessingResult } from './webhook.service';
