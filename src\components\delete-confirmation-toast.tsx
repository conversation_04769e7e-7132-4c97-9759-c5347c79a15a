import { toast } from '@/hooks/use-toast';
import * as React from 'react';
import { ToastAction } from './ui/toast';

export interface DeleteConfirmationOptions {
  message?: string;
  title?: string;
  onConfirm: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
}

export function showDeleteConfirmationToast({
  message = 'Are you sure you want to delete this item? This action cannot be undone.',
  title = 'Confirm Deletion',
  onConfirm,
  onCancel,
  confirmText = 'Delete',
  cancelText = 'Cancel',
}: DeleteConfirmationOptions) {
  let dismissed = false;
  const { dismiss } = toast({
    title,
    description: message,
    variant: 'destructive',
    action: (
      <div className="flex gap-2">
        <ToastAction
          altText={cancelText}
          onClick={() => {
            if (!dismissed) {
              dismissed = true;
              dismiss();
              onCancel?.();
            }
          }}
        >
          {cancelText}
        </ToastAction>
        <ToastAction
          altText={confirmText}
          onClick={() => {
            if (!dismissed) {
              dismissed = true;
              dismiss();
              onConfirm();
            }
          }}
          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
        >
          {confirmText}
        </ToastAction>
      </div>
    ),
  });
}

// Optional React hook for convenience
export function useDeleteConfirmationToast() {
  return React.useCallback(showDeleteConfirmationToast, []);
}
