import { supabase } from '@/lib/supabase';
import type { BillPlzBill } from '@/types/billing';
import { createHash, timingSafeEqual } from 'crypto';
import { z } from 'zod';
import { BulkPaymentService } from './bulk-payment.service';

// BillPlz webhook payload schema
export const billPlzWebhookSchema = z.object({
  id: z.string(),
  collection_id: z.string(),
  paid: z.boolean(),
  state: z.enum(['overdue', 'paid']),
  amount: z.number(),
  paid_amount: z.number().optional(),
  due_at: z.string(),
  email: z.string().email(),
  mobile: z.string().optional(),
  name: z.string(),
  url: z.string().url(),
  reference_1_label: z.string().optional(),
  reference_1: z.string().optional(),
  reference_2_label: z.string().optional(),
  reference_2: z.string().optional(),
  redirect_url: z.string().url().optional(),
  callback_url: z.string().url().optional(),
  description: z.string().optional(),
  paid_at: z.string().optional(),
});

export interface WebhookProcessingResult {
  success: boolean;
  error?: string;
  accessRestored: boolean;
  paymentRecordId?: string;
  subscriptionId?: string;
  projectId?: string;
  contractorId?: string;
  billState?: string;
}

export class WebhookService {
  /**
   * Verify BillPlz webhook signature
   */
  static verifyBillPlzSignature(
    body: string,
    signature: string | null,
  ): boolean {
    if (!signature) {
      console.warn('No X-Signature header found in webhook request');
      return false;
    }

    const xSignatureKey = process.env.BILLPLZ_X_SIGNATURE_KEY;
    if (!xSignatureKey) {
      console.error('BILLPLZ_X_SIGNATURE_KEY environment variable not set');
      return false;
    }

    try {
      // Generate expected signature
      const expectedSignature = createHash('sha256')
        .update(xSignatureKey + body)
        .digest('hex');

      // Compare signatures using timing-safe comparison
      const providedSignature = Buffer.from(signature, 'utf8');
      const expectedBuffer = Buffer.from(expectedSignature, 'utf8');

      if (providedSignature.length !== expectedBuffer.length) {
        return false;
      }

      return timingSafeEqual(providedSignature, expectedBuffer);
    } catch (error) {
      console.error('Error verifying BillPlz signature:', error);
      return false;
    }
  }

  /**
   * Process BillPlz webhook for PMA consolidated payments
   */
  static async processBillPlzWebhook(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
  ): Promise<WebhookProcessingResult> {
    try {
      // Get PMA payment records by BillPlz bill ID
      const paymentRecords = await BulkPaymentService.getPaymentRecordsByBillId(
        webhookData.id,
      );

      if (paymentRecords.length === 0) {
        return {
          success: false,
          error: `PMA payment records not found for BillPlz bill: ${webhookData.id}`,
          accessRestored: false,
        };
      }

      console.log(
        `BillPlz webhook received for bill ${webhookData.id}: ${webhookData.state} (${paymentRecords.length} PMA payment records)`,
      );

      // Handle consolidated PMA payments
      return await this.handleConsolidatedPayment(webhookData, paymentRecords);
    } catch (error) {
      console.error('Webhook processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        accessRestored: false,
      };
    }
  }

  /**
   * Handle consolidated payment webhook (PMA payments)
   */
  private static async handleConsolidatedPayment(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
    paymentRecords: Array<{ id: string; pma_subscription_id: string }>,
  ): Promise<WebhookProcessingResult> {
    try {
      if (webhookData.paid && webhookData.state === 'paid') {
        // Update all payment records to paid status
        const updateResult =
          await BulkPaymentService.updatePaymentRecordsStatus(
            webhookData.id,
            'paid',
            webhookData as unknown as BillPlzBill,
            webhookData.paid_at,
          );

        if (!updateResult.success) {
          return {
            success: false,
            error: 'Failed to update payment records to paid status',
            accessRestored: false,
          };
        }

        // Reactivate all PMA subscriptions
        let totalReactivated = 0;
        for (const record of paymentRecords) {
          const { error: subscriptionError } = await supabase
            .from('pma_subscriptions')
            .update({
              status: 'active',
              next_billing_date: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000,
              ).toISOString(), // Next month
              grace_period_ends: null,
            })
            .eq('id', record.pma_subscription_id);

          if (!subscriptionError) {
            totalReactivated++;
          } else {
            console.error(
              `Failed to reactivate subscription ${record.pma_subscription_id}:`,
              subscriptionError,
            );
          }
        }

        console.log(
          `Consolidated payment processed: ${updateResult.updatedCount} payment records updated, ${totalReactivated} subscriptions reactivated`,
        );

        return {
          success: true,
          accessRestored: totalReactivated > 0,
          paymentRecordId: paymentRecords[0].id,
          billState: webhookData.state,
        };
      } else {
        // Handle failed payment
        await BulkPaymentService.updatePaymentRecordsStatus(
          webhookData.id,
          'failed',
          webhookData as unknown as BillPlzBill,
          undefined,
          'Payment overdue - BillPlz webhook',
        );

        return {
          success: true,
          accessRestored: false,
          paymentRecordId: paymentRecords[0]?.id,
          subscriptionId: paymentRecords[0]?.pma_subscription_id,
          billState: webhookData.state,
        };
      }
    } catch (error) {
      console.error('Consolidated payment webhook error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        accessRestored: false,
      };
    }
  }

  /**
   * Convert webhook data to BillPlzBill type
   */
  private static webhookToBillPlzBill(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
  ): BillPlzBill {
    return {
      id: webhookData.id,
      collection_id: webhookData.collection_id,
      paid: webhookData.paid,
      state: webhookData.state,
      amount: webhookData.amount,
      paid_amount: webhookData.paid_amount || 0,
      due_at: webhookData.due_at,
      email: webhookData.email,
      mobile: webhookData.mobile || null,
      name: webhookData.name,
      url: webhookData.url,
      reference_1_label: webhookData.reference_1_label || null,
      reference_1: webhookData.reference_1 || null,
      reference_2_label: webhookData.reference_2_label || null,
      reference_2: webhookData.reference_2 || null,
      redirect_url: webhookData.redirect_url || null,
      callback_url: webhookData.callback_url || '',
      description: webhookData.description || '',
      paid_at: webhookData.paid_at || null,
    };
  }
}

export const webhookService = new WebhookService();
