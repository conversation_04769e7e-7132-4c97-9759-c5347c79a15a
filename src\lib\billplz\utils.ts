/**
 * BillPlz Utility Functions
 * Helper functions for BillPlz payment gateway integration
 */

import { BILLPLZ_DEFAULTS } from './config';
import type {
  BillReferenceOptions,
  AmountConversionOptions,
  StatusMapping,
  BillPlzState,
  BillPlzWebhookPayload,
  WebhookVerificationResult,
} from './types';
import type { PaymentStatus, SubscriptionStatus } from '@/types/billing';

// ================================
// BILL REFERENCE GENERATION
// ================================

/**
 * Generate unique bill reference following BillPlz requirements
 * Format: SIMPLE-YYMMDD-{projectId/subscriptionId}-{timestamp}
 */
export function generateBillReference(
  options: BillReferenceOptions = {},
): string {
  const {
    prefix = BILLPLZ_DEFAULTS.BILL_REFERENCE_PREFIX,
    projectId,
    subscriptionId,
    contractorId,
    timestamp = true,
  } = options;

  const parts: string[] = [prefix];

  // Add date component (YYMMDD)
  const now = new Date();
  const dateString = [
    now.getFullYear().toString().slice(-2),
    (now.getMonth() + 1).toString().padStart(2, '0'),
    now.getDate().toString().padStart(2, '0'),
  ].join('');
  parts.push(dateString);

  // Add identifier (prefer subscription ID, then project ID, then contractor ID)
  const identifier = subscriptionId || projectId || contractorId;
  if (identifier) {
    // Use only the first 8 characters of the UUID for brevity
    parts.push(identifier.slice(0, 8));
  }

  // Add timestamp for uniqueness (if enabled)
  if (timestamp) {
    const timestampString = now.getTime().toString().slice(-6); // Last 6 digits
    parts.push(timestampString);
  }

  return parts.join('-').toUpperCase();
}

/**
 * Parse bill reference to extract components
 */
export function parseBillReference(reference: string): {
  prefix?: string;
  date?: string;
  identifier?: string;
  timestamp?: string;
} {
  const parts = reference.split('-');

  if (parts.length < 2) {
    return {};
  }

  return {
    prefix: parts[0] || undefined,
    date: parts[1] || undefined,
    identifier: parts[2] || undefined,
    timestamp: parts[3] || undefined,
  };
}

// ================================
// AMOUNT CONVERSION UTILITIES
// ================================

/**
 * Convert MYR amount to cents for BillPlz API
 * BillPlz expects amounts in the smallest currency unit (cents)
 */
export function convertMyrToCents(
  amount: number,
  options: AmountConversionOptions = {},
): number {
  const {
    precision = 2,
    validate = true,
    minAmount = BILLPLZ_DEFAULTS.MIN_AMOUNT,
    maxAmount = BILLPLZ_DEFAULTS.MAX_AMOUNT,
  } = options;

  if (validate) {
    validateAmount(amount, minAmount, maxAmount);
  }

  // Round to specified precision and convert to cents
  const rounded = Math.round(amount * Math.pow(10, precision));
  return rounded * Math.pow(10, 2 - precision);
}

/**
 * Convert cents from BillPlz API to MYR amount
 */
export function convertCentsToMyr(
  cents: number,
  options: AmountConversionOptions = {},
): number {
  const { precision = 2 } = options;

  const amount = cents / 100;
  return Math.round(amount * Math.pow(10, precision)) / Math.pow(10, precision);
}

/**
 * Format amount for display (MYR currency)
 */
export function formatAmount(
  amount: number,
  options: {
    currency?: string;
    locale?: string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {},
): string {
  const {
    currency = 'MYR',
    locale = 'en-MY',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
  } = options;

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(amount);
}

/**
 * Validate amount is within acceptable range
 */
export function validateAmount(
  amount: number,
  minAmount: number = BILLPLZ_DEFAULTS.MIN_AMOUNT,
  maxAmount: number = BILLPLZ_DEFAULTS.MAX_AMOUNT,
): void {
  if (typeof amount !== 'number' || isNaN(amount) || !isFinite(amount)) {
    throw new Error('Amount must be a valid number');
  }

  if (amount < minAmount) {
    throw new Error(`Amount must be at least ${formatAmount(minAmount)}`);
  }

  if (amount > maxAmount) {
    throw new Error(`Amount must not exceed ${formatAmount(maxAmount)}`);
  }

  if (amount < 0) {
    throw new Error('Amount must be positive');
  }
}

// ================================
// STATUS MAPPING UTILITIES
// ================================

/**
 * Map BillPlz bill state to our payment status
 */
export function mapBillPlzStateToPaymentStatus(
  state: BillPlzState,
): PaymentStatus {
  const mapping: Record<BillPlzState, PaymentStatus> = {
    open: 'pending',
    paid: 'paid',
    overdue: 'failed',
  };

  return mapping[state] || 'pending';
}

/**
 * Map payment status to BillPlz expected state
 */
export function mapPaymentStatusToBillPlzState(
  status: PaymentStatus,
): BillPlzState {
  const mapping: Record<PaymentStatus, BillPlzState> = {
    pending: 'open',
    paid: 'paid',
    failed: 'overdue',
    cancelled: 'open', // Cancelled bills remain open in BillPlz
    refunded: 'paid', // Refunded bills were originally paid
  };

  return mapping[status] || 'open';
}

/**
 * Determine subscription status based on payment status and current state
 */
export function determineSubscriptionStatus(
  currentStatus: SubscriptionStatus,
  paymentStatus: PaymentStatus,
  gracePeriodActive: boolean = false,
): SubscriptionStatus {
  // If payment is successful, activate subscription
  if (paymentStatus === 'paid') {
    return 'active';
  }

  // If payment failed and we're not in grace period, start grace period
  if (paymentStatus === 'failed' && !gracePeriodActive) {
    return 'grace_period';
  }

  // If in grace period and payment still failing, suspend
  if (gracePeriodActive && paymentStatus === 'failed') {
    return 'suspended';
  }

  // For cancelled payments, cancel subscription
  if (paymentStatus === 'cancelled') {
    return 'cancelled';
  }

  // Default: keep current status
  return currentStatus;
}

/**
 * Get comprehensive status mapping
 */
export function getStatusMapping(
  billPlzState: BillPlzState,
  currentSubscriptionStatus: SubscriptionStatus,
  gracePeriodActive: boolean = false,
): StatusMapping {
  const paymentStatus = mapBillPlzStateToPaymentStatus(billPlzState);
  const subscriptionStatus = determineSubscriptionStatus(
    currentSubscriptionStatus,
    paymentStatus,
    gracePeriodActive,
  );

  return {
    billplzState: billPlzState,
    paymentStatus,
    subscriptionStatus,
  };
}

// ================================
// DATE FORMATTING UTILITIES
// ================================

/**
 * Format date for BillPlz API (ISO 8601)
 */
export function formatDateForBillPlz(date: Date): string {
  return date.toISOString().split('T')[0]; // YYYY-MM-DD format
}

/**
 * Calculate due date for bill (default: 7 days from now)
 */
export function calculateDueDate(
  daysFromNow: number = BILLPLZ_DEFAULTS.DEFAULT_DUE_DAYS,
): Date {
  const dueDate = new Date();
  dueDate.setDate(dueDate.getDate() + daysFromNow);
  return dueDate;
}

/**
 * Parse BillPlz date string to Date object
 */
export function parseBillPlzDate(dateString: string): Date {
  return new Date(dateString);
}

/**
 * Check if date is expired
 */
export function isDateExpired(dateString: string): boolean {
  const date = parseBillPlzDate(dateString);
  return date < new Date();
}

// ================================
// WEBHOOK UTILITIES
// ================================

/**
 * Generate webhook signature for verification
 */
export function generateWebhookSignature(
  payload: string,
  xSignatureKey: string,
): string {
  // Note: In a real implementation, you'd use crypto.createHmac
  // This is a placeholder implementation for server-side only
  if (typeof window !== 'undefined') {
    throw new Error('Webhook signature generation not available in browser');
  }

  // Dynamic import for server-side crypto
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const crypto = require('crypto');
    return crypto
      .createHmac('sha256', xSignatureKey)
      .update(payload)
      .digest('hex');
  } catch {
    throw new Error('Crypto module not available');
  }
}

/**
 * Verify webhook signature
 */
export function verifyWebhookSignature(
  payload: string,
  receivedSignature: string,
  xSignatureKey: string,
): boolean {
  try {
    const expectedSignature = generateWebhookSignature(payload, xSignatureKey);
    return expectedSignature === receivedSignature;
  } catch (error) {
    console.error('Webhook signature verification error:', error);
    return false;
  }
}

/**
 * Verify and parse webhook payload
 */
export function verifyWebhookPayload(
  payload: unknown,
  signature: string,
  xSignatureKey: string,
): WebhookVerificationResult {
  try {
    // Verify payload structure
    if (typeof payload !== 'object' || !payload) {
      return {
        isValid: false,
        error: 'Invalid payload format',
      };
    }

    // Extract signature from payload
    const webhookPayload = payload as Partial<BillPlzWebhookPayload>;
    const payloadSignature = webhookPayload.x_signature;

    if (!payloadSignature) {
      return {
        isValid: false,
        error: 'Missing x_signature in payload',
      };
    }

    // Verify signature matches
    if (payloadSignature !== signature) {
      return {
        isValid: false,
        error: 'Signature mismatch',
      };
    }

    // Verify against our signature key (if we have one)
    const payloadString = JSON.stringify(payload);
    if (!verifyWebhookSignature(payloadString, signature, xSignatureKey)) {
      return {
        isValid: false,
        error: 'Invalid signature',
      };
    }

    return {
      isValid: true,
      payload: webhookPayload as BillPlzWebhookPayload,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

// ================================
// VALIDATION UTILITIES
// ================================

/**
 * Validate email address format
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate Malaysian mobile number
 */
export function validateMalaysianMobile(mobile: string): boolean {
  // Malaysian mobile numbers: +60xxxxxxxxx or 60xxxxxxxxx or 0xxxxxxxxx
  const mobileRegex = /^(\+?60|0)[1-9]\d{7,9}$/;
  return mobileRegex.test(mobile);
}

/**
 * Format Malaysian mobile number for BillPlz
 */
export function formatMalaysianMobile(mobile: string): string {
  // Remove all non-digit characters
  const digits = mobile.replace(/\D/g, '');

  // Convert to international format
  if (digits.startsWith('60')) {
    return `+${digits}`;
  } else if (digits.startsWith('0')) {
    return `+6${digits}`;
  } else if (digits.length >= 9) {
    return `+60${digits}`;
  }

  return mobile; // Return original if cannot format
}

/**
 * Validate BillPlz collection ID format
 */
export function validateCollectionId(collectionId: string): boolean {
  // BillPlz collection IDs are alphanumeric with underscores and hyphens
  const collectionRegex = /^[a-zA-Z0-9_-]+$/;
  return collectionRegex.test(collectionId);
}

/**
 * Validate BillPlz bill ID format
 */
export function validateBillId(billId: string): boolean {
  // BillPlz bill IDs are typically alphanumeric
  const billRegex = /^[a-zA-Z0-9_-]+$/;
  return billRegex.test(billId);
}

// ================================
// UTILITY HELPERS
// ================================

/**
 * Generate unique request ID for logging
 */
export function generateRequestId(): string {
  return [
    Date.now().toString(36),
    Math.random().toString(36).substr(2, 9),
  ].join('-');
}

/**
 * Mask sensitive data for logging
 */
export function maskSensitiveData(data: unknown): unknown {
  if (typeof data !== 'object' || !data) {
    return data;
  }

  const sensitiveFields = ['api_key', 'x_signature', 'password', 'token'];
  const masked = { ...(data as Record<string, unknown>) };

  for (const field of sensitiveFields) {
    if (field in masked && typeof masked[field] === 'string') {
      const value = masked[field] as string;
      masked[field] =
        value.length > 8
          ? `${value.slice(0, 4)}****${value.slice(-4)}`
          : '****';
    }
  }

  return masked;
}

/**
 * Deep clone object (for avoiding mutations)
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item)) as T;
  }

  const clonedObj = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }

  return clonedObj;
}

/**
 * Retry utility with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  maxDelay: number = 30000,
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);

      // Add some jitter to prevent thundering herd
      const jitter = delay * 0.1 * Math.random();
      const actualDelay = delay + jitter;

      console.warn(
        `Attempt ${attempt + 1} failed, retrying in ${Math.round(actualDelay)}ms:`,
        lastError.message,
      );

      await new Promise((resolve) => setTimeout(resolve, actualDelay));
    }
  }

  throw lastError!;
}

// ================================
// EXPORTS
// ================================

export { BILLPLZ_DEFAULTS } from './config';
