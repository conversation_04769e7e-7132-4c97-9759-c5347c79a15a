import { authenticateWithPermission } from '@/features/auth';
import { pmaSubscriptionsService } from '@/features/billing/services/pma-subscriptions.service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/billing/pma-subscriptions
 * List PMA subscriptions with filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user and check permissions for viewing projects
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const contractorId = searchParams.get('contractorId');
    const pmaId = searchParams.get('pmaId');
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const accessAllowed = searchParams.get('accessAllowed');

    // Build filters
    const filters: Record<string, unknown> = {};

    // Role-based filtering: contractors can only see their own subscriptions
    if (user.user_role === 'contractor') {
      filters.contractorId = user.id;
    } else if (contractorId) {
      filters.contractorId = contractorId;
    }

    if (pmaId) filters.pmaId = pmaId;
    if (projectId) filters.projectId = projectId;
    if (status) filters.status = status;
    if (accessAllowed !== null) {
      filters.accessAllowed = accessAllowed === 'true';
    }

    // Get subscriptions
    const result = await pmaSubscriptionsService.getAllWithFilters(filters);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({
      subscriptions: result.data,
      total: result.data.length,
    });
  } catch (error) {
    console.error('GET /api/billing/pma-subscriptions error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * POST /api/billing/pma-subscriptions
 * Create a new PMA subscription
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user and check permissions for creating projects
    const { user, error } = await authenticateWithPermission('projects.create');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    // Parse request body
    const body = await request.json();
    const { pmaId, contractorId, amount, billingCycle, status } = body;

    // Validate required fields
    if (!pmaId) {
      return NextResponse.json(
        { error: 'PMA certificate ID is required' },
        { status: 400 },
      );
    }

    // For contractors, they can only create subscriptions for themselves
    let targetContractorId = contractorId;
    if (user.user_role === 'contractor') {
      targetContractorId = user.id;
    } else if (!contractorId) {
      return NextResponse.json(
        { error: 'Contractor ID is required' },
        { status: 400 },
      );
    }

    // Create subscription
    const result = await pmaSubscriptionsService.create({
      pmaId,
      contractorId: targetContractorId,
      amount,
      billingCycle,
      status,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({ subscription: result.data }, { status: 201 });
  } catch (error) {
    console.error('POST /api/billing/pma-subscriptions error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
