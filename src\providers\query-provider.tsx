'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useEffect, useState } from 'react';

interface QueryProviderProps {
  children: React.ReactNode;
}

interface HttpError extends Error {
  status?: number;
}

export function QueryProvider({ children }: QueryProviderProps) {
  const [isClient, setIsClient] = useState(false);
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000, // 5 minutes
            gcTime: 10 * 60 * 1000, // 10 minutes
            retry: (failureCount, error: HttpError) => {
              // Don't retry on 4xx errors except for 429 (rate limit)
              if (
                error?.status &&
                error.status >= 400 &&
                error.status < 500 &&
                error.status !== 429
              ) {
                return false;
              }
              return failureCount < 3;
            },
            refetchOnWindowFocus: false,
          },
          mutations: {
            retry: (failureCount, error: HttpError) => {
              // Don't retry on 4xx errors except for 429 (rate limit)
              if (
                error?.status &&
                error.status >= 400 &&
                error.status < 500 &&
                error.status !== 429
              ) {
                return false;
              }
              return failureCount < 2;
            },
          },
        },
      }),
  );

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {isClient && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}
