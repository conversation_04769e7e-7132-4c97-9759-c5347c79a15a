import { BulkPaymentService } from '@/features/billing/services/bulk-payment.service';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema for saving a card
const SaveCardSchema = z.object({
  contractor_id: z.string().uuid(),
  token: z.string().min(1),
  fingerprint: z.string().min(1),
  last_four: z
    .string()
    .length(4)
    .regex(/^\d{4}$/),
  brand: z.string().min(1),
  exp_month: z.number().int().min(1).max(12),
  exp_year: z.number().int().min(new Date().getFullYear()),
  is_default: z.boolean().optional().default(false),
});

// Validation schema for card operations
const CardOperationSchema = z.object({
  contractor_id: z.string().uuid(),
  card_id: z.string().uuid(),
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const contractor_id = searchParams.get('contractor_id');

    if (!contractor_id) {
      return NextResponse.json(
        { success: false, error: 'contractor_id is required' },
        { status: 400 },
      );
    }

    // Validate contractor_id is a valid UUID
    if (
      !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        contractor_id,
      )
    ) {
      return NextResponse.json(
        { success: false, error: 'Invalid contractor_id format' },
        { status: 400 },
      );
    }

    const cards = await BulkPaymentService.getSavedCards(contractor_id);

    return NextResponse.json({
      success: true,
      data: cards,
    });
  } catch (error) {
    console.error('Get saved cards API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = SaveCardSchema.parse(body);

    // Save the card
    const result =
      await BulkPaymentService.saveCardForContractor(validatedData);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to save card',
        },
        { status: 400 },
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        card_id: result.card_id,
      },
    });
  } catch (error) {
    console.error('Save card API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid card data',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = CardOperationSchema.parse(body);

    // Delete the card
    const result = await BulkPaymentService.deleteSavedCard(validatedData);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to delete card',
        },
        { status: 400 },
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Card deleted successfully',
    });
  } catch (error) {
    console.error('Delete card API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = CardOperationSchema.parse(body);

    // Set card as default
    const result = await BulkPaymentService.setDefaultCard(validatedData);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to set default card',
        },
        { status: 400 },
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Default card updated successfully',
    });
  } catch (error) {
    console.error('Set default card API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
