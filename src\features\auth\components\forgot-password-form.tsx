'use client';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  useAuthTranslations,
  useValidationTranslations,
} from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { toast } from 'sonner';
import { usePasswordReset } from '../hooks/use-auth';
import {
  createForgotPasswordSchema,
  type ForgotPasswordFormValues,
} from '../schemas';

interface ForgotPasswordFormProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function ForgotPasswordForm({
  className,
  ...props
}: ForgotPasswordFormProps) {
  const router = useRouter();
  const passwordResetMutation = usePasswordReset();
  const auth = useAuthTranslations();
  const validation = useValidationTranslations();

  const formSchema = createForgotPasswordSchema(validation, auth);

  type FormValues = ForgotPasswordFormValues;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading(auth('sendingResetLink'));

      await passwordResetMutation.mutateAsync(values.email);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success(auth('resetLinkSent'));

      // Redirect to verify-code page with email parameter
      router.push(`/verify-code?email=${encodeURIComponent(values.email)}`);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : auth('resetLinkFailed');

      toast.error(errorMessage);
    }
  };

  return (
    <div
      className={cn('flex flex-col gap-6 w-full max-w-md mx-auto', className)}
      {...props}
    >
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-xl sm:text-2xl font-bold">
          {auth('forgotPasswordTitle')}
        </h1>
        <p className="text-muted-foreground text-sm text-balance px-4 sm:px-0">
          {auth('forgotPasswordSubtitle')}
        </p>
      </div>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4 sm:space-y-6"
        >
          <div className="grid gap-4 sm:gap-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="grid gap-2">
                  <FormLabel className="text-sm sm:text-base">
                    {auth('email')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('emailPlaceholder')}
                      type="email"
                      className="h-10 sm:h-11"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="w-full h-10 sm:h-11 text-sm sm:text-base"
              disabled={passwordResetMutation.isPending}
            >
              {passwordResetMutation.isPending
                ? auth('sendingResetLink')
                : auth('sendResetLink')}
            </Button>
          </div>
          <div className="text-center text-xs sm:text-sm">
            <Link
              href="/admin/login"
              className="underline underline-offset-4 hover:no-underline hover:text-primary"
            >
              {auth('backToLogin')}
            </Link>
          </div>
        </form>
      </Form>
    </div>
  );
}
