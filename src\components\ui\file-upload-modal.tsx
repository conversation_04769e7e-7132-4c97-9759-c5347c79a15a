import { uploadToOBS } from '@/lib/obs-upload';
import {
  AlertCircle,
  CheckCircle,
  FileText,
  Loader2,
  Upload,
  X,
} from 'lucide-react';
import React, { useState } from 'react';
import { Button } from './button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './dialog';
import { FileUploadDropzone } from './file-upload-dropzone';

export interface FileUploadResult {
  file: File;
  url: string;
}

interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (results: FileUploadResult[]) => Promise<void>;
  title: string;
  description: string;
  maxFiles?: number;
  acceptedTypes?: string;
  maxSize?: number;
  isLoading?: boolean;
  folderPath?: string;
}

interface FileUploadStatus {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

/**
 * Generic modal component for uploading files with drag-and-drop functionality
 */
export function FileUploadModal({
  isOpen,
  onClose,
  onUpload,
  title,
  description,
  maxFiles = 1,
  acceptedTypes = '.pdf,.jpg,.jpeg,.png',
  maxSize = 10 * 1024 * 1024, // 10MB default
  isLoading = false,
  folderPath,
}: FileUploadModalProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [fileStatuses, setFileStatuses] = useState<FileUploadStatus[]>([]);

  const handleFilesChange = (files: File[]) => {
    // Append new files to existing selection instead of replacing
    setSelectedFiles((currentFiles) => {
      const newFiles = files.filter(
        (newFile) =>
          !currentFiles.some(
            (existingFile) =>
              existingFile.name === newFile.name &&
              existingFile.size === newFile.size,
          ),
      );
      const updatedFiles = [...currentFiles, ...newFiles];

      // Update file statuses for all files (including existing ones)
      setFileStatuses((currentStatuses) => {
        const existingStatuses = currentStatuses.filter((status) =>
          updatedFiles.some((file) => file.name === status.file.name),
        );
        const newStatuses = newFiles.map((file) => ({
          file,
          progress: 0,
          status: 'pending' as const,
        }));
        return [...existingStatuses, ...newStatuses];
      });

      return updatedFiles;
    });
    setUploadError(null);
  };

  // Calculate overall upload progress
  const overallProgress = React.useMemo(() => {
    if (fileStatuses.length === 0) return 0;
    const totalProgress = fileStatuses.reduce(
      (sum, status) => sum + status.progress,
      0,
    );
    return Math.round(totalProgress / fileStatuses.length);
  }, [fileStatuses]);

  const isUploading = fileStatuses.some(
    (status) => status.status === 'uploading',
  );

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const removeFile = (fileName: string) => {
    // Remove from selected files
    const updatedFiles = selectedFiles.filter((file) => file.name !== fileName);
    setSelectedFiles(updatedFiles);

    // Remove from file statuses
    setFileStatuses((current) =>
      current.filter((status) => status.file.name !== fileName),
    );

    // Clear upload error if no files selected
    if (updatedFiles.length === 0) {
      setUploadError(null);
    }
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      setUploadError('Please select at least one file to upload');
      return;
    }

    try {
      setUploadError(null);

      // Ensure we have file statuses for all selected files and set them to uploading
      setFileStatuses(() =>
        selectedFiles.map((file) => ({
          file,
          progress: 0,
          status: 'uploading',
        })),
      );

      // Upload all files using uploadToOBS
      const uploadResults: FileUploadResult[] = [];
      const uploadPromises = selectedFiles.map(async (file) => {
        try {
          const url = await uploadToOBS({
            file,
            folder: folderPath,
            onProgress: (progress: number) => {
              setFileStatuses((current) =>
                current.map((status) =>
                  status.file.name === file.name
                    ? { ...status, progress }
                    : status,
                ),
              );
            },
          });

          // Update status to completed
          setFileStatuses((current) =>
            current.map((status) =>
              status.file.name === file.name
                ? { ...status, status: 'completed', progress: 100 }
                : status,
            ),
          );

          // Store the upload result
          uploadResults.push({ file, url });
          return { file, url };
        } catch (error) {
          // Update status to error for this file
          setFileStatuses((current) =>
            current.map((status) =>
              status.file.name === file.name
                ? {
                    ...status,
                    status: 'error',
                    progress: 0,
                    error:
                      error instanceof Error ? error.message : 'Upload failed',
                  }
                : status,
            ),
          );
          throw error;
        }
      });

      // Wait for all uploads to complete
      await Promise.all(uploadPromises);

      // Show completed state briefly before closing
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Notify parent component of completed uploads with both files and URLs
      await onUpload(uploadResults);

      // Clear and close on success
      setSelectedFiles([]);
      setFileStatuses([]);
      onClose();
    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'Upload failed');
    }
  };

  const handleClose = () => {
    if (!isLoading && !isUploading) {
      setSelectedFiles([]);
      setUploadError(null);
      setFileStatuses([]);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-hidden w-full max-w-[calc(100vw-2rem)]">
        <DialogHeader className="space-y-4 pb-2">
          <DialogTitle className="flex items-center gap-3 text-xl font-semibold">
            <div className="p-2 rounded-lg bg-primary/10">
              <Upload className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1 min-w-0 overflow-hidden">
              <h3 className="text-xl font-semibold truncate" title={title}>
                {title}
              </h3>
              {isUploading && (
                <div className="mt-2 space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Upload Progress
                    </span>
                    <span className="font-medium text-primary">
                      {overallProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                    <div
                      className="h-full bg-primary transition-all duration-300 ease-out rounded-full"
                      style={{ width: `${overallProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </DialogTitle>
          <DialogDescription className="text-base text-muted-foreground leading-relaxed">
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-8 py-2 overflow-hidden">
          <FileUploadDropzone
            onFilesChange={handleFilesChange}
            accept={acceptedTypes}
            maxFiles={maxFiles}
            maxSize={maxSize}
            disabled={isLoading || isUploading}
            files={selectedFiles}
          />

          {/* Unified File Section - Show both selected and upload status */}
          {selectedFiles.length > 0 && (
            <div className="space-y-3 overflow-hidden">
              <h4 className="text-sm font-semibold text-foreground">
                {maxFiles === 1
                  ? 'Selected Files (1):'
                  : `Selected Files (${selectedFiles.length}):`}
              </h4>
              <div className="space-y-2 overflow-hidden">
                {selectedFiles.map((file) => {
                  const fileStatus = fileStatuses.find(
                    (status) => status.file.name === file.name,
                  );

                  return (
                    <div
                      key={file.name}
                      className="relative overflow-hidden rounded-lg border bg-card transition-all duration-200 w-full"
                    >
                      {/* Progress background for uploading files */}
                      {fileStatus?.status === 'uploading' && (
                        <div
                          className="absolute inset-0 bg-primary/5 transition-all duration-300 ease-out"
                          style={{
                            background: `linear-gradient(to right, hsl(var(--primary) / 0.08) ${fileStatus.progress}%, transparent ${fileStatus.progress}%)`,
                          }}
                        />
                      )}

                      <div className="relative flex items-center justify-between p-4 min-w-0">
                        <div className="flex items-center space-x-3 min-w-0 flex-1 overflow-hidden">
                          <div className="flex-shrink-0">
                            <FileText className="w-5 h-5 text-primary" />
                          </div>
                          <div className="min-w-0 flex-1 overflow-hidden">
                            <p
                              className="text-sm font-medium text-foreground truncate"
                              title={file.name}
                            >
                              {file.name}
                            </p>
                            <div className="flex items-center space-x-2 mt-1">
                              <p className="text-xs text-muted-foreground">
                                {formatFileSize(file.size)}
                              </p>
                              {fileStatus?.status === 'uploading' && (
                                <p className="text-xs text-primary font-medium">
                                  • {fileStatus.progress}% uploaded
                                </p>
                              )}
                              {fileStatus?.status === 'completed' && (
                                <p className="text-xs text-green-600 font-medium">
                                  • Upload complete
                                </p>
                              )}
                              {fileStatus?.status === 'error' && (
                                <p
                                  className="text-xs text-destructive truncate max-w-[150px]"
                                  title={fileStatus.error}
                                >
                                  • {fileStatus.error}
                                </p>
                              )}
                            </div>

                            {/* Progress bar for uploading files */}
                            {fileStatus?.status === 'uploading' && (
                              <div className="mt-2 w-full bg-muted rounded-full h-1.5 overflow-hidden">
                                <div
                                  className="h-full bg-primary transition-all duration-300 ease-out rounded-full"
                                  style={{ width: `${fileStatus.progress}%` }}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex-shrink-0">
                          {fileStatus?.status === 'uploading' && (
                            <div className="flex items-center space-x-2">
                              <Loader2 className="w-4 h-4 animate-spin text-primary" />
                              <span className="text-xs font-mono text-primary min-w-[3ch]">
                                {fileStatus.progress}%
                              </span>
                            </div>
                          )}
                          {fileStatus?.status === 'completed' && (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          )}
                          {fileStatus?.status === 'error' && (
                            <AlertCircle className="w-4 h-4 text-destructive" />
                          )}

                          {/* Remove file button - only show when not uploading */}
                          {(!fileStatus ||
                            fileStatus.status !== 'uploading') && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeFile(file.name);
                              }}
                              disabled={isLoading || isUploading}
                              className="ml-2 h-8 w-8 p-0 hover:bg-destructive/10"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {uploadError && (
            <div className="p-4 rounded-lg bg-destructive/10 border border-destructive/20 flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-destructive mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-destructive">
                  Upload Error
                </p>
                <p className="text-sm text-destructive/80 mt-1 break-words">
                  {uploadError}
                </p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end gap-3 pt-6 border-t">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading || isUploading}
            className="min-w-[100px]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={selectedFiles.length === 0 || isLoading || isUploading}
            className="min-w-[120px]"
          >
            {isUploading ? (
              <>
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload {selectedFiles.length > 0 && `(${selectedFiles.length})`}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
