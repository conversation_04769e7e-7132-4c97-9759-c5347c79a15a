import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  removeMemberFromProject,
  restoreMemberToProject,
} from '../services/member-service';
import { toast } from 'sonner';

export function useRemoveMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: removeMemberFromProject,
    onSuccess: (data, variables) => {
      // Invalidate and refetch project data to update the UI
      queryClient.invalidateQueries({
        queryKey: ['project', variables.projectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['projects'],
      });
    },
    onError: (error) => {
      console.error('Remove member mutation error:', error);
    },
  });
}

export function useRestoreMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: restoreMemberToProject,
    onSuccess: (data, variables) => {
      // Show success toast
      toast.success('Member restored successfully');

      // Invalidate and refetch project data to update the UI
      queryClient.invalidateQueries({
        queryKey: ['project', variables.projectId],
      });
      queryClient.invalidateQueries({
        queryKey: ['projects'],
      });
    },
    onError: (error) => {
      console.error('Restore member mutation error:', error);

      // Show error toast
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to restore member';
      toast.error(errorMessage);
    },
  });
}
