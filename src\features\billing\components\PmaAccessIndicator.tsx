'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { usePmaAccess } from '@/features/billing/hooks/usePmaAccess';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  CreditCard,
  XCircle,
} from 'lucide-react';
import Link from 'next/link';

interface PmaAccessIndicatorProps {
  pmaId: string;
  pmaCertNumber?: string;
  showDetails?: boolean;
  className?: string;
}

export function PmaAccessIndicator({
  pmaId,
  pmaCertNumber,
  showDetails = false,
  className,
}: PmaAccessIndicatorProps) {
  const {
    isLoadingAccess,
    accessError,
    hasAccess,
    accessReason,
    subscription,
    gracePeriodDays,
    accessMessage,
  } = usePmaAccess(pmaId);

  if (isLoadingAccess) {
    return (
      <div className={className}>
        <Skeleton className="h-6 w-24" />
      </div>
    );
  }

  if (accessError) {
    return (
      <Badge variant="destructive" className={className}>
        <XCircle className="w-3 h-3 mr-1" />
        Error
      </Badge>
    );
  }

  const getAccessBadge = () => {
    switch (accessReason) {
      case 'active':
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 border-green-200"
          >
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case 'grace_period':
        return (
          <Badge
            variant="secondary"
            className="bg-yellow-100 text-yellow-800 border-yellow-200"
          >
            <Clock className="w-3 h-3 mr-1" />
            Grace Period ({gracePeriodDays}d)
          </Badge>
        );
      case 'admin_bypass':
        return (
          <Badge
            variant="secondary"
            className="bg-blue-100 text-blue-800 border-blue-200"
          >
            <CheckCircle className="w-3 h-3 mr-1" />
            Admin Access
          </Badge>
        );
      case 'no_subscription':
        return (
          <Badge variant="outline" className="border-red-200 text-red-700">
            <CreditCard className="w-3 h-3 mr-1" />
            No Subscription
          </Badge>
        );
      case 'suspended':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Suspended
          </Badge>
        );
      case 'expired':
        return (
          <Badge variant="destructive">
            <AlertCircle className="w-3 h-3 mr-1" />
            Expired
          </Badge>
        );
      case 'invalid_pma':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Invalid PMA
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <AlertCircle className="w-3 h-3 mr-1" />
            Unknown
          </Badge>
        );
    }
  };

  const getActionButton = () => {
    if (hasAccess) return null;

    switch (accessReason) {
      case 'no_subscription':
        return (
          <Button asChild size="sm" className="ml-2">
            <Link href={`/billing/subscribe?pmaId=${pmaId}`}>Subscribe</Link>
          </Button>
        );
      case 'suspended':
      case 'expired':
        return (
          <Button asChild size="sm" variant="outline" className="ml-2">
            <Link href={`/billing/payment?pmaId=${pmaId}`}>Pay Now</Link>
          </Button>
        );
      default:
        return null;
    }
  };

  if (!showDetails) {
    return (
      <div className={`flex items-center ${className}`}>
        {getAccessBadge()}
        {getActionButton()}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">
          PMA Access Status
          {pmaCertNumber && (
            <span className="text-muted-foreground ml-2 font-normal">
              ({pmaCertNumber})
            </span>
          )}
        </CardTitle>
        <CardDescription>
          Current access status for this PMA certificate
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Status:</span>
          {getAccessBadge()}
        </div>

        {accessMessage && (
          <div className="flex items-start space-x-2">
            <span className="text-sm font-medium">Message:</span>
            <span className="text-sm text-muted-foreground">
              {accessMessage}
            </span>
          </div>
        )}

        {subscription && (
          <>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Amount:</span>
              <span className="text-sm">
                {subscription.currency || 'MYR'}{' '}
                {subscription.calculated_amount || 0}
              </span>
            </div>

            {subscription.next_billing_date && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Next Billing:</span>
                <span className="text-sm">
                  {new Date(
                    subscription.next_billing_date,
                  ).toLocaleDateString()}
                </span>
              </div>
            )}

            {subscription.grace_period_ends && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Grace Period Ends:</span>
                <span className="text-sm text-yellow-600">
                  {new Date(
                    subscription.grace_period_ends,
                  ).toLocaleDateString()}
                </span>
              </div>
            )}
          </>
        )}

        {getActionButton() && (
          <div className="pt-2 border-t">{getActionButton()}</div>
        )}
      </CardContent>
    </Card>
  );
}

export default PmaAccessIndicator;
