import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';

type PmaSubscription = Database['public']['Tables']['pma_subscriptions']['Row'];
type PmaSubscriptionUpdate =
  Database['public']['Tables']['pma_subscriptions']['Update'];

type SubscriptionStatus = Database['public']['Enums']['subscription_status'];

export interface PmaSubscriptionWithDetails extends PmaSubscription {
  pma_certificates: {
    id: string;
    project_id: string | null;
    pma_number: string | null;
    status: string;
    expiry_date: string;
    location: string | null;
  } | null;
  contractors: {
    id: string;
    name: string;
  } | null;
}

export interface CreatePmaSubscriptionParams {
  pmaId: string;
  contractorId: string;
  amount?: number;
  billingCycle?: 'monthly';
  status?: SubscriptionStatus;
}

export interface PmaSubscriptionFilters {
  contractorId?: string;
  pmaId?: string;
  projectId?: string;
  status?: SubscriptionStatus;
  accessAllowed?: boolean;
}

export class PmaSubscriptionsService {
  /**
   * Create a new PMA subscription
   */
  async create(params: CreatePmaSubscriptionParams): Promise<{
    data: PmaSubscription | null;
    error: string | null;
  }> {
    try {
      const { pmaId, contractorId, amount = 150.0 } = params;

      // Validate that PMA certificate exists and is valid
      const { data: pmaCert } = await supabase
        .from('pma_certificates')
        .select('id, status, project_id')
        .eq('id', pmaId)
        .in('status', ['valid', 'validating']) // Allow both valid and validating certificates
        .is('deleted_at', null)
        .single();

      if (!pmaCert) {
        return {
          data: null,
          error:
            'PMA certificate not found, deleted, or has invalid status. Only valid and validating certificates can have subscriptions.',
        };
      }

      // Check if subscription already exists
      const { data: existingSubscription } = await supabase
        .from('pma_subscriptions')
        .select('id')
        .eq('pma_certificate_id', pmaId)
        .single();

      if (existingSubscription) {
        return {
          data: null,
          error: 'Subscription already exists for this PMA certificate',
        };
      }

      // Use the database function to create subscription with automatic trial detection
      const { data: subscriptionId, error } = await supabase.rpc(
        'create_pma_subscription_with_trial',
        {
          p_pma_certificate_id: pmaId,
          p_contractor_id: contractorId,
          p_amount: amount,
        },
      );

      if (error) {
        console.error('Create PMA subscription error:', error);
        return { data: null, error: error.message };
      }

      // Fetch the created subscription with all details
      const { data, error: fetchError } = await supabase
        .from('pma_subscriptions')
        .select()
        .eq('id', subscriptionId)
        .single();

      if (fetchError) {
        console.error('Fetch PMA subscription error:', fetchError);
        return { data: null, error: fetchError.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Create PMA subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get PMA subscription by ID
   */
  async getById(subscriptionId: string): Promise<{
    data: PmaSubscriptionWithDetails | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status,
            expiry_date,
            location
          ),
          contractors!inner (
            id,
            name
          )
        `,
        )
        .eq('id', subscriptionId)
        .single();

      if (error) {
        console.error('Get PMA subscription error:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Get PMA subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get PMA subscription by PMA certificate ID
   */
  async getByPmaId(pmaId: string): Promise<{
    data: PmaSubscriptionWithDetails | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status,
            expiry_date,
            location
          ),
          contractors!inner (
            id,
            name
          )
        `,
        )
        .eq('pma_certificate_id', pmaId)
        .single();

      if (error) {
        console.error('Get PMA subscription by PMA ID error:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Get PMA subscription by PMA ID error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get all PMA subscriptions for a project
   */
  async getByProjectId(projectId: string): Promise<{
    data: PmaSubscriptionWithDetails[];
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status,
            expiry_date,
            location
          ),
          contractors!inner (
            id,
            name
          )
        `,
        )
        .eq('pma_certificates.project_id', projectId);

      if (error) {
        console.error('Get PMA subscriptions by project ID error:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get PMA subscriptions by project ID error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get all PMA subscriptions with filters
   */
  async getAllWithFilters(filters: PmaSubscriptionFilters): Promise<{
    data: PmaSubscriptionWithDetails[];
    error: string | null;
  }> {
    try {
      let query = supabase.from('pma_subscriptions').select(`
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status,
            expiry_date,
            location
          ),
          contractors!inner (
            id,
            name
          )
        `);

      if (filters.contractorId) {
        query = query.eq('contractor_id', filters.contractorId);
      }

      if (filters.pmaId) {
        query = query.eq('pma_certificate_id', filters.pmaId);
      }

      if (filters.projectId) {
        query = query.eq('pma_certificates.project_id', filters.projectId);
      }

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.accessAllowed !== undefined) {
        if (filters.accessAllowed) {
          query = query.in('status', ['active', 'grace_period', 'trial']);
        } else {
          query = query.not('status', 'in', '(active,grace_period,trial)');
        }
      }

      const { data, error } = await query;

      if (error) {
        console.error('Get PMA subscriptions with filters error:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get PMA subscriptions with filters error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update PMA subscription
   */
  async update(
    subscriptionId: string,
    updates: PmaSubscriptionUpdate,
  ): Promise<{
    data: PmaSubscription | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('pma_subscriptions')
        .update(updates)
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        console.error('Update PMA subscription error:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Update PMA subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Activate PMA subscription
   */
  async activateSubscription(subscriptionId: string): Promise<{
    data: PmaSubscription | null;
    error: string | null;
  }> {
    try {
      const nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

      const updates: PmaSubscriptionUpdate = {
        status: 'active',
        next_billing_date: nextBillingDate.toISOString(),
        grace_period_ends: null,
      };

      return await this.update(subscriptionId, updates);
    } catch (error) {
      console.error('Activate PMA subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Suspend PMA subscription
   */
  async suspendSubscription(
    subscriptionId: string,
    _reason: string = 'Payment failed',
  ): Promise<{
    data: PmaSubscription | null;
    error: string | null;
  }> {
    try {
      const updates: PmaSubscriptionUpdate = {
        status: 'suspended',
        grace_period_ends: null,
      };

      return await this.update(subscriptionId, updates);
    } catch (error) {
      console.error('Suspend PMA subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Cancel PMA subscription
   * - If in grace period or trial: suspend immediately (no extended access)
   * - If active: cancel with access until 27th (via cron job)
   */
  async cancelSubscription(subscriptionId: string): Promise<{
    data: PmaSubscription | null;
    error: string | null;
  }> {
    try {
      // First, get the current subscription to check its status
      const { data: currentSubscription, error: fetchError } = await supabase
        .from('pma_subscriptions')
        .select('status')
        .eq('id', subscriptionId)
        .single();

      if (fetchError || !currentSubscription) {
        return {
          data: null,
          error: 'Subscription not found',
        };
      }

      // If in grace period or trial, suspend immediately (no extended access)
      if (
        currentSubscription.status === 'grace_period' ||
        currentSubscription.status === 'trial'
      ) {
        const updates: PmaSubscriptionUpdate = {
          status: 'suspended',
          next_billing_date: null,
          grace_period_ends: null,
          trial_ends_at: null,
          cancelled_at: new Date().toISOString(),
        };
        return await this.update(subscriptionId, updates);
      }

      // Otherwise (active status), cancel with access until 27th (via cron job)
      const updates: PmaSubscriptionUpdate = {
        status: 'cancelled',
        next_billing_date: null,
        grace_period_ends: null,
        cancelled_at: new Date().toISOString(),
      };

      return await this.update(subscriptionId, updates);
    } catch (error) {
      console.error('Cancel PMA subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Set subscription to grace period
   */
  async setGracePeriod(
    subscriptionId: string,
    gracePeriodDays: number = 7,
  ): Promise<{
    data: PmaSubscription | null;
    error: string | null;
  }> {
    try {
      const gracePeriodEnds = new Date(
        Date.now() + gracePeriodDays * 24 * 60 * 60 * 1000,
      );

      const updates: PmaSubscriptionUpdate = {
        status: 'grace_period',
        grace_period_ends: gracePeriodEnds.toISOString(),
      };

      return await this.update(subscriptionId, updates);
    } catch (error) {
      console.error('Set grace period error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Reactivate subscription (from suspended/cancelled)
   */
  async reactivateSubscription(subscriptionId: string): Promise<{
    data: PmaSubscription | null;
    error: string | null;
  }> {
    try {
      const nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

      const updates: PmaSubscriptionUpdate = {
        status: 'active',
        next_billing_date: nextBillingDate.toISOString(),
        grace_period_ends: null,
        cancelled_at: null,
      };

      return await this.update(subscriptionId, updates);
    } catch (error) {
      console.error('Reactivate PMA subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get subscriptions requiring billing (active subscriptions due for payment)
   */
  async getSubscriptionsDueForBilling(): Promise<{
    data: PmaSubscriptionWithDetails[];
    error: string | null;
  }> {
    try {
      const now = new Date().toISOString();

      const { data, error } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status,
            expiry_date,
            location
          ),
          contractors!inner (
            id,
            name
          )
        `,
        )
        .eq('status', 'active')
        .lte('next_billing_date', now);

      if (error) {
        console.error('Get subscriptions due for billing error:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get subscriptions due for billing error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get subscriptions in grace period that are expiring
   */
  async getExpiringGracePeriodSubscriptions(): Promise<{
    data: PmaSubscriptionWithDetails[];
    error: string | null;
  }> {
    try {
      const now = new Date().toISOString();

      const { data, error } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status,
            expiry_date,
            location
          ),
          contractors!inner (
            id,
            name
          )
        `,
        )
        .eq('status', 'grace_period')
        .lte('grace_period_ends', now);

      if (error) {
        console.error('Get expiring grace period subscriptions error:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get expiring grace period subscriptions error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get cancelled subscriptions (for monitoring/reporting)
   */
  async getCancelledSubscriptions(): Promise<{
    data: PmaSubscriptionWithDetails[];
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status,
            expiry_date,
            location
          ),
          contractors!inner (
            id,
            name
          )
        `,
        )
        .eq('status', 'cancelled');

      if (error) {
        console.error('Get cancelled subscriptions error:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get cancelled subscriptions error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get existing subscriptions by certificate IDs
   */
  async getExistingByCertificateIds(certificateIds: string[]): Promise<{
    data: { pma_certificate_id: string }[];
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('pma_subscriptions')
        .select('pma_certificate_id')
        .in('pma_certificate_id', certificateIds);

      if (error) {
        console.error(
          'Get existing subscriptions by certificate IDs error:',
          error,
        );
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error(
        'Get existing subscriptions by certificate IDs error:',
        error,
      );
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Calculate initial pro-rated amount for new subscriptions
   */
  private async calculateInitialProratedAmount(
    status: SubscriptionStatus,
    baseAmount: number,
  ): Promise<number> {
    try {
      // Only pro-rate for pending_payment subscriptions
      if (status !== 'pending_payment') {
        return baseAmount;
      }

      // Use the database function to calculate pro-rated amount
      const { data, error } = await supabase.rpc(
        'calculate_prorated_pma_amount',
        {
          subscription_created_date: new Date().toISOString(),
          calculation_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
        },
      );

      if (error) {
        console.error('Error calculating initial pro-rated amount:', error);
        return baseAmount; // Fallback to base amount if calculation fails
      }

      return data || baseAmount;
    } catch (error) {
      console.error('Error calculating initial pro-rated amount:', error);
      return baseAmount; // Fallback to base amount if calculation fails
    }
  }
}

export const pmaSubscriptionsService = new PmaSubscriptionsService();
