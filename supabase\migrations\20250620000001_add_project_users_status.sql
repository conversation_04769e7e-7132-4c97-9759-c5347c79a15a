-- ================================
-- ADD STATUS COLUMN TO PROJECT_USERS
-- Migration to add invitation status tracking
-- ================================

-- Create the invitation status enum
DO $$ BEGIN 
  CREATE TYPE project_user_status AS ENUM ('invited', 'accepted', 'declined'); 
EXCEPTION 
  WHEN duplicate_object THEN NULL; 
END $$;

-- Add status column to project_users table
ALTER TABLE project_users 
ADD COLUMN status project_user_status DEFAULT 'accepted';

-- Update existing records to 'accepted' status (they were directly added)
UPDATE project_users 
SET status = 'accepted' 
WHERE status IS NULL;

-- Make status column NOT NULL after setting defaults
ALTER TABLE project_users 
ALTER COLUMN status SET NOT NULL;

-- Add index for better query performance
CREATE INDEX idx_project_users_status ON project_users(status);

-- Add comment for documentation
COMMENT ON COLUMN project_users.status IS 'Invitation status: invited (pending), accepted (active member), declined (rejected invitation)';

-- ================================
-- UPDATE RLS POLICY TO USE STATUS COLUMN
-- ================================

-- Drop and recreate the projects view policy to include status check
DROP POLICY IF EXISTS "Users can view projects they are members of" ON projects;

CREATE POLICY "Users can view projects they are members of"
ON projects
FOR SELECT
TO public
USING (
  -- Contractors can see their own projects
  contractor_id IN (
    SELECT u.contractor_id
    FROM users u 
    WHERE u.id = auth.uid() AND u.user_role = 'contractor'
  )
  OR
  -- Users can see projects they're members of (avoiding recursion by checking directly)
  id IN (
    SELECT pu.project_id
    FROM project_users pu
    WHERE pu.user_id = auth.uid() 
    AND pu.status = 'accepted' 
    AND pu.is_active = true
  )
);
