import { useEffect, useState } from 'react';

interface UsePaginationProps {
  totalRecords: number;
  initialPage?: number;
  initialPageSize?: number;
  pageSizeOptions?: number[];
}

export function usePagination({
  totalRecords,
  initialPage = 1,
  initialPageSize = 10,
  pageSizeOptions = [10, 20, 30, 50],
}: UsePaginationProps) {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [actualTotalRecords, setActualTotalRecords] = useState(totalRecords);

  const totalPages = Math.max(1, Math.ceil(actualTotalRecords / pageSize));

  // Update total records when the prop changes
  useEffect(() => {
    setActualTotalRecords(totalRecords);
  }, [totalRecords]);

  // Reset to first page if currentPage is out of range when totalPages changes
  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [totalPages, currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page on size change
  };

  const updateTotalRecords = (newTotal: number) => {
    setActualTotalRecords(newTotal);
  };

  return {
    currentPage,
    pageSize,
    totalPages,
    pageSizeOptions,
    setCurrentPage: handlePageChange,
    setPageSize: handlePageSizeChange,
    updateTotalRecords,
  };
}
