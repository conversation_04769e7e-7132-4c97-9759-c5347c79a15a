import { supabase } from '@/lib/supabase';
import type { Competent<PERSON>erson } from '@/types/competent-person';

export interface CompetentPersonSearchParams {
  contractorId: string;
  searchQuery?: string;
  limit?: number;
  offset?: number;
}

export interface CompetentPersonSearchResult {
  data: CompetentPerson[];
  count: number;
  hasMore: boolean;
}

/**
 * Fetch competent persons with optional search functionality
 * Performs database-level search for better performance with large datasets
 */
export async function fetchCompetentPersonsWithSearch({
  contractorId,
  searchQuery,
  limit = 50,
  offset = 0,
}: CompetentPersonSearchParams): Promise<CompetentPersonSearchResult> {
  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  // If no search query, fetch all competent persons
  if (!searchQuery || !searchQuery.trim()) {
    const { data, error, count } = await supabase
      .from('competent_person')
      .select('*', { count: 'exact' })
      .eq('contractor_id', contractorId)
      .is('deleted_at', null)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      data: (data as unknown as CompetentPerson[]) || [],
      count: count || 0,
      hasMore: (count || 0) > offset + limit,
    };
  }

  // For search queries, use textSearch or a simpler approach
  const trimmedQuery = searchQuery.trim().toLowerCase();

  // TODO: Optimize this to use proper database-level search once database supports it
  // Current approach: Get all data first, then filter in JavaScript as a fallback
  // This ensures compatibility while maintaining search functionality
  const { data, error } = await supabase
    .from('competent_person')
    .select('*')
    .eq('contractor_id', contractorId)
    .is('deleted_at', null)
    .order('created_at', { ascending: false });

  if (error) throw error;

  const allData = (data as unknown as CompetentPerson[]) || [];

  // Filter the results in JavaScript
  const filteredData = allData.filter(
    (person) =>
      person.name.toLowerCase().includes(trimmedQuery) ||
      person.ic_no.toLowerCase().includes(trimmedQuery) ||
      person.cp_type.toLowerCase().includes(trimmedQuery) ||
      (person.phone_no &&
        person.phone_no.toLowerCase().includes(trimmedQuery)) ||
      (person.cp_registeration_no &&
        person.cp_registeration_no.toLowerCase().includes(trimmedQuery)),
  );

  // Apply pagination to filtered results
  const paginatedData = filteredData.slice(offset, offset + limit);

  return {
    data: paginatedData,
    count: filteredData.length,
    hasMore: filteredData.length > offset + limit,
  };
}

/**
 * Fetch all competent persons for statistics (without pagination)
 * Used for summary cards and statistics
 */
export async function fetchAllCompetentPersonsForStats(
  contractorId: string,
): Promise<CompetentPerson[]> {
  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  const { data, error } = await supabase
    .from('competent_person')
    .select('*')
    .eq('contractor_id', contractorId)
    .is('deleted_at', null)
    .order('created_at', { ascending: false });

  if (error) {
    throw error;
  }

  return (data as unknown as CompetentPerson[]) || [];
}

export interface CreateCompetentPersonParams {
  contractor_id: string;
  name: string;
  phone_no: string;
  ic_no: string;
  cp_type: 'CP1' | 'CP2' | 'CP3';
}

/**
 * Create a new competent person
 */
export async function createCompetentPerson(
  params: CreateCompetentPersonParams,
): Promise<CompetentPerson> {
  const { data, error } = await supabase
    .from('competent_person')
    .insert(params)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create competent person: ${error.message}`);
  }

  return data as unknown as CompetentPerson;
}
