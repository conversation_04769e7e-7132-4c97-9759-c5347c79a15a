import type { BaseFilters, BaseTableState } from '@/components/filter-section';
import type { BaseTableRow } from '@/components/table-section';
import type {
  MAINTENANCE_STATUS,
  OPERATION_LOG_TYPES,
} from '../schemas/create-maintenance-log';

export interface MaintenanceLog extends BaseTableRow {
  id: string;
  log_date: string;
  operation_log_type: (typeof OPERATION_LOG_TYPES)[number];
  contractor_id: string;
  contractor_name: string;
  person_in_charge_name: string;
  person_in_charge_phone: string;
  pma_id: string | null;
  pma_number: string | null;
  description: string;
  created_at: string;
  created_by: string;
  project_id: string;
  status: (typeof MAINTENANCE_STATUS)[number];
  actions?: React.ReactNode;
}

export interface MaintenanceLogsTableColumns {
  key: string;
  label: string;
  className?: string;
}

export interface MaintenanceTableState extends BaseTableState {
  pageIndex: number;
  pageSize: number;
  sorting?: {
    column: string;
    direction: 'asc' | 'desc';
  };
  filters: MaintenanceLogsFilters;
  visibleColumns?: Array<keyof MaintenanceLog>;
  columnVisibility: Record<string, boolean>;
}

export interface MaintenanceLogsFilters extends BaseFilters {
  contractors?: string[];
  contractorId?: string;
  pmaId?: string;
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  operationType?: (typeof OPERATION_LOG_TYPES)[number];
  status?: (typeof MAINTENANCE_STATUS)[number];
  search?: string;
}

export type MaintenanceLogsType = MaintenanceLog & {
  actions?: React.ReactNode;
};

export interface MaintenanceLogsTableProps {
  data: MaintenanceLogsType[];
  isLoading?: boolean;
  tableState: MaintenanceTableState;
  onTableStateChange?: (state: MaintenanceTableState) => void;
  totalItems?: number;
  columns?: MaintenanceLogsTableColumns[];
}

// "person_in_charge_name" column removed
export const ALL_COLUMNS: MaintenanceLogsTableColumns[] = [
  {
    key: 'log_date',
    label: 'Date',
    className: 'w-32',
  },
  {
    key: 'operation_log_type',
    label: 'Type',
    className: 'w-40',
  },
  {
    key: 'status',
    label: 'Status',
    className: 'w-36',
  },
  {
    key: 'pma_number',
    label: 'PMA Number',
    className: 'w-40',
  },
  {
    key: 'description',
    label: 'Description',
    className: 'min-w-[300px]',
  },
  {
    key: 'created_by',
    label: 'Created By',
    className: 'min-w-[150px]',
  },
  {
    key: 'contractor_name',
    label: 'Contractor',
    className: 'min-w-[180px]',
  },
  {
    key: 'actions',
    label: 'Actions',
    className: 'w-[100px] text-right',
  },
];

// Quick View Presets
export const QUICK_VIEW_COLUMNS: MaintenanceLogsTableColumns[] = [
  {
    key: 'log_date',
    label: 'Date',
    className: 'w-32',
  },
  {
    key: 'operation_log_type',
    label: 'Type',
    className: 'w-40',
  },
  {
    key: 'status',
    label: 'Status',
    className: 'w-36',
  },
  {
    key: 'actions',
    label: 'Actions',
    className: 'w-[100px] text-right',
  },
];

export const STANDARD_VIEW_COLUMNS: MaintenanceLogsTableColumns[] = [
  {
    key: 'log_date',
    label: 'Date',
    className: 'w-32',
  },
  {
    key: 'operation_log_type',
    label: 'Type',
    className: 'w-40',
  },
  {
    key: 'status',
    label: 'Status',
    className: 'w-36',
  },
  {
    key: 'pma_number',
    label: 'PMA Number',
    className: 'w-40',
  },
  {
    key: 'description',
    label: 'Description',
    className: 'min-w-[300px]',
  },
  {
    key: 'contractor_name',
    label: 'Contractor',
    className: 'min-w-[180px]',
  },
  {
    key: 'actions',
    label: 'Actions',
    className: 'w-[100px] text-right',
  },
];

export const DETAILED_VIEW_COLUMNS: MaintenanceLogsTableColumns[] = ALL_COLUMNS;

export const DEFAULT_COLUMNS: MaintenanceLogsTableColumns[] = ALL_COLUMNS;

const INITIAL_COLUMN_VISIBILITY: Record<string, boolean> = ALL_COLUMNS.reduce(
  (acc, col) => ({ ...acc, [col.key]: true }),
  {},
);

export const DEFAULT_TABLE_STATE: MaintenanceTableState = {
  pageIndex: 0,
  pageSize: 10,
  sorting: {
    column: 'created_at',
    direction: 'desc',
  },
  filters: {
    contractors: [],
    operationType: undefined,
    status: undefined,
    search: '',
    dateRange: undefined,
    contractorId: undefined,
    pmaId: undefined,
  },
  columnVisibility: INITIAL_COLUMN_VISIBILITY,
  visibleColumns: ALL_COLUMNS.filter(
    (col) => INITIAL_COLUMN_VISIBILITY[col.key],
  ).map((col) => col.key),
};

export const DEFAULT_VISIBLE_COLUMNS = DEFAULT_COLUMNS.map((col) => col.key);
