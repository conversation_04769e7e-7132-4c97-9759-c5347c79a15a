import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Filter, Search, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useEffect, useState } from 'react';

// Generic filter interface for basic filters with search
export interface BaseFilters {
  search?: string;
  [key: string]: unknown;
}

// Generic table state interface
export interface BaseTableState {
  columnVisibility?: Record<string, boolean>;
  sorting?: {
    column: string;
    direction: 'asc' | 'desc';
  };
  [key: string]: unknown;
}

// Generic column interface
export interface BaseColumn {
  key: string;
  label: string;
  className?: string;
}

/**
 * Generic, reusable filter section for table/list pages, styled exactly like MinimalistFilters.
 *
 * Props:
 * - filters: The current filter state object.
 * - onFilterChange: Callback to update filters.
 * - tableState: (optional) Table state object (for column visibility, etc.).
 * - onTableStateChange: (optional) Callback to update table state.
 * - columns, columnVisibility, onColumnVisibilityChange: (optional) For column toggling.
 * - renderFilters: (optional) A render prop for custom filter fields inside the filter popover.
 * - searchPlaceholder: (optional) Placeholder for the search input.
 * - getActiveFilterBadges: (optional) Function to render active filter badges below the filters.
 * - clearAllFilters: (optional) Function to clear all filters. If not provided, resets all keys to undefined or ''.
 * - columnVisibilityToggle: (optional) React node for column visibility toggle, rendered beside the filter popover.
 */
interface FilterSectionProps<
  F extends BaseFilters = BaseFilters,
  T extends BaseTableState = BaseTableState,
> {
  filters: F;
  onFilterChange: (filters: F) => void;
  tableState?: T;
  onTableStateChange?: (state: T) => void;
  columns?: BaseColumn[];
  columnVisibility?: Record<string, boolean>;
  onColumnVisibilityChange?: (
    columnVisibility: Record<string, boolean>,
  ) => void;
  renderFilters?: React.ReactNode;
  searchPlaceholder?: string;
  getActiveFilterBadges?: (
    filters: F,
    onFilterChange: (filters: F) => void,
  ) => React.ReactNode;
  clearAllFilters?: () => void;
  columnVisibilityToggle?: React.ReactNode;
}

export function FilterSection<
  F extends BaseFilters = BaseFilters,
  T extends BaseTableState = BaseTableState,
>({
  filters,
  onFilterChange,
  tableState: _tableState,
  onTableStateChange: _onTableStateChange,
  columns: _columns,
  columnVisibility: _columnVisibility,
  onColumnVisibilityChange: _onColumnVisibilityChange,
  renderFilters,
  searchPlaceholder,
  getActiveFilterBadges,
  clearAllFilters,
  columnVisibilityToggle,
}: FilterSectionProps<F, T>) {
  const t = useTranslations('common');
  const defaultSearchPlaceholder = searchPlaceholder || t('search');
  const searchInit = filters.search || '';
  const [searchValue, setSearchValue] = useState(searchInit);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      onFilterChange({ ...filters, search: searchValue } as F);
    }, 300);
    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue]);

  // Count active filters (excluding search)
  const activeFiltersCount = Object.entries(
    filters as Record<string, unknown>,
  ).filter(([key, value]) => {
    if (key === 'search')
      return value && typeof value === 'string' && value.length > 0;
    if (typeof value === 'object' && value !== null) {
      // For objects (e.g., dateRange), check if any value is truthy
      return Object.values(value as Record<string, unknown>).some((v) => v);
    }
    return value && value !== 'all';
  }).length;

  // Default clear all: set all keys to undefined or ''
  const handleClearFilters = () => {
    setSearchValue('');
    if (clearAllFilters) {
      clearAllFilters();
    } else {
      const cleared: Partial<F> = { ...filters };
      Object.keys(cleared).forEach((key) => {
        if (key === 'search') cleared[key as keyof F] = '' as F[keyof F];
        else if (
          typeof cleared[key as keyof F] === 'object' &&
          cleared[key as keyof F] !== null
        )
          cleared[key as keyof F] = {} as F[keyof F];
        else cleared[key as keyof F] = undefined;
      });
      onFilterChange(cleared as F);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
      {/* Search Filter */}
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
        <Input
          placeholder={defaultSearchPlaceholder}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className="pl-9 pr-10 border-gray-300 focus:border-gray-400 focus:ring-1 focus:ring-gray-400 rounded-xl h-11 bg-white/50 backdrop-blur-sm"
        />
        {searchValue && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchValue('')}
            className="absolute right-2 top-1/2 h-7 w-7 -translate-y-1/2 p-0 hover:bg-gray-100 rounded-lg"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Combined Filters */}
      <div className="flex items-center gap-3">
        {/* All Filters Combo */}
        <Popover open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="h-11 px-4 border-gray-300 hover:border-gray-400 rounded-xl bg-white/50 backdrop-blur-sm"
            >
              <Filter className="h-4 w-4 mr-2" />
              {t('filters')}
              {activeFiltersCount > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-2 h-5 px-2 text-xs bg-blue-100 text-blue-700 border-blue-200"
                >
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-6" align="end">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold text-base text-gray-900">
                  {t('filterOptions')}
                </h4>
                {activeFiltersCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearFilters}
                    className="h-auto p-0 text-sm text-gray-500 hover:text-gray-700"
                  >
                    {t('clearAll')}
                  </Button>
                )}
              </div>
              {/* Custom filter fields go here */}
              {renderFilters}
            </div>
          </PopoverContent>
        </Popover>

        {/* Column Visibility Toggle (optional, rendered beside popover) */}
        {columnVisibilityToggle}
      </div>

      {/* Active Filters Display (badges) */}
      {getActiveFilterBadges && (
        <div className="flex items-center gap-2 flex-wrap mt-4 sm:mt-0">
          {getActiveFilterBadges(filters, onFilterChange)}
        </div>
      )}
    </div>
  );
}
