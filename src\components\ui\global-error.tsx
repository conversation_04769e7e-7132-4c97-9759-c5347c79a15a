import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import type { ReactNode } from 'react';

interface GlobalErrorProps {
  title?: string;
  message: string;
  icon?: ReactNode;
  retry?: () => void;
}

export function GlobalError({
  title = 'Error',
  message,
  icon,
  retry,
}: GlobalErrorProps) {
  return (
    <Alert
      variant="destructive"
      className="flex flex-col items-center text-center max-w-md mx-auto"
    >
      <div className="flex items-center justify-center mb-2">
        {icon ?? <AlertCircle className="h-6 w-6 text-red-500" />}
      </div>
      <AlertTitle className="font-bold">{title}</AlertTitle>
      <AlertDescription className="mb-2">{message}</AlertDescription>
      {retry && (
        <Button variant="outline" onClick={retry}>
          Retry
        </Button>
      )}
    </Alert>
  );
}

export default GlobalError;
