import type { UserRole } from '@/types/auth';
import type { ProjectRole } from '../types/invitation';

/**
 * Maps project roles to user roles with 1:1 mapping:
 * - technician (project) → contractor (user)
 * - competent_person (project) → contractor (user)
 * - admin (project) → admin (user)
 * - viewer (project) → viewer (user)
 *
 * Note: When creating users with 'contractor' role from project invitations,
 * the contractor_id should be set to match the project's contractor_id.
 */
export function mapProjectRoleToUserRole(projectRole: ProjectRole): UserRole {
  switch (projectRole) {
    case 'technician':
    case 'competent_person':
      return 'contractor';
    case 'admin':
      return 'admin';
    case 'viewer':
      return 'viewer';
    default:
      // Fallback to viewer for any unexpected role
      return 'viewer';
  }
}
