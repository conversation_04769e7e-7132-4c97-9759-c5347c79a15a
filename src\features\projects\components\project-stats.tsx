'use client';

import { Card, CardContent } from '@/components/ui/card';
import { FolderOpen } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ProjectStats } from '../types/project';

interface ProjectStatsCardsProps {
  stats: ProjectStats;
  isLoading?: boolean;
}

/**
 * Display project statistics in card format
 */
export const ProjectStatsCards: React.FC<ProjectStatsCardsProps> = ({
  stats,
  isLoading = false,
}) => {
  const t = useTranslations('pages.projects.stats');

  if (isLoading) {
    return (
      <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="border-0 shadow-sm">
            <CardContent className="p-3 sm:p-4">
              <div className="animate-pulse">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-12"></div>
                  </div>
                  <div className="h-9 w-9 bg-gray-200 rounded-lg"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
      {/* Total Projects */}
      <Card className="border-0 shadow-sm">
        <CardContent className="p-3 sm:p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm font-medium text-gray-500">
                {t('total')}
              </p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900">
                {stats.total}
              </p>
            </div>
            <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg">
              <FolderOpen className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
