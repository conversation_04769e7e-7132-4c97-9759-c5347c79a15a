import { NextRequest, NextResponse } from 'next/server';

/**
 * PDF Preview Proxy API Route
 * This route acts as a proxy to serve PDF files with proper headers for inline viewing.
 * It helps bypass CORS and Content-Disposition issues that prevent PDF preview in browsers.
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');

    if (!url) {
      return NextResponse.json(
        { error: 'URL parameter is required' },
        { status: 400 },
      );
    }

    // Validate that the URL is from our allowed domains (security check)
    const allowedDomains = [
      process.env.TMONE_OBS_STORAGE,
      'supabase.co',
      'supabase.in',
      'amazonaws.com',
      'storage.googleapis.com',
      'localhost',
      '127.0.0.1',
      'simple-obs.obs.my-kualalumpur-1.alphaedge.tmone.com.my',
    ].filter(Boolean);

    const urlObj = new URL(url);
    const isAllowedDomain = allowedDomains.some(
      (domain) =>
        urlObj.hostname.includes(domain as string) ||
        urlObj.hostname === domain,
    );

    if (!isAllowedDomain) {
      console.error(
        'Domain not allowed:',
        urlObj.hostname,
        'Allowed:',
        allowedDomains,
      );
      return NextResponse.json(
        { error: 'URL not from allowed domain', domain: urlObj.hostname },
        { status: 403 },
      );
    }

    // Fetch the PDF file
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; PDF-Viewer/1.0)',
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch PDF: ${response.status} ${response.statusText}`,
      );
    }

    const contentType =
      response.headers.get('content-type') || 'application/pdf';
    const contentLength = response.headers.get('content-length');

    // Create response with proper headers for PDF viewing
    const pdfResponse = new NextResponse(response.body, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': 'inline; filename="certificate.pdf"',
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=3600, must-revalidate',
        'X-Content-Type-Options': 'nosniff',
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        ...(contentLength && { 'Content-Length': contentLength }),
      },
    });

    return pdfResponse;
  } catch (error) {
    console.error('PDF preview error:', error);
    return NextResponse.json(
      {
        error: 'Failed to load PDF',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}
