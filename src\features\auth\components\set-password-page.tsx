'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/lib/supabase';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface SetPasswordPageProps {
  /** The Supabase auth token from the invitation */
  token?: string;
  /** The project token to redirect back to */
  projectToken?: string;
}

/**
 * Component for setting password after accepting project invitation
 * Used on the /auth/set-password page
 */
export function SetPasswordPage({ token, projectToken }: SetPasswordPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get tokens from props or URL params
  const authToken = token || searchParams?.get('token');
  const projectTokenParam = projectToken || searchParams?.get('project');

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [sessionEstablished, setSessionEstablished] = useState(false);

  const [errors, setErrors] = useState<{
    password?: string;
    confirmPassword?: string;
    general?: string;
  }>({});

  // Initialize session from invitation token
  useEffect(() => {
    const initializeSession = async () => {
      setIsInitializing(false);

      try {
        // Check if we already have a session
        const { data: sessionData } = await supabase.auth.getSession();
        console.log('Current session:', sessionData);

        if (sessionData.session) {
          setSessionEstablished(true);
          console.log('Existing session found');
          return;
        }

        // Check for auth fragments in URL hash (from Supabase redirect)
        const urlHash = window.location.hash;
        if (urlHash.includes('access_token')) {
          console.log('Found access token in URL hash');
          const hashParams = new URLSearchParams(urlHash.substring(1));
          const accessToken = hashParams.get('access_token');
          const refreshToken = hashParams.get('refresh_token');

          if (accessToken && refreshToken) {
            console.log('Setting session with tokens from hash');
            const { data, error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            if (error) {
              console.error('Error setting session from hash:', error);
              setErrors({ general: `Session error: ${error.message}` });
            } else if (data.session) {
              setSessionEstablished(true);
              console.log('Session established from hash');
              // Clean up the URL hash
              window.history.replaceState(
                {},
                document.title,
                window.location.pathname + window.location.search,
              );
            }
          }
        } else {
          console.log(
            'No access token in URL hash, checking for other auth methods',
          );
          setErrors({
            general:
              'No authentication session found. Please click the invitation link from your email.',
          });
        }
      } catch (error) {
        console.error('Error initializing session:', error);
        setErrors({
          general: `Initialization error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    };

    initializeSession();
  }, [authToken, projectTokenParam]);

  const validatePassword = (pwd: string): string | undefined => {
    if (pwd.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    if (!/(?=.*[a-z])/.test(pwd)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!/(?=.*[A-Z])/.test(pwd)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!/(?=.*\d)/.test(pwd)) {
      return 'Password must contain at least one number';
    }
    if (!/(?=.*[@$!%*?&])/.test(pwd)) {
      return 'Password must contain at least one special character (@$!%*?&)';
    }
    return undefined;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    // Validate inputs
    const passwordError = validatePassword(password);
    let hasErrors = false;
    const newErrors: typeof errors = {};

    if (passwordError) {
      newErrors.password = passwordError;
      hasErrors = true;
    }

    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      hasErrors = true;
    }

    if (!sessionEstablished) {
      newErrors.general =
        'Authentication session not established. Please check the debug information above.';
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      setIsLoading(false);
      return;
    }

    try {
      // Update user password using Supabase
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) {
        console.error('Error setting password:', error);
        setErrors({ general: error.message });
        setIsLoading(false);
        return;
      }

      // Success - redirect to project or dashboard
      toast.success('Password set successfully! Welcome to SimPLE.');

      if (projectTokenParam) {
        // Redirect back to project invitation page
        router.push(`/invite?token=${projectTokenParam}`);
      } else {
        // Redirect to dashboard
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      setErrors({
        general:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isInitializing) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
            <p className="text-lg font-medium">Initializing session...</p>
            <p className="text-sm text-gray-600">
              Please wait while we set up your account
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!projectTokenParam && !authToken) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-red-600">Invalid Link</CardTitle>
            <CardDescription>
              This invitation link is invalid or has expired. Please contact the
              person who invited you for a new invitation.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Show error state if session couldn't be established
  if (!sessionEstablished && errors.general) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-red-600">Session Error</CardTitle>
            <CardDescription>{errors.general}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="mt-4"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="text-2xl font-bold text-blue-600 mb-2">SimPLE</div>
          <CardTitle>Set Your Password</CardTitle>
          <CardDescription>
            Create a secure password to complete your account setup
          </CardDescription>
        </CardHeader>
        <CardContent>
          {sessionEstablished && (
            <div className="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md mb-4">
              ✅ Authentication verified. You can now set your password.
            </div>
          )}

          {!sessionEstablished && !errors.general && (
            <div className="p-3 text-sm text-yellow-600 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
              ⏳ Waiting for authentication session...
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {errors.general && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {errors.general}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={errors.password ? 'border-red-500' : ''}
                  disabled={isLoading}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className={errors.confirmPassword ? 'border-red-500' : ''}
                  disabled={isLoading}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <p className="text-sm text-blue-800 font-medium mb-1">
                Password Requirements:
              </p>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• At least 8 characters long</li>
                <li>• Contains uppercase and lowercase letters</li>
                <li>• Contains at least one number</li>
                <li>• Contains at least one special character (@$!%*?&)</li>
              </ul>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || !sessionEstablished}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isLoading ? 'Setting Password...' : 'Set Password & Continue'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
