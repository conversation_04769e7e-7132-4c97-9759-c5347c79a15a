import { BulkPaymentService } from '@/features/billing/services/bulk-payment.service';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import crypto from 'crypto';

// Type declaration for global card callback data storage
declare global {
  var cardCallbackData:
    | Map<
        string,
        {
          card_id: string;
          token: string;
          last_four: string;
          brand: string;
          exp_month: number;
          exp_year: number;
          fingerprint: string;
          timestamp: number;
        }
      >
    | undefined;
}

// Validation schema for BillPlz card callback
const CardCallbackSchema = z.object({
  card_id: z.string(),
  token: z.string(),
  last_four: z.string(),
  brand: z.string(),
  exp_month: z.number(),
  exp_year: z.number(),
  fingerprint: z.string(),
  country: z.string().optional(),
  funding: z.string().optional(),
  checksum: z.string(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Received card callback:', JSON.stringify(body, null, 2));

    // Extract contractor_id and save_card from URL parameters
    const { searchParams } = new URL(request.url);
    const contractorId = searchParams.get('contractor_id');
    const saveCard = searchParams.get('save_card') === 'true';

    // Validate callback data
    const validatedData = CardCallbackSchema.parse(body);

    // Verify checksum (implement according to BillPlz documentation)
    const isValidChecksum = await verifyBillPlzChecksum(validatedData);
    if (!isValidChecksum) {
      console.error('Invalid checksum in card callback');
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid checksum',
        },
        { status: 400 },
      );
    }

    // Save card to contractor_payment_methods table if contractor_id is provided and save_card is true
    if (contractorId && saveCard) {
      try {
        const saveResult = await BulkPaymentService.saveCardForContractor({
          contractor_id: contractorId,
          token: validatedData.token,
          fingerprint: validatedData.fingerprint,
          last_four: validatedData.last_four,
          brand: validatedData.brand,
          exp_month: validatedData.exp_month,
          exp_year: validatedData.exp_year,
          is_default: false, // Let user set default manually
        });

        if (saveResult.success) {
          console.log('Card saved successfully for contractor:', contractorId);
        } else {
          console.error(
            'Failed to save card for contractor:',
            saveResult.error,
          );
        }
      } catch (saveError) {
        console.error('Error saving card for contractor:', saveError);
      }
    } else {
      console.log('Card callback received but not saving:', {
        contractorId,
        saveCard,
      });
    }

    // Store card details temporarily for frontend to retrieve
    // In production, use a more robust storage solution
    const cardData = {
      card_id: validatedData.card_id,
      token: validatedData.token,
      last_four: validatedData.last_four,
      brand: validatedData.brand,
      exp_month: validatedData.exp_month,
      exp_year: validatedData.exp_year,
      fingerprint: validatedData.fingerprint,
      timestamp: Date.now(),
    };

    // Store for retrieval by frontend (implement proper storage in production)
    global.cardCallbackData = global.cardCallbackData || new Map();
    global.cardCallbackData.set(validatedData.card_id, cardData);

    return NextResponse.json({
      success: true,
      message: 'Card details received and processed successfully',
    });
  } catch (error) {
    console.error('Card callback API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid callback data format',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}

/**
 * Verify BillPlz checksum according to their documentation
 * Implementation depends on BillPlz's specific checksum algorithm
 */
async function verifyBillPlzChecksum(
  data: z.infer<typeof CardCallbackSchema>,
): Promise<boolean> {
  try {
    // Get X-Signature key from environment
    const xSignatureKey = process.env.BILLPLZ_X_SIGNATURE_KEY;
    if (!xSignatureKey) {
      console.error('BILLPLZ_X_SIGNATURE_KEY not configured');
      return false;
    }

    // Create checksum string according to BillPlz documentation
    // This is a placeholder - implement according to actual BillPlz docs
    const checksumString = [
      data.card_id,
      data.token,
      data.last_four,
      data.brand,
      data.exp_month.toString(),
      data.exp_year.toString(),
      data.fingerprint,
    ].join('|');

    const expectedChecksum = crypto
      .createHash('sha256')
      .update(checksumString + xSignatureKey)
      .digest('hex');

    return data.checksum === expectedChecksum;
  } catch (error) {
    console.error('Error verifying checksum:', error);
    return false;
  }
}
