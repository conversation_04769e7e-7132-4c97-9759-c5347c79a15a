import { createServerSupabaseClient } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { invitationId } = await request.json();

    if (!invitationId) {
      return NextResponse.json(
        { error: 'Missing required field: invitationId' },
        { status: 400 },
      );
    }

    // Create server Supabase client with access to cookies
    const supabase = await createServerSupabaseClient();

    // Get current user from session
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();

    if (sessionError || !sessionData.session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized: No valid session' },
        { status: 401 },
      );
    }

    const currentUserId = sessionData.session.user.id;

    // Get the invitation details and verify user can decline it
    const { data: invitation, error: invitationError } = await supabase
      .from('project_users')
      .select('*')
      .eq('id', invitationId)
      .eq('user_id', currentUserId)
      .eq('status', 'invited')
      .eq('is_active', true)
      .single();

    if (invitationError || !invitation) {
      return NextResponse.json(
        {
          error: 'Invitation not found or you are not authorized to decline it',
        },
        { status: 404 },
      );
    }

    // Update the invitation status to 'declined'
    const { data: updatedInvitation, error: updateError } = await supabase
      .from('project_users')
      .update({
        status: 'declined',
        updated_at: new Date().toISOString(),
        updated_by: currentUserId,
      })
      .eq('id', invitationId)
      .select()
      .single();

    if (updateError) {
      console.error('Error declining invitation:', updateError);
      return NextResponse.json(
        { error: 'Failed to decline invitation' },
        { status: 500 },
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Invitation declined successfully',
        invitation: updatedInvitation,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Decline invitation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
