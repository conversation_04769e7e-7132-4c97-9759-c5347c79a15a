-- Migration: Allow all admins to be project PIC (project admin)
-- Date: 2025-06-20
-- Description: Update constraints and triggers so any user with user_role = 'admin' can be assigned as project admin, regardless of admin_access_mode.

-- ================================
-- UPDATE CHECK CONSTRAINT
-- ================================
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_admin_mode_validity;

ALTER TABLE users ADD CONSTRAINT chk_admin_mode_validity 
CHECK (
  (user_role = 'admin') 
  OR 
  (user_role = 'contractor' AND (admin_access_mode IS NULL OR admin_access_mode = 'project'))
  OR 
  (user_role NOT IN ('admin', 'contractor') AND admin_access_mode IS NULL)
);

-- ================================
-- UPDATE TRIGGER FUNCTION
-- ================================
CREATE OR REPLACE FUNCTION enforce_project_admin_mode() RETURNS trigger AS $$
BEGIN
  -- Allow project admin assignment if user is admin (any admin_access_mode) 
  -- OR if user is a contractor (for project creation purposes)
  IF NEW.role = 'admin' THEN
    IF NOT EXISTS (
      SELECT 1 FROM users 
      WHERE id = NEW.user_id 
      AND (user_role = 'admin' OR user_role = 'contractor')
    ) THEN
      RAISE EXCEPTION 'User % is not authorized to be a project admin', NEW.user_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- No changes needed for helper functions.
