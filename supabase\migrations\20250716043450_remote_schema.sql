drop index if exists "public"."idx_complaints_verified_by";

drop index if exists "public"."idx_complaints_verified_date";

-- First, update any 'partially function' values to 'broken' since we're removing that option
UPDATE "public"."maintenance_logs" 
SET status = 'broken' 
WHERE status = 'partially function';

-- Remove the default temporarily
alter table "public"."maintenance_logs" alter column "status" drop default;

-- Rename the old enum type
alter type "public"."maintenance_status" rename to "maintenance_status__old_version_to_be_dropped";

-- Create the new enum type with only two values
create type "public"."maintenance_status" as enum ('fully function', 'broken');

-- Convert the column to the new type
alter table "public"."maintenance_logs" alter column status type "public"."maintenance_status" using status::text::"public"."maintenance_status";

-- Drop the old enum type
drop type "public"."maintenance_status__old_version_to_be_dropped";

-- Drop the verification columns from complaints
alter table "public"."complaints" drop column "verified_by";

alter table "public"."complaints" drop column "verified_date";


