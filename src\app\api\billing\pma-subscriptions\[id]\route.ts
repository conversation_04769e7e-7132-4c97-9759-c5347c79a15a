import { authenticateWithPermission } from '@/features/auth';
import { pmaSubscriptionsService } from '@/features/billing/services/pma-subscriptions.service';
import { NextRequest, NextResponse } from 'next/server';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/billing/pma-subscriptions/[id]
 * Get PMA subscription details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user and check permissions for viewing projects
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id } = await params;

    // Get subscription
    const result = await pmaSubscriptionsService.getById(id);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    if (!result.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    // Role-based access control: contractors can only see their own subscriptions
    if (
      user.user_role === 'contractor' &&
      result.data.contractors?.id !== user.id
    ) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json({ subscription: result.data });
  } catch (error) {
    console.error('GET /api/billing/pma-subscriptions/[id] error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * PATCH /api/billing/pma-subscriptions/[id]
 * Update PMA subscription
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user and check permissions for updating projects
    const { user, error } = await authenticateWithPermission('projects.edit');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id } = await params;

    // Get existing subscription to check ownership
    const existingResult = await pmaSubscriptionsService.getById(id);

    if (existingResult.error) {
      return NextResponse.json(
        { error: existingResult.error },
        { status: 500 },
      );
    }

    if (!existingResult.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    // Role-based access control: contractors can only update their own subscriptions
    if (
      user.user_role === 'contractor' &&
      existingResult.data.contractors?.id !== user.id
    ) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { action, ...updates } = body;

    let result;

    // Handle special actions
    if (action) {
      switch (action) {
        case 'activate':
          result = await pmaSubscriptionsService.activateSubscription(id);
          break;
        case 'suspend':
          result = await pmaSubscriptionsService.suspendSubscription(
            id,
            updates.reason,
          );
          break;
        case 'cancel':
          result = await pmaSubscriptionsService.cancelSubscription(id);
          break;
        case 'reactivate':
          result = await pmaSubscriptionsService.reactivateSubscription(id);
          break;
        case 'grace_period':
          result = await pmaSubscriptionsService.setGracePeriod(
            id,
            updates.gracePeriodDays,
          );
          break;
        default:
          return NextResponse.json(
            { error: `Unknown action: ${action}` },
            { status: 400 },
          );
      }
    } else {
      // Regular update
      result = await pmaSubscriptionsService.update(id, updates);
    }

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({ subscription: result.data });
  } catch (error) {
    console.error('PATCH /api/billing/pma-subscriptions/[id] error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/billing/pma-subscriptions/[id]
 * Cancel PMA subscription (soft delete)
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user - for DELETE, contractors can cancel their own subscriptions
    const { user, error } = await authenticateWithPermission('projects.view');

    if (error || !user) {
      return NextResponse.json(
        { error: error || 'Unauthorized' },
        { status: error === 'Insufficient permissions' ? 403 : 401 },
      );
    }

    const { id } = await params;

    // Get existing subscription to check ownership
    const existingResult = await pmaSubscriptionsService.getById(id);

    if (existingResult.error) {
      return NextResponse.json(
        { error: existingResult.error },
        { status: 500 },
      );
    }

    if (!existingResult.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    // Role-based access control: contractors can only cancel their own subscriptions
    if (user.user_role === 'contractor') {
      if (existingResult.data.contractors?.id !== user.id) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      // Contractors can only cancel, not hard delete
      const result = await pmaSubscriptionsService.cancelSubscription(id);

      if (result.error) {
        return NextResponse.json({ error: result.error }, { status: 400 });
      }

      return NextResponse.json({
        message: 'Subscription cancelled successfully',
        subscription: result.data,
      });
    }

    // Admin operations - cancel for all users (no hard delete for now)
    const result = await pmaSubscriptionsService.cancelSubscription(id);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({
      message: 'Subscription cancelled successfully',
      subscription: result.data,
    });
  } catch (error) {
    console.error('DELETE /api/billing/pma-subscriptions/[id] error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
