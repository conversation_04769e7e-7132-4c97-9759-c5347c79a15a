import type { PostgrestError } from '@supabase/supabase-js';

export type SortDirection = 'asc' | 'desc';

export interface QueryOptions {
  withCount?: boolean;
}

export interface PmaCertificate {
  pma_number: string | null;
}

export interface MaintenanceLogRow {
  id: string;
  log_date: string;
  operation_log_type: string;
  contractor_id: string | null;
  person_in_charge_name: string | null;
  person_in_charge_phone: string | null;
  description: string | null;
  created_at: string | null;
  project_id: string;
  pma_id: string | null;
  pma_certificates?: PmaCertificate | null;
}

export interface QueryResult<T = unknown> {
  data: T[] | null;
  error: PostgrestError | null;
  count: number | null;
  status: number;
  statusText: string;
}

// Type for Supabase query builder
export type SupabaseQueryBuilder = {
  select: (
    query: string,
    options?: { count?: 'exact' | 'planned' },
  ) => SupabaseQueryBuilder;
  eq: (
    column: string,
    value: string | number | boolean,
  ) => SupabaseQueryBuilder;
  or: (filters: string) => SupabaseQueryBuilder;
  gte: (column: string, value: string | number) => SupabaseQueryBuilder;
  lte: (column: string, value: string | number) => SupabaseQueryBuilder;
  order: (
    column: string,
    options: { ascending: boolean },
  ) => SupabaseQueryBuilder;
  range: (start: number, end: number) => SupabaseQueryBuilder;
  then: <T = unknown>() => Promise<QueryResult<T>>;
};

// Strong typed function return types
export type QueryBuilderResult<T> = Promise<QueryResult<T>>;
