'use client';

import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { ChevronDown } from 'lucide-react';
// Light mode only
import { useCallback, useState } from 'react';
import {
  Bar,
  Bar<PERSON>hart as RechartsBar<PERSON>hart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const barChartVariants = cva('', {
  variants: {
    variant: {
      default: '',
      outline: 'border rounded-lg p-4',
    },
    size: {
      default: 'h-[300px]',
      sm: 'h-[200px]',
      lg: 'h-[400px]',
      xl: 'h-[500px]',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

export interface BarChartProps {
  data: Array<Record<string, unknown>>;
  categories: Array<{
    name: string;
    color?: string;
    dataKey: string;
  }>;
  xAxisDataKey: string;
  yAxisLabel?: string;
  title?: string;
  className?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  variant?: 'default' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'xl';
  tooltipFormatter?: (value: unknown) => string;
  onBarClick?: (data: unknown, index: number) => void;
}

export function BarChart({
  data,
  categories,
  xAxisDataKey,
  yAxisLabel,
  title,
  className,
  showLegend = true,
  showGrid = true,
  showTooltip = true,
  variant = 'default',
  size = 'default',
  tooltipFormatter,
  onBarClick,
}: BarChartProps) {
  // Always use light mode colors
  const textColor = '#424242';
  const gridColor = '#e0e0e0';

  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const handleBarClick = useCallback(
    (data: unknown, index: number) => {
      setActiveIndex(index === activeIndex ? null : index);
      if (onBarClick) {
        onBarClick(data, index);
      }
    },
    [activeIndex, onBarClick],
  );

  const renderContent = () => (
    <ResponsiveContainer width="100%" height="100%">
      <RechartsBarChart
        data={data}
        margin={{ top: 10, right: 30, left: 20, bottom: 40 }}
      >
        {showGrid && (
          <CartesianGrid
            strokeDasharray="3 3"
            vertical={false}
            stroke={gridColor}
          />
        )}
        <XAxis
          dataKey={xAxisDataKey}
          stroke={textColor}
          tick={{ fill: textColor, fontSize: 12 }}
          tickLine={{ stroke: textColor }}
          axisLine={{ stroke: textColor }}
        />
        <YAxis
          stroke={textColor}
          tick={{ fill: textColor, fontSize: 12 }}
          tickLine={{ stroke: textColor }}
          axisLine={{ stroke: textColor }}
          label={
            yAxisLabel
              ? {
                  value: yAxisLabel,
                  angle: -90,
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: textColor },
                }
              : undefined
          }
        />
        {showTooltip && (
          <Tooltip
            formatter={tooltipFormatter}
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e0e0e0',
              borderRadius: '6px',
              color: textColor,
            }}
          />
        )}
        {showLegend && (
          <Legend
            verticalAlign="top"
            height={36}
            wrapperStyle={{ fontSize: '12px', color: textColor }}
          />
        )}
        {categories.map((category, index) => (
          <Bar
            key={`bar-${index}`}
            dataKey={category.dataKey}
            name={category.name}
            fill={category.color || `hsl(${index * 40}, 70%, 60%)`}
            radius={[4, 4, 0, 0]}
            onClick={handleBarClick}
            className="cursor-pointer hover:opacity-80 transition-opacity"
            isAnimationActive={true}
            animationDuration={800}
          />
        ))}
      </RechartsBarChart>
    </ResponsiveContainer>
  );

  if (variant === 'outline' || title) {
    return (
      <Card className={cn('overflow-hidden', className)}>
        {title && (
          <div className="flex justify-between items-center px-4 py-3 border-b">
            <div className="font-medium">{title}</div>
            <button className="rounded-full p-1 hover:bg-secondary">
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        )}
        <div className={barChartVariants({ variant: 'default', size })}>
          {renderContent()}
        </div>
      </Card>
    );
  }

  return (
    <div className={cn(barChartVariants({ variant, size }), className)}>
      {renderContent()}
    </div>
  );
}
