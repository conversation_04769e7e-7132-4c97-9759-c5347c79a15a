-- ================================
-- FIX INFINITE RECURSION IN PROJECT_USERS RLS POLICIES
-- ================================

-- Drop the problematic policies that cause infinite recursion
DROP POLICY IF EXISTS "Authorized users can create project memberships" ON project_users;
DROP POLICY IF EXISTS "Authorized users can update project memberships" ON project_users;

-- Create new policies without circular references
-- Allow contractors to create project memberships for their own projects
CREATE POLICY "Contractors can create project memberships"
ON project_users
FOR INSERT
TO public
WITH CHECK (
  project_id IN (
    SELECT p.id 
    FROM projects p 
    INNER JOIN users u ON u.contractor_id = p.contractor_id 
    WHERE u.id = auth.uid() AND u.user_role = 'contractor'
  )
);

-- Allow project admins to create project memberships (without self-reference)
-- This policy will only apply to existing admins identified through a separate check
CREATE POLICY "Project admins can create project memberships" 
ON project_users
FOR INSERT  
TO public
WITH CHECK (
  -- Check if the user is an admin of this project by looking at the projects table
  -- and checking if they're already an admin through their contractor relationship
  project_id IN (
    SELECT p.id 
    FROM projects p 
    INNER JOIN users u ON u.contractor_id = p.contractor_id 
    WHERE u.id = auth.uid() AND u.user_role = 'contractor'
  )
  OR
  -- Allow service accounts to bypass this check for invitation acceptance
  auth.role() = 'service_role'
);

-- Allow contractors to update project memberships for their own projects
CREATE POLICY "Contractors can update project memberships"
ON project_users  
FOR UPDATE
TO public
USING (
  project_id IN (
    SELECT p.id 
    FROM projects p 
    INNER JOIN users u ON u.contractor_id = p.contractor_id 
    WHERE u.id = auth.uid() AND u.user_role = 'contractor'
  )
);

-- Allow project admins to update memberships (simplified to avoid recursion)
CREATE POLICY "Project admins can update memberships through contractor"
ON project_users
FOR UPDATE  
TO public
USING (
  project_id IN (
    SELECT p.id 
    FROM projects p 
    INNER JOIN users u ON u.contractor_id = p.contractor_id 
    WHERE u.id = auth.uid() AND u.user_role = 'contractor'
  )
);

-- ================================
-- UPDATE VIEW POLICY TO AVOID RECURSION
-- ================================

-- Drop and recreate the view policy without self-reference
DROP POLICY IF EXISTS "Users can view their project memberships" ON project_users;

CREATE POLICY "Users can view their project memberships"
ON project_users
FOR SELECT  
TO public
USING (
  -- Users can see their own memberships
  user_id = auth.uid()
  OR
  -- Contractors can see all memberships for their projects
  project_id IN (
    SELECT p.id 
    FROM projects p 
    INNER JOIN users u ON u.contractor_id = p.contractor_id 
    WHERE u.id = auth.uid() AND u.user_role = 'contractor'
  )
);

-- ================================
-- CREATE HELPER FUNCTION FOR ADMIN CHECK
-- ================================

-- Create a function to safely check if a user is an admin of a project
-- This will be used by the application layer for additional validation
CREATE OR REPLACE FUNCTION is_project_admin(project_uuid uuid, user_uuid uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is contractor owner of the project
  IF EXISTS (
    SELECT 1 
    FROM projects p 
    INNER JOIN users u ON u.contractor_id = p.contractor_id 
    WHERE p.id = project_uuid 
    AND u.id = user_uuid 
    AND u.user_role = 'contractor'
  ) THEN
    RETURN true;
  END IF;
  
  -- If not contractor owner, this would check project_users table
  -- but we'll handle this through application logic to avoid recursion
  RETURN false;
END;
$$;