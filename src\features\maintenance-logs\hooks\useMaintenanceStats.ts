import { useProjectContext } from '@/providers/project-context';
import { useQuery } from '@tanstack/react-query';
import { fetchMaintenanceStats } from '../services/maintenanceLogsService';
import type { MaintenanceLogsFilters } from '../types/table';

interface UseMaintenanceStatsParams {
  filters: MaintenanceLogsFilters;
}

export function useMaintenanceStats(params: UseMaintenanceStatsParams) {
  const { selectedProjectId } = useProjectContext();

  return useQuery({
    queryKey: ['maintenance-stats', selectedProjectId, params.filters],
    queryFn: () => {
      if (!selectedProjectId) {
        throw new Error('No project selected');
      }

      return fetchMaintenanceStats(selectedProjectId, params.filters);
    },
    enabled: !!selectedProjectId,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}
