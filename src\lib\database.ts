import { supabase } from '@/lib/supabase';
import type { Profile, UserRole } from '@/types/auth';
import type { Database } from '@/types/database';

// Type aliases for easier use
type Enums = Database['public']['Enums'];

// Helper function to get user role from profile
export async function getUserRole(userId: string): Promise<UserRole | null> {
  const { data, error } = await supabase
    .from('users')
    .select('user_role')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error fetching user role:', error);
    return null;
  }

  return data.user_role;
}

// Helper function to check if user has specific role
export async function hasRole(
  userId: string,
  role: UserRole,
): Promise<boolean> {
  const userRole = await getUserRole(userId);
  return userRole === role;
}

// Helper function to get profiles by multiple roles
export async function getProfilesByRoles(
  roles: UserRole[],
): Promise<Profile[]> {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .in('user_role', roles)
    .is('deleted_at', null)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching profiles by roles:', error);
    return [];
  }

  return data as Profile[];
}

// Helper function to update user's last activity
export async function updateLastLogin(userId: string): Promise<void> {
  try {
    // Since last_login field doesn't exist in your schema, just update updated_at
    const { error } = await supabase
      .from('users')
      .update({
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating user activity:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error updating user activity:', error);
    throw error;
  }
}

// Helper function to search profiles by name
export async function searchProfilesByName(
  searchTerm: string,
  role?: UserRole,
): Promise<Profile[]> {
  let query = supabase
    .from('users')
    .select('*')
    .ilike('name', `%${searchTerm}%`)
    .is('deleted_at', null);

  if (role) {
    query = query.eq('user_role', role);
  }

  const { data, error } = await query
    .order('name', { ascending: true })
    .limit(50);

  if (error) {
    console.error('Error searching profiles:', error);
    return [];
  }

  return data as Profile[];
}

// Helper function to get profile statistics by role
export async function getProfileStats(): Promise<Record<UserRole, number>> {
  const { data, error } = await supabase
    .from('users')
    .select('user_role')
    .is('deleted_at', null);

  if (error) {
    console.error('Error fetching profile stats:', error);
    return { admin: 0, contractor: 0, viewer: 0 };
  }

  const stats = data.reduce(
    (acc, profile) => {
      acc[profile.user_role] = (acc[profile.user_role] || 0) + 1;
      return acc;
    },
    {} as Record<UserRole, number>,
  );

  // Ensure all roles are present
  return {
    admin: stats.admin || 0,
    contractor: stats.contractor || 0,
    viewer: stats.viewer || 0,
  };
}

// Type-safe table access helpers
export const db = {
  users: () => supabase.from('users'),
  contractors: () => supabase.from('contractors'),
  // Add more table helpers as needed based on your actual schema
} as const;

// Enum helpers
export const enums: Partial<Enums> = {
  user_role: 'admin' as UserRole, // This is just for type inference
  company_type: 'COMPETENT_FIRM',
  complaint_status: 'open',
  pma_status: 'valid',
};

// Export all user roles for easy access
export const USER_ROLES: UserRole[] = ['admin', 'contractor', 'viewer'];
