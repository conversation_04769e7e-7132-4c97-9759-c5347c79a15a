import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Type declaration for global card callback data storage
declare global {
  var cardCallbackData:
    | Map<
        string,
        {
          card_id: string;
          token: string;
          last_four: string;
          brand: string;
          exp_month: number;
          exp_year: number;
          fingerprint: string;
          timestamp: number;
        }
      >
    | undefined;
}

// Validation schema for card result retrieval
const CardResultSchema = z.object({
  card_id: z.string(),
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const cardId = searchParams.get('card_id');

    if (!cardId) {
      return NextResponse.json(
        {
          success: false,
          error: 'card_id parameter is required',
        },
        { status: 400 },
      );
    }

    const validatedData = CardResultSchema.parse({ card_id: cardId });

    // Retrieve card data from temporary storage
    // In production, use Redis or a database
    const cardCallbackData = global.cardCallbackData || new Map();
    const cardData = cardCallbackData.get(validatedData.card_id);

    if (!cardData) {
      return NextResponse.json(
        {
          success: false,
          error: 'Card data not found or expired',
        },
        { status: 404 },
      );
    }

    // Check if data is too old (30 minutes expiry)
    const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000;
    if (cardData.timestamp < thirtyMinutesAgo) {
      cardCallbackData.delete(validatedData.card_id);
      return NextResponse.json(
        {
          success: false,
          error: 'Card data has expired',
        },
        { status: 410 },
      );
    }

    // Remove the data after successful retrieval
    cardCallbackData.delete(validatedData.card_id);

    return NextResponse.json({
      success: true,
      data: {
        card_id: cardData.card_id,
        token: cardData.token,
        last_four: cardData.last_four,
        brand: cardData.brand,
        exp_month: cardData.exp_month,
        exp_year: cardData.exp_year,
        fingerprint: cardData.fingerprint,
      },
    });
  } catch (error) {
    console.error('Card result API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request parameters',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}
