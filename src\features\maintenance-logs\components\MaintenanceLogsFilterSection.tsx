import { FilterSection } from '@/components/filter-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { ColumnVisibilityToggle } from '@/components/ui/ColumnVisibilityToggle';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { usePMACertificates } from '@/features/pma-management/hooks/use-pma-certificates';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import {
  MAINTENANCE_STATUS,
  OPERATION_LOG_TYPES,
} from '../schemas/create-maintenance-log';
import type {
  MaintenanceLogsFilters,
  MaintenanceTableState,
} from '../types/table';
import { MaintenanceLog } from '../types/table';

export function MaintenanceLogsFilterSection({
  filters,
  onFilterChange,
  tableState,
  onTableStateChange,
  columns,
}: {
  filters: MaintenanceLogsFilters;
  onFilterChange: (filters: MaintenanceLogsFilters) => void;
  tableState: MaintenanceTableState;
  onTableStateChange: (state: MaintenanceTableState) => void;
  columns: { key: string; label: string }[];
}) {
  const { selectedProjectId } = useProjectContext();

  // Fetch PMA certificates for the current project
  const { data: pmaCertificates, isLoading: isPMALoading } = usePMACertificates(
    selectedProjectId,
    1,
    1000, // Large page size to get all certificates
    { column: 'pma_number', direction: 'asc' },
    '',
  );
  // Render filter fields for the popover
  const renderFilters = (
    <>
      {/* Status Filter */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">Status</label>
        <Select
          value={filters.status || 'all'}
          onValueChange={(value) =>
            onFilterChange({
              ...filters,
              status:
                value === 'all'
                  ? undefined
                  : (value as (typeof MAINTENANCE_STATUS)[number]),
            })
          }
        >
          <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            {MAINTENANCE_STATUS.map((status) => (
              <SelectItem key={status} value={status}>
                <div className="flex items-center gap-2">
                  <div
                    className={cn(
                      'w-2 h-2 rounded-full',
                      status === 'fully function' && 'bg-emerald-500',
                      status === 'broken' && 'bg-red-500',
                    )}
                  />
                  <span className="capitalize">{status.replace('_', ' ')}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Operation Type Filter */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">
          Operation Type
        </label>
        <Select
          value={filters.operationType || 'all'}
          onValueChange={(value) =>
            onFilterChange({
              ...filters,
              operationType:
                value === 'all'
                  ? undefined
                  : (value as (typeof OPERATION_LOG_TYPES)[number]),
            })
          }
        >
          <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {OPERATION_LOG_TYPES.map((type) => (
              <SelectItem key={type} value={type}>
                <span className="capitalize">{type}</span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* PMA Filter */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">
          PMA Certificate
        </label>
        <Select
          value={filters.pmaId || 'all'}
          onValueChange={(value) =>
            onFilterChange({
              ...filters,
              pmaId: value === 'all' ? undefined : value,
            })
          }
          disabled={isPMALoading}
        >
          <SelectTrigger className="w-full border-gray-300 focus:border-gray-400 rounded-lg">
            <SelectValue
              placeholder={isPMALoading ? 'Loading...' : 'Select PMA'}
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All PMA Certificates</SelectItem>
            {pmaCertificates?.data?.map((pma) => (
              <SelectItem key={pma.id} value={pma.id}>
                {pma.pma_number} - {pma.location}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Date Range */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">Date Range</label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal border-gray-300 hover:border-gray-400 rounded-lg',
                !filters.dateRange?.from && 'text-gray-500',
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {filters.dateRange?.from ? (
                filters.dateRange.to ? (
                  <>
                    {format(filters.dateRange.from, 'MMM dd')} -{' '}
                    {format(filters.dateRange.to, 'MMM dd')}
                  </>
                ) : (
                  format(filters.dateRange.from, 'MMM dd, yyyy')
                )
              ) : (
                'Select date range'
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="range"
              defaultMonth={filters.dateRange?.from}
              selected={{
                from: filters.dateRange?.from,
                to: filters.dateRange?.to,
              }}
              onSelect={(range) =>
                onFilterChange({ ...filters, dateRange: range || {} })
              }
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
      </div>
    </>
  );

  // Render active filter badges
  const getActiveFilterBadges = (
    filters: MaintenanceLogsFilters,
    onFilterChange: (filters: MaintenanceLogsFilters) => void,
  ) => (
    <>
      {filters.status && (
        <Badge
          variant="secondary"
          className="h-7 text-xs bg-emerald-100 text-emerald-700 border border-emerald-200 rounded-lg"
        >
          Status: {filters.status}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange({ ...filters, status: undefined })}
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
      {filters.operationType && (
        <Badge
          variant="secondary"
          className="h-7 text-xs bg-blue-100 text-blue-700 border border-blue-200 rounded-lg"
        >
          Type: {filters.operationType}
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              onFilterChange({ ...filters, operationType: undefined })
            }
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
      {filters.pmaId && (
        <Badge
          variant="secondary"
          className="h-7 text-xs bg-purple-100 text-purple-700 border border-purple-200 rounded-lg"
        >
          PMA:{' '}
          {pmaCertificates?.data?.find((pma) => pma.id === filters.pmaId)
            ?.pma_number || 'Selected'}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange({ ...filters, pmaId: undefined })}
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
      {filters.dateRange?.from && (
        <Badge
          variant="secondary"
          className="h-7 text-xs bg-amber-100 text-amber-700 border border-amber-200 rounded-lg"
        >
          Date: {format(filters.dateRange.from, 'MMM dd')}
          {filters.dateRange.to &&
            ` - ${format(filters.dateRange.to, 'MMM dd')}`}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange({ ...filters, dateRange: {} })}
            className="ml-1 h-auto p-0 hover:bg-transparent"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
    </>
  );

  // Render column visibility toggle
  const columnVisibilityToggle = (
    <ColumnVisibilityToggle
      columns={columns}
      columnVisibility={tableState.columnVisibility}
      onColumnVisibilityChange={(columnVisibility) => {
        // You may want to update visibleColumns or other state here
        const newVisibleColumns: string[] = columns
          .filter((col) => columnVisibility[col.key])
          .map((col) => col.key);

        onTableStateChange({
          ...tableState,
          columnVisibility,
          visibleColumns: newVisibleColumns as Array<keyof MaintenanceLog>, // Properly type the visible columns
        });
      }}
    />
  );

  return (
    <FilterSection
      filters={filters}
      onFilterChange={onFilterChange}
      renderFilters={renderFilters}
      getActiveFilterBadges={getActiveFilterBadges}
      columnVisibilityToggle={columnVisibilityToggle}
      searchPlaceholder="Search by description"
    />
  );
}
