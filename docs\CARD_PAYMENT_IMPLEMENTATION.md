# Card Payment Implementation Guide

This document explains how to use the new card payment functionality implemented using BillPlz tokenization API.

## Overview

The card payment system allows contractors to:

1. **Tokenize cards** - Securely store card details using BillPlz tokenization
2. **Save cards** - Store tokenized cards for future use
3. **Charge cards directly** - Process payments without redirects using saved cards
4. **Manage cards** - View, delete, and set default payment methods

## Database Schema

### New Table: `contractor_payment_methods`

```sql
CREATE TABLE contractor_payment_methods (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  contractor_id uuid NOT NULL REFERENCES contractors(id) ON DELETE CASCADE,
  card_token text NOT NULL,
  card_fingerprint text NOT NULL,
  last_four text NOT NULL,
  brand text NOT NULL,
  exp_month integer NOT NULL,
  exp_year integer NOT NULL,
  is_default boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);
```

## API Endpoints

### 1. Card Tokenization

**Endpoint:** `POST /api/billing/tokenize-card`

**Purpose:** Convert card details into a secure token

**Request:**

```json
{
  "number": "****************",
  "exp_month": 12,
  "exp_year": 2025,
  "cvc": "123",
  "name": "John Doe"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "token": "tok_xxxxx",
    "fingerprint": "fp_xxxxx",
    "last_four": "4242",
    "brand": "Visa",
    "exp_month": 12,
    "exp_year": 2025
  }
}
```

### 2. Save Card

**Endpoint:** `POST /api/billing/saved-cards`

**Purpose:** Save a tokenized card for future use

**Request:**

```json
{
  "contractor_id": "123e4567-e89b-12d3-a456-************",
  "token": "tok_xxxxx",
  "fingerprint": "fp_xxxxx",
  "last_four": "4242",
  "brand": "Visa",
  "exp_month": 12,
  "exp_year": 2025,
  "is_default": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "card_id": "456e7890-e89b-12d3-a456-************"
  }
}
```

### 3. Get Saved Cards

**Endpoint:** `GET /api/billing/saved-cards?contractor_id={uuid}`

**Purpose:** Retrieve all saved cards for a contractor

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "456e7890-e89b-12d3-a456-************",
      "card_token": "tok_xxxxx",
      "last_four": "4242",
      "brand": "Visa",
      "exp_month": 12,
      "exp_year": 2025,
      "is_default": true
    }
  ]
}
```

### 4. Bulk Payment with Card

**Endpoint:** `POST /api/billing/bulk-payment`

**Purpose:** Process payment using saved card or BillPlz redirect

**Request (Card Payment):**

```json
{
  "contractor_id": "123e4567-e89b-12d3-a456-************",
  "subscription_ids": ["sub1", "sub2"],
  "payment_method_id": "pm_xxxxx",
  "total_amount": 150.0,
  "payment_method": "saved_card",
  "card_token": "tok_xxxxx"
}
```

**Request (BillPlz Redirect):**

```json
{
  "contractor_id": "123e4567-e89b-12d3-a456-************",
  "subscription_ids": ["sub1", "sub2"],
  "payment_method_id": "pm_xxxxx",
  "total_amount": 150.0,
  "payment_method": "billplz_redirect"
}
```

**Response (Card Payment):**

```json
{
  "success": true,
  "data": {
    "charge_id": "charge_xxxxx",
    "payment_record_ids": ["rec1", "rec2"],
    "total_amount": 150.0,
    "processed_subscriptions": ["sub1", "sub2"],
    "payment_method": "card_charge"
  }
}
```

**Response (BillPlz Redirect):**

```json
{
  "success": true,
  "data": {
    "payment_url": "https://billplz.com/bills/xxxxx",
    "billplz_bill_id": "bill_xxxxx",
    "payment_record_ids": ["rec1", "rec2"],
    "total_amount": 150.0,
    "processed_subscriptions": ["sub1", "sub2"],
    "payment_method": "billplz_redirect"
  }
}
```

### 5. Delete Saved Card

**Endpoint:** `DELETE /api/billing/saved-cards`

**Request:**

```json
{
  "contractor_id": "123e4567-e89b-12d3-a456-************",
  "card_id": "456e7890-e89b-12d3-a456-************"
}
```

### 6. Set Default Card

**Endpoint:** `PATCH /api/billing/saved-cards`

**Request:**

```json
{
  "contractor_id": "123e4567-e89b-12d3-a456-************",
  "card_id": "456e7890-e89b-12d3-a456-************"
}
```

## Service Methods

### BulkPaymentService

The `BulkPaymentService` class provides the following methods:

#### Card Management

- `tokenizeCard(cardDetails)` - Tokenize a new card
- `saveCardForContractor(cardData)` - Save tokenized card
- `getSavedCards(contractorId)` - Get saved cards
- `deleteSavedCard(contractorId, cardId)` - Delete a card
- `setDefaultCard(contractorId, cardId)` - Set default card

#### Payment Processing

- `createConsolidatedPayment(request)` - Process bulk payment (supports both methods)

## Payment Flow Comparison

### BillPlz Redirect Flow

1. Create consolidated payment request
2. BillPlz creates a bill
3. User redirected to BillPlz payment page
4. User completes payment on BillPlz
5. Webhook notification received
6. Payment records updated

### Card Payment Flow

1. Tokenize card (first time only)
2. Save card for contractor (optional)
3. Create consolidated payment request with card token
4. BillPlz charges card directly
5. Payment completed immediately
6. Payment records updated automatically

## Security Considerations

1. **PCI Compliance** - Card details are never stored; only BillPlz tokens are saved
2. **Row Level Security** - Database policies ensure contractors can only access their own cards
3. **API Validation** - All endpoints validate input data and contractor ownership
4. **Server-side Only** - All card operations are server-side only for security

## Error Handling

All API endpoints return standardized error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": {} // Optional additional error details
}
```

Common error scenarios:

- Invalid card details during tokenization
- Card tokenization failures from BillPlz
- Card charging failures
- Database constraint violations
- Unauthorized access attempts

## Frontend Integration

Example React component for card payment:

```tsx
// Save card flow
const tokenizeAndSaveCard = async (cardDetails, contractorId) => {
  // 1. Tokenize card
  const tokenResult = await fetch('/api/billing/tokenize-card', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(cardDetails),
  });

  // 2. Save tokenized card
  if (tokenResult.success) {
    await fetch('/api/billing/saved-cards', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contractor_id: contractorId,
        ...tokenResult.data,
        is_default: true,
      }),
    });
  }
};

// Payment with saved card
const payWithSavedCard = async (contractorId, subscriptionIds, cardToken) => {
  return fetch('/api/billing/bulk-payment', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      contractor_id: contractorId,
      subscription_ids: subscriptionIds,
      payment_method_id: 'saved_card',
      payment_method: 'saved_card',
      card_token: cardToken,
      total_amount: calculateTotal(subscriptionIds),
    }),
  });
};
```

## Migration Steps

1. **Run the database migration** to create the `contractor_payment_methods` table
2. **Deploy the updated code** with the new API endpoints and services
3. **Update frontend components** to support card payment option
4. **Test thoroughly** in sandbox environment
5. **Monitor payment success rates** and error logs

## Configuration

Ensure the following environment variables are set:

```env
# BillPlz Configuration (existing)
BILLPLZ_API_KEY=your_api_key
BILLPLZ_X_SIGNATURE_KEY=your_signature_key
BILLPLZ_COLLECTION_ID=your_collection_id
BILLPLZ_SANDBOX_MODE=true # for testing

# Database (existing)
DATABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

## Testing

Use these test card numbers in sandbox mode:

- **Visa**: ****************
- **Mastercard**: ****************
- **Success CVC**: 123
- **Decline CVC**: 456

All test cards should use:

- **Expiry**: Any future date
- **Name**: Any name

## Support

For issues related to:

- **BillPlz API**: Check BillPlz documentation and error codes
- **Database errors**: Check Supabase logs and RLS policies
- **Frontend integration**: Ensure proper API request formatting
- **Security**: Verify server-side only usage and proper validation
