'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  ComplaintFilterSection,
  ComplaintFilters,
} from '@/features/complaints/components';
import {
  ComplaintWithRelations,
  useComplaints,
} from '@/features/complaints/hooks/use-complaints-simple';
import { ComplaintUI } from '@/features/complaints/types/ui-types';
import { useProjectContext } from '@/providers/project-context';
import {
  AlertCircle,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Edit,
  Plus,
  TrendingUp,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function ComplaintsPage() {
  const router = useRouter();
  const { selectedProjectId } = useProjectContext();

  // Translations
  const t = useTranslations('complaints');
  // Filter states
  const [filters, setFilters] = useState<ComplaintFilters>({
    search: '',
    status: undefined,
    followUp: undefined,
    dateRange: {},
  });

  // Fetch real complaints data
  const { data: complaintsData = [], isLoading, error } = useComplaints();
  // Filter function
  const applyFilters = (complaints: ComplaintUI[]) => {
    return complaints.filter((complaint) => {
      // Search filter
      const matchesSearch =
        !filters.search ||
        complaint.email?.toLowerCase().includes(filters.search.toLowerCase()) ||
        complaint.contractor_name
          ?.toLowerCase()
          .includes(filters.search.toLowerCase()) ||
        complaint.location
          ?.toLowerCase()
          .includes(filters.search.toLowerCase()) ||
        complaint.no_pma_lif
          ?.toLowerCase()
          .includes(filters.search.toLowerCase()) ||
        complaint.description
          ?.toLowerCase()
          .includes(filters.search.toLowerCase());

      // Status filter
      const matchesStatus =
        !filters.status || complaint.status === filters.status;

      // Follow up filter
      const matchesFollowUp =
        !filters.followUp || complaint.follow_up === filters.followUp;

      // Date range filter
      const matchesDateRange = (() => {
        if (!filters.dateRange?.from) return true;
        const complaintDate = new Date(complaint.date);
        const fromDate = filters.dateRange.from;
        const toDate = filters.dateRange.to || fromDate;
        return complaintDate >= fromDate && complaintDate <= toDate;
      })();

      return (
        matchesSearch && matchesStatus && matchesFollowUp && matchesDateRange
      );
    });
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: ComplaintFilters) => {
    setFilters(newFilters);
  };
  // Helper function to determine if Section A is completed
  const isSectionACompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.email &&
      complaint.date &&
      complaint.expected_completion_date &&
      complaint.contractor_name &&
      complaint.location &&
      complaint.no_pma_lif &&
      complaint.description
    );
  };

  // Helper function to determine if Section B is completed
  const isSectionBCompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.actual_completion_date &&
      complaint.repair_completion_time &&
      complaint.cause_of_damage &&
      complaint.correction_action &&
      complaint.proof_of_repair_urls &&
      complaint.proof_of_repair_urls.length > 0
    );
  };

  // Helper function to determine follow-up status based on completion and admin approval
  /**
   * Determines follow-up status based on section completion and admin verification:
   * 1. 'in_progress': User completed Section A (damage complaint form) only
   * 2. 'pending_approval': User completed both Section A & B (damage complaint + repair info)
   * 3. 'verified': Admin has approved the complaint (only admin can set this)
   */
  const determineFollowUpStatus = (
    complaint: ComplaintWithRelations,
  ): 'in_progress' | 'pending_approval' | 'verified' => {
    const sectionAComplete = isSectionACompleted(complaint);
    const sectionBComplete = isSectionBCompleted(complaint);

    // If admin has verified/approved, return verified (only admin can set this)
    if (complaint.follow_up === 'verified') {
      return 'verified';
    }

    // If both sections are completed, set to pending approval
    if (sectionAComplete && sectionBComplete) {
      return 'pending_approval';
    }

    // If only Section A is completed, set to in progress
    if (sectionAComplete) {
      return 'in_progress';
    }

    // If neither section is completed, default to in_progress
    return 'in_progress';
  };

  // Convert database complaints to UI format
  const rawComplaints: ComplaintUI[] = complaintsData.map((complaint) => {
    const sectionAComplete = isSectionACompleted(complaint);
    const sectionBComplete = isSectionBCompleted(complaint);
    const computedFollowUp = determineFollowUpStatus(complaint);

    return {
      // Direct database field mapping
      id: complaint.id,
      email: complaint.email,
      number:
        complaint.number ||
        `DCL-${new Date(complaint.created_at || '').getFullYear()}-${complaint.id.slice(-4)}`,
      date: complaint.date || complaint.created_at || new Date().toISOString(),
      expected_completion_date:
        complaint.expected_completion_date ||
        complaint.created_at ||
        new Date().toISOString(),
      contractor_name: complaint.contractor_name,
      location: complaint.location,
      no_pma_lif: complaint.no_pma_lif,
      description: complaint.description,
      involves_mantrap: complaint.involves_mantrap,

      // Section B fields
      actual_completion_date: complaint.actual_completion_date,
      repair_completion_time: complaint.repair_completion_time,
      cause_of_damage: complaint.cause_of_damage,
      correction_action: complaint.correction_action,
      proof_of_repair_urls: complaint.proof_of_repair_urls,
      before_repair_files: complaint.before_repair_files,
      repair_cost: complaint.repair_cost,

      // Status and metadata
      status: complaint.status,
      follow_up: computedFollowUp, // Use computed follow-up status
      created_at: complaint.created_at,

      // Computed fields
      sectionACompleted: sectionAComplete,
      sectionBCompleted: sectionBComplete,
      proofOfRepairFiles:
        complaint.proof_of_repair_urls?.map((url: string, index: number) => ({
          name: `Repair Evidence ${index + 1}`,
          url,
          size: 0, // Size not stored in current schema
        })) || [],
      beforeRepairFiles:
        complaint.before_repair_files?.map((url: string, index: number) => ({
          name: `Before Repair Photo ${index + 1}`,
          url,
          size: 0, // Size not stored in current schema
        })) || [],
    };
  });
  // Apply filters to get final complaints list
  const complaints = applyFilters(rawComplaints);
  // Calculate stats with the new follow-up system
  const inProgressReports = complaints.filter(
    (c: ComplaintUI) => c.follow_up === 'in_progress',
  ).length;
  const pendingApprovalReports = complaints.filter(
    (c: ComplaintUI) => c.follow_up === 'pending_approval',
  ).length;
  const verifiedReports = complaints.filter(
    (c: ComplaintUI) => c.follow_up === 'verified',
  ).length;
  const totalReports = complaints.length;
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-800 border-green-200"
          >
            {t('status.open')}
          </Badge>
        );
      case 'on_hold':
        return (
          <Badge
            variant="outline"
            className="bg-orange-50 text-orange-800 border-orange-200"
          >
            {t('status.onHold')}
          </Badge>
        );
      case 'closed':
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-800 border-red-200"
          >
            {t('status.closed')}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getFollowUpBadge = (followUp: string) => {
    switch (followUp) {
      case 'in_progress':
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-800 border-blue-200 flex items-center gap-1"
          >
            <Clock className="h-3 w-3" />
            {t('followUp.inProgress')}
          </Badge>
        );
      case 'pending_approval':
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-800 border-yellow-200 flex items-center gap-1"
          >
            <AlertCircle className="h-3 w-3" />
            {t('followUp.pendingApproval')}
          </Badge>
        );
      case 'verified':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-800 border-green-200 flex items-center gap-1"
          >
            <CheckCircle className="h-3 w-3" />
            {t('followUp.verified')}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{followUp}</Badge>;
    }
  };

  const handleViewComplaint = (complaint: ComplaintUI) => {
    // Navigate to complaint details page or open modal
    router.push(`/complaints/${complaint.id}`);
  };

  const handleEditComplaintFromTable = (complaint: ComplaintUI) => {
    router.push(`/complaints/${complaint.id}/edit`);
  };
  const handleCreateNewComplaint = () => {
    router.push('/complaints/new');
  };

  // Handle no project selected
  if (!selectedProjectId) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex flex-col items-center gap-4 py-8">
          <AlertCircle className="h-12 w-12 text-amber-500" />
          <div className="text-center">
            <h2 className="text-lg font-semibold mb-2">No Project Selected</h2>
            <p className="text-muted-foreground">
              Please select a project to view its complaint logs.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            Loading complaints...
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex flex-col items-center gap-2 py-8">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <p className="text-red-600">Failed to load complaints</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="mb-4 sm:mb-6">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 tracking-tight">
            {t('title')}
          </h1>
          <p className="text-gray-600 mt-2 sm:mt-3 text-sm sm:text-base lg:text-lg max-w-2xl">
            {t('subtitle')}
          </p>
        </div>
        <div className="flex gap-3">
          {/* <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            {t('exportReport')}
          </Button> */}
          <Button
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            onClick={handleCreateNewComplaint}
          >
            <Plus className="h-4 w-4" />
            {t('createAduan')}
          </Button>
        </div>
      </div>
      {/* Statistics Cards - Matching the design */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-blue-700">
                  {inProgressReports}
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  {t('stats.inProgress')}
                </p>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-orange-50 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-orange-700">
                  {pendingApprovalReports}
                </p>
                <p className="text-sm text-orange-600 mt-1">
                  {t('stats.pendingApproval')}
                </p>
              </div>
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-green-700">
                  {verifiedReports}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  {t('stats.verified')}
                </p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-purple-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-purple-700">
                  {totalReports}
                </p>
                <p className="text-sm text-purple-600 mt-1">
                  {t('stats.totalReports')}
                </p>
              </div>
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6">
        {' '}
        {/* Aduan Reports Table */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
              <CardTitle className="text-lg font-semibold">
                {t('table.title')}
              </CardTitle>
              <div className="w-full lg:w-auto lg:max-w-lg">
                <ComplaintFilterSection
                  filters={filters}
                  onFilterChange={handleFilterChange}
                />
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-medium text-gray-700">
                    {t('table.reportId')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.dateSubmitted')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.pmaNumber')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.location')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.issueSummary')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.status')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.followUp')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.completionDate')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.actions')}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {complaints.slice(0, 5).map((complaint: ComplaintUI) => (
                  <TableRow key={complaint.id} className="hover:bg-gray-50">
                    <TableCell>
                      <Button
                        variant="link"
                        className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium"
                        onClick={() => handleViewComplaint(complaint)}
                      >
                        {complaint.number}
                      </Button>
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {new Date(complaint.date).toLocaleDateString('en-MY')}
                    </TableCell>
                    <TableCell className="text-sm">
                      <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                        {complaint.no_pma_lif || 'N/A'}
                      </code>
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {complaint.location}
                    </TableCell>
                    <TableCell className="text-sm text-gray-600 max-w-xs truncate">
                      {complaint.description || 'No description'}
                    </TableCell>
                    <TableCell>{getStatusBadge(complaint.status)}</TableCell>
                    <TableCell>
                      {getFollowUpBadge(complaint.follow_up)}
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {complaint.actual_completion_date
                        ? new Date(
                            complaint.actual_completion_date,
                          ).toLocaleDateString('en-MY')
                        : '-'}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() =>
                            handleEditComplaintFromTable(complaint)
                          }
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {/* Pagination */}
            <div className="flex justify-between items-center p-4 border-t">
              <span className="text-sm text-gray-600">
                {t('pagination.showing', {
                  start: 1,
                  end: Math.min(5, complaints.length),
                  total: complaints.length,
                })}
              </span>
              <div className="flex gap-1">
                <Button variant="outline" size="sm">
                  <ChevronLeft className="h-4 w-4" />
                  {t('pagination.previous')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-blue-600 text-white"
                >
                  1
                </Button>
                <Button variant="outline" size="sm">
                  2
                </Button>
                <Button variant="outline" size="sm">
                  3
                </Button>
                <Button variant="outline" size="sm">
                  {t('pagination.next')}
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
