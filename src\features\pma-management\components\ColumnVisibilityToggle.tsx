'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SlidersHorizontal } from 'lucide-react';

interface ColumnVisibilityToggleProps {
  columns: { key: string; label: string }[];
  columnVisibility: Record<string, boolean>;
  onColumnVisibilityChange: (visibility: Record<string, boolean>) => void;
}

export function ColumnVisibilityToggle({
  columns,
  columnVisibility,
  onColumnVisibilityChange,
}: ColumnVisibilityToggleProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="ml-auto">
          <SlidersHorizontal className="mr-2 h-4 w-4" />
          View
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {columns
          .filter((column) => column.key !== 'actions')
          .map((column) => {
            return (
              <DropdownMenuCheckboxItem
                key={column.key}
                className="capitalize"
                checked={columnVisibility[column.key]}
                onCheckedChange={(value) =>
                  onColumnVisibilityChange({
                    ...columnVisibility,
                    [column.key]: !!value,
                  })
                }
              >
                {column.label}
              </DropdownMenuCheckboxItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
