'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Download, FileText } from 'lucide-react';

interface CertificatePreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  certificateUrl: string;
  personName: string;
}

export function CertificatePreviewModal({
  open,
  onOpenChange,
  certificateUrl,
  personName,
}: CertificatePreviewModalProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [pdfViewMethod, setPdfViewMethod] = useState<
    'iframe' | 'embed' | 'object' | 'proxy' | 'fallback'
  >('iframe');

  // Get file extension from URL
  const getFileExtension = (url: string) => {
    try {
      const pathname = new URL(url).pathname;
      return pathname.split('.').pop()?.toLowerCase() || '';
    } catch {
      return '';
    }
  };

  const isPdfFile = (url: string) => getFileExtension(url) === 'pdf';

  const isImageFile = (url: string) =>
    ['jpg', 'jpeg', 'png'].includes(getFileExtension(url));

  const getPdfPreviewUrl = (url: string) => {
    // Always use proxy for consistent inline viewing
    return getProxyUrl(url);
  };

  const getProxyUrl = (url: string) =>
    `/api/preview-pdf?url=${encodeURIComponent(url)}`;

  const tryNextPdfMethod = () => {
    setPdfViewMethod((prev) => {
      switch (prev) {
        case 'iframe':
          return 'embed';
        case 'embed':
          return 'object';
        case 'object':
          return 'proxy';
        case 'proxy':
          return 'fallback';
        default:
          return 'fallback';
      }
    });
  };

  const handleDownload = async () => {
    try {
      // Use proxy API to handle CORS and authentication issues
      const response = await fetch(getProxyUrl(certificateUrl));
      if (!response.ok) throw new Error('Download failed');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${personName.replace(/\s+/g, '_')}_certificate.${
        getFileExtension(certificateUrl) || 'pdf'
      }`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch {
      window.open(certificateUrl, '_blank');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[90vh] p-0 gap-0">
        <DialogHeader className=" flex-row items-center justify-between p-6 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <FileText className="h-5 w-5 text-primary" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-foreground">
                Certificate Preview
              </DialogTitle>
              <p className="text-sm text-muted-foreground mt-1">{personName}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 mr-6">
            <Button variant="outline" size="sm" onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 relative bg-muted/30">
          {isLoading && (
            <div className="absolute inset-0 items-center justify-center bg-background/80 backdrop-blur-sm z-10 ">
              <div className="animate-spin rounded-full h-10 w-10 border-2 border-primary/30 border-t-primary mb-4" />
              <p className="text-sm text-muted-foreground">
                Loading certificate...
              </p>
            </div>
          )}

          {isPdfFile(certificateUrl) ? (
            <>
              {pdfViewMethod === 'iframe' && (
                <iframe
                  src={getPdfPreviewUrl(certificateUrl)}
                  className="w-full h-full border-0"
                  title="Certificate PDF Preview"
                  onLoad={() => setIsLoading(false)}
                  onError={tryNextPdfMethod}
                />
              )}

              {pdfViewMethod === 'embed' && (
                <embed
                  src={getPdfPreviewUrl(certificateUrl)}
                  type="application/pdf"
                  className="w-full h-full"
                  onLoad={() => setIsLoading(false)}
                  onError={tryNextPdfMethod}
                />
              )}

              {pdfViewMethod === 'object' && (
                <object
                  data={getPdfPreviewUrl(certificateUrl)}
                  type="application/pdf"
                  className="w-full h-full"
                  onLoad={() => setIsLoading(false)}
                  onError={tryNextPdfMethod}
                >
                  <div className="h-full flex items-center justify-center">
                    <Button onClick={tryNextPdfMethod} variant="outline">
                      Try Alternative Method
                    </Button>
                  </div>
                </object>
              )}

              {pdfViewMethod === 'proxy' && (
                <iframe
                  src={getProxyUrl(certificateUrl)}
                  className="w-full h-full border-0"
                  title="Certificate PDF (Proxy)"
                  onLoad={() => setIsLoading(false)}
                  onError={tryNextPdfMethod}
                />
              )}

              {pdfViewMethod === 'fallback' && (
                <div className="h-full flex flex-col items-center justify-center text-center">
                  <FileText className="w-10 h-10 text-muted-foreground mb-4" />
                  <p className="mb-2 text-gray-700">Preview unavailable</p>
                  <p className="text-sm text-muted-foreground mb-4">
                    This file cannot be previewed in your browser. You can try
                    downloading it or opening it in a new tab.
                  </p>
                  <div className="flex gap-2">
                    <Button onClick={handleDownload}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => window.open(certificateUrl, '_blank')}
                    >
                      Open in New Tab
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setPdfViewMethod('iframe');
                        setIsLoading(true);
                      }}
                    >
                      🔄 Try Again
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : isImageFile(certificateUrl) ? (
            <div className="h-full flex items-center justify-center bg-gray-50">
              <Image
                src={certificateUrl}
                alt="Certificate"
                width={800}
                height={600}
                className="max-w-full max-h-full object-contain"
                onLoad={() => setIsLoading(false)}
              />
            </div>
          ) : (
            <div className="h-full flex items-center justify-center bg-gray-50 p-6">
              <div className="text-center">
                <FileText className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                <p className="mb-2 text-gray-700">Unsupported file type</p>
                <Button onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download File
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
