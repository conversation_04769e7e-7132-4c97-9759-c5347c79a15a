-- ================================
-- ADD COMPETENT_PERSON TABLE
-- Migration to add competent person management with contractor relationship
-- ================================

-- Create the CP type enum
DO $$ BEGIN 
  CREATE TYPE cp_type AS ENUM ('CP1', 'CP2', 'CP3'); 
EXCEPTION 
  WHEN duplicate_object THEN NULL; 
END $$;

-- ================================
-- COMPETENT_PERSON TABLE
-- ================================
CREATE TABLE competent_person (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  contractor_id uuid NOT NULL REFERENCES contractors(id) ON DELETE CASCADE,
  
  -- Personal information
  name text NOT NULL,
  ic_no text NOT NULL UNIQUE,
  phone_no text,
  address text,
  
  -- Professional information
  cp_type cp_type NOT NULL,
  cp_registeration_no text,
  cp_registeration_cert text, -- URL/path to certificate file
  cert_exp_date date,
  no_of_pma integer DEFAULT 0,
  
  -- Audit trail (following the pattern from other tables)
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid,
  updated_by uuid,
  deleted_by uuid
);

-- ================================
-- NOTE: PMA_CERTIFICATES TABLE UPDATE
-- ================================
-- The competent_person_id column in pma_certificates will be updated in the next migration
-- to reference the new competent_person table instead of the users table

-- ================================
-- ADD FOREIGN KEY CONSTRAINTS (Audit Trail)
-- ================================
ALTER TABLE competent_person 
ADD CONSTRAINT fk_competent_person_contractor FOREIGN KEY (contractor_id) REFERENCES contractors(id),
ADD CONSTRAINT fk_competent_person_created_by FOREIGN KEY (created_by) REFERENCES users(id),
ADD CONSTRAINT fk_competent_person_updated_by FOREIGN KEY (updated_by) REFERENCES users(id),
ADD CONSTRAINT fk_competent_person_deleted_by FOREIGN KEY (deleted_by) REFERENCES users(id);

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================
CREATE INDEX idx_competent_person_contractor_id ON competent_person(contractor_id);
CREATE INDEX idx_competent_person_ic_no ON competent_person(ic_no);
CREATE INDEX idx_competent_person_cp_type ON competent_person(cp_type);
CREATE INDEX idx_competent_person_cert_exp_date ON competent_person(cert_exp_date);
CREATE INDEX idx_competent_person_deleted_at ON competent_person(deleted_at);

-- ================================
-- COMMENTS FOR DOCUMENTATION
-- ================================
COMMENT ON TABLE competent_person IS 
'Competent persons associated with contractors. Each contractor can have multiple competent persons with different CP types (CP1, CP2, CP3).';

COMMENT ON COLUMN competent_person.contractor_id IS 
'Foreign key to contractors table - one contractor can have many competent persons';

COMMENT ON COLUMN competent_person.ic_no IS 
'Identity card number - must be unique across all competent persons';

COMMENT ON COLUMN competent_person.cp_type IS 
'Competent person type: CP1, CP2, or CP3';

COMMENT ON COLUMN competent_person.cp_registeration_cert IS 
'URL or file path to the competent person registration certificate';

COMMENT ON COLUMN competent_person.no_of_pma IS 
'Number of PMAs associated with this competent person';

-- ================================
-- END OF MIGRATION
-- ================================
