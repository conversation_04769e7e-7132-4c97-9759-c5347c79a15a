/**
 * BillPlz Payment Gateway Integration
 * Central export file for all BillPlz functionality
 */

// ================================
// MAIN EXPORTS
// ================================

// Client and configuration
export { BillPlzClient, getBillPlzClient } from './client';
export {
  getBillPlzConfig,
  getBillPlzConfigSingleton,
  validateBillPlzConfig,
  isSandboxMode,
  getApiBaseUrl,
  getApiEndpoint,
  getMaskedApiKey,
  BILLPLZ_ENDPOINTS,
  BILLPLZ_DEFAULTS,
  RATE_LIMIT_CONFIG,
  LOGGING_CONFIG,
} from './config';

// Types
export type {
  // Core API types
  BillPlzCreateBillRequest,
  BillPlzBill,
  BillPlzCreateCollectionRequest,
  BillPlzCollection,
  BillPlzTransaction,
  BillPlzError,

  // Error types
  BillPlzApiError,
  BillPlzNetworkError,
  BillPlzValidationError,
  BillPlzRateLimitError,

  // Response types
  BillPlzApiResponse,
  BillPlzListResponse,

  // Webhook types
  BillPlzWebhookPayload,
  WebhookVerificationResult,

  // Utility types
  BillReferenceOptions,
  AmountConversionOptions,
  StatusMapping,
  RateLimitInfo,
  RateLimitState,

  // Configuration types
  BillPlzConfig,
  BillPlzClientOptions,
  RequestOptions,
  HttpMethod,

  // Logging types
  LogContext,
  RequestLog,
  ResponseLog,
  ErrorLog,

  // Constants
  BillPlzState,
  PaymentChannel,
  HttpStatusCode,
} from './types';

// Type guards
export {
  isBillPlzError,
  isBillPlzNetworkError,
  isBillPlzValidationError,
  isBillPlzRateLimitError,
  isWebhookPayload,
} from './types';

// Utilities
export {
  // Bill reference utilities
  generateBillReference,
  parseBillReference,

  // Amount conversion utilities
  convertMyrToCents,
  convertCentsToMyr,
  formatAmount,
  validateAmount,

  // Status mapping utilities
  mapBillPlzStateToPaymentStatus,
  mapPaymentStatusToBillPlzState,
  determineSubscriptionStatus,
  getStatusMapping,

  // Date utilities
  formatDateForBillPlz,
  calculateDueDate,
  parseBillPlzDate,
  isDateExpired,

  // Webhook utilities
  generateWebhookSignature,
  verifyWebhookSignature,
  verifyWebhookPayload,

  // Validation utilities
  validateEmail,
  validateMalaysianMobile,
  formatMalaysianMobile,
  validateCollectionId,
  validateBillId,

  // Helper utilities
  generateRequestId,
  maskSensitiveData,
  deepClone,
  retryWithBackoff,
} from './utils';

// ================================
// CONSTANTS
// ================================

export {
  BILLPLZ_STATES,
  BILLPLZ_PAYMENT_CHANNELS,
  HTTP_STATUS_CODES,
} from './types';

// ================================
// CONVENIENCE EXPORTS
// ================================

// Default client instance
export { default } from './client';

// Most commonly used functions
export {
  convertMyrToCents as toMyrCents,
  convertCentsToMyr as fromMyrCents,
  formatAmount as formatMyr,
} from './utils';
