<!doctype html>
<html lang="ms">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON></title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f9f9f9;
      }
      .email-container {
        background-color: white;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .logo {
        font-size: 28px;
        font-weight: bold;
        color: #6366f1;
        margin-bottom: 10px;
      }
      .title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 10px;
      }
      .subtitle {
        font-size: 16px;
        color: #6b7280;
        margin-bottom: 30px;
      }
      .button-container {
        text-align: center;
        margin: 30px 0;
      }
      .confirm-button {
        display: inline-block;
        background-color: #10b981;
        color: white;
        padding: 12px 24px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 600;
        font-size: 16px;
      }
      .confirm-button:hover {
        background-color: #059669;
      }
      .instructions {
        font-size: 16px;
        color: #4b5563;
        margin: 20px 0;
        text-align: center;
      }
      .footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
        font-size: 14px;
        color: #6b7280;
        text-align: center;
      }
      .security-note {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 15px;
        margin: 20px 0;
        font-size: 14px;
        color: #92400e;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="logo">SimPLE</div>
        <h1 class="title">Sahkan Akaun Anda</h1>
        <p class="subtitle">
          Satu langkah terakhir untuk mengaktifkan akaun SimPLE anda
        </p>
      </div>

      <div class="button-container">
        <a href="{{ .ConfirmationURL }}" class="confirm-button">Sahkan Akaun</a>
      </div>

      <p class="instructions">
        Klik butang di atas untuk mengesahkan alamat e-mel anda dan mengaktifkan
        akaun SimPLE anda.
      </p>

      <div class="security-note">
        <strong>Notis Keselamatan:</strong> Pautan pengesahan ini akan tamat
        tempoh dalam 24 jam. Jika anda tidak membuat akaun SimPLE, sila abaikan
        e-mel ini.
      </div>

      <div class="footer">
        <p>E-mel ini dihantar daripada SimPLE</p>
        <p>Jika anda menghadapi masalah, sila hubungi pasukan sokongan kami.</p>
      </div>
    </div>
  </body>
</html>
