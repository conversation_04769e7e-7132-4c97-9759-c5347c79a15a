import { supabase } from '@/lib/supabase';
import type {
  EmailInvitationResult,
  InviteUserByEmailParams,
} from '../types/invitation';

/**
 * Check if there's already an active invitation for this email and project
 */
async function checkExistingInvitation(
  email: string,
  projectId: string,
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('project_invitations')
      .select('id')
      .eq('invitee_email', email.toLowerCase().trim())
      .eq('project_id', projectId)
      .eq('status', 'pending')
      .gt('expiry_date', new Date().toISOString())
      .maybeSingle();

    if (error) {
      console.error('Error checking existing invitation:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Error in checkExistingInvitation:', error);
    return false;
  }
}

/**
 * Send invitation using our API endpoint that handles Supabase Admin calls
 */
async function sendInvitationViaAPI(
  params: InviteUserByEmailParams,
): Promise<{ success: boolean; error?: string; supabaseUserId?: string }> {
  try {
    // Get current session token for authentication
    let { data: sessionData } = await supabase.auth.getSession();

    // If no session, try to refresh
    if (!sessionData.session?.access_token) {
      const { data: refreshData } = await supabase.auth.refreshSession();
      sessionData = refreshData;
    }

    if (!sessionData.session?.access_token) {
      return {
        success: false,
        error: 'Authentication required - please login again',
      };
    }

    const response = await fetch('/api/invite', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${sessionData.session.access_token}`,
      },
      body: JSON.stringify({
        email: params.email,
        projectId: params.projectId,
        role: params.role,
        inviterId: params.inviterId,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      // Handle specific authentication errors
      if (response.status === 401) {
        // Try refresh token one more time
        const { data: retryRefreshData } = await supabase.auth.refreshSession();
        if (retryRefreshData.session?.access_token) {
          // Retry the request with new token
          const retryResponse = await fetch('/api/invite', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${retryRefreshData.session.access_token}`,
            },
            body: JSON.stringify({
              email: params.email,
              projectId: params.projectId,
              role: params.role,
              inviterId: params.inviterId,
            }),
          });

          const retryResult = await retryResponse.json();
          if (retryResponse.ok) {
            return {
              success: true,
              supabaseUserId: retryResult.supabaseUserId,
            };
          }
        }

        return {
          success: false,
          error: 'Session expired - please refresh the page and try again',
        };
      }

      return {
        success: false,
        error: result.error || 'Failed to send invitation',
      };
    }

    return {
      success: true,
      supabaseUserId: result.supabaseUserId,
    };
  } catch (error) {
    console.error('Error in sendInvitationViaAPI:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Invite a user by email to join a project using Supabase Auth via API
 * Creates an invitation record and sends an email with a secure token
 */
export async function inviteUserByEmail(
  params: InviteUserByEmailParams,
): Promise<EmailInvitationResult> {
  try {
    // Check if there's already a pending invitation
    const hasExistingInvite = await checkExistingInvitation(
      params.email,
      params.projectId,
    );

    if (hasExistingInvite) {
      return {
        success: false,
        error: 'An active invitation already exists for this email address.',
      };
    }

    // Send invitation via our API endpoint
    const inviteResult = await sendInvitationViaAPI(params);

    if (!inviteResult.success) {
      return {
        success: false,
        error: inviteResult.error || 'Failed to send invitation',
      };
    }

    return {
      success: true,
      supabaseUserId: inviteResult.supabaseUserId,
      message:
        'Invitation sent successfully! The user will receive an email with instructions to join the project.',
    };
  } catch (error) {
    console.error('Error in inviteUserByEmail:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred while sending the invitation.',
    };
  }
}
