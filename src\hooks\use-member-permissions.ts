import { useUserWithProfile } from '@/hooks/use-auth';
import { useProject } from '@/features/projects';

interface MemberPermissions {
  canRemoveMembers: boolean;
  canInviteMembers: boolean;
  canRestoreMembers: boolean;
  isAdmin: boolean;
  isCompetentPerson: boolean;
  canRemoveMember: (memberId: string, memberUserId: string) => boolean;
}

export function useMemberPermissions(projectId: string): MemberPermissions {
  const { data: user } = useUserWithProfile();
  const { data: project } = useProject(projectId);

  if (!user || !project) {
    return {
      canRemoveMembers: false,
      canInviteMembers: false,
      canRestoreMembers: false,
      isAdmin: false,
      isCompetentPerson: false,
      canRemoveMember: () => false,
    };
  }

  // Find current user's role in the project
  const currentUserMembership = project.project_users?.find(
    (pu) => pu.user.id === user.id && pu.is_active && pu.status === 'accepted',
  );

  const userRole = currentUserMembership?.role;
  const isAdmin = userRole === 'admin';
  const isCompetentPerson = userRole === 'competent_person';

  // Function to check if a specific member can be removed
  const canRemoveMember = (_memberId: string, memberUserId: string) => {
    // Must have general permission to remove members
    if (!isAdmin && !isCompetentPerson) return false;

    // Cannot remove yourself
    if (memberUserId === user.id) return false;

    return true;
  };

  return {
    canRemoveMembers: isAdmin || isCompetentPerson,
    canInviteMembers: isAdmin,
    canRestoreMembers: isAdmin,
    isAdmin,
    isCompetentPerson,
    canRemoveMember,
  };
}
