import { supabase } from '@/lib/supabase';

export interface RemoveMemberParams {
  projectId: string;
  memberId: string;
}

export interface RemoveMemberResponse {
  success: boolean;
  message: string;
}

export async function removeMemberFromProject({
  projectId,
  memberId,
}: RemoveMemberParams): Promise<RemoveMemberResponse> {
  try {
    const response = await fetch(
      `/api/projects/${projectId}/members/${memberId}/remove`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    const data = await response.json();

    if (!response.ok) {
      // Create a more specific error message based on status code
      let errorMessage = data.error || 'Failed to remove member';

      switch (response.status) {
        case 400:
          errorMessage = data.error || 'Invalid request';
          break;
        case 401:
          errorMessage = 'You are not authorized to perform this action';
          break;
        case 403:
          errorMessage =
            'You do not have permission to remove members from this project';
          break;
        case 404:
          errorMessage = 'Member not found or already removed';
          break;
        case 500:
          errorMessage = 'Server error occurred while removing member';
          break;
        default:
          errorMessage = data.error || 'Failed to remove member';
      }

      throw new Error(errorMessage);
    }

    return data;
  } catch (error) {
    console.error('Error removing member:', error);

    // If it's a network error, provide a more user-friendly message
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Network error: Please check your internet connection');
    }

    throw error;
  }
}

export interface RestoreMemberParams {
  projectId: string;
  memberId: string;
}

export async function restoreMemberToProject({
  projectId,
  memberId,
}: RestoreMemberParams): Promise<RemoveMemberResponse> {
  try {
    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError) {
      console.error('Auth error:', userError);
      throw new Error('Authentication failed: Please log in again');
    }

    if (!user) {
      throw new Error('User not authenticated: Please log in again');
    }

    // Check permissions - only admins can restore members
    const { data: userPermission, error: permissionError } = await supabase
      .from('project_users')
      .select('role')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .eq('status', 'accepted')
      .eq('is_active', true)
      .single();

    if (permissionError) {
      console.error('Permission check error:', permissionError);
      throw new Error('Failed to verify permissions');
    }

    if (!userPermission || userPermission.role !== 'admin') {
      throw new Error('Unauthorized: Only admins can restore members');
    }

    // Restore the member
    const { error: restoreError } = await supabase
      .from('project_users')
      .update({
        is_active: true,
        deleted_at: null,
        deleted_by: null,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      })
      .eq('id', memberId)
      .eq('project_id', projectId)
      .eq('is_active', false);

    if (restoreError) {
      console.error('Restore member error:', restoreError);
      throw new Error('Database error: Failed to restore member');
    }

    return {
      success: true,
      message: 'Member restored successfully',
    };
  } catch (error) {
    console.error('Error restoring member:', error);

    // Re-throw with the same error if it's already a proper Error object
    if (error instanceof Error) {
      throw error;
    }

    // Handle unexpected error types
    throw new Error('An unexpected error occurred while restoring the member');
  }
}
