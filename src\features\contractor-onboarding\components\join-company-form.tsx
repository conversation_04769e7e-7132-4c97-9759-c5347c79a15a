'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export function JoinCompanyForm() {
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    console.log('Join company form submitted');
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 lg:py-12">
        <div className="w-full max-w-3xl mx-auto">
          <form
            className="bg-card rounded-3xl shadow-lg p-8 lg:p-12 border"
            onSubmit={handleSubmit}
          >
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-foreground mb-2">
                Join Existing Company
              </h1>
              <p className="text-muted-foreground">
                Search and request to join an existing company
              </p>
            </div>

            {/* Form Fields */}
            <div className="space-y-6">
              <div>
                <Label
                  htmlFor="search-method"
                  className="text-sm font-medium text-foreground"
                >
                  Search Method <span className="text-destructive">*</span>
                </Label>
                <Select name="search-method">
                  <SelectTrigger id="search-method" className="mt-2 w-full">
                    <SelectValue placeholder="How would you like to search?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Company Name</SelectItem>
                    <SelectItem value="registration">
                      Registration Number
                    </SelectItem>
                    <SelectItem value="code">Company Code</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label
                  htmlFor="search-value"
                  className="text-sm font-medium text-foreground"
                >
                  Search Value <span className="text-destructive">*</span>
                </Label>
                <Input
                  type="text"
                  id="search-value"
                  name="search-value"
                  placeholder="Enter company name, registration number, or company code"
                  className="mt-2"
                  required
                />
              </div>

              <div>
                <Label
                  htmlFor="your-position"
                  className="text-sm font-medium text-foreground"
                >
                  Your Position in the Company{' '}
                  <span className="text-destructive">*</span>
                </Label>
                <Input
                  type="text"
                  id="your-position"
                  name="your-position"
                  placeholder="e.g., Project Manager, Engineer, etc."
                  className="mt-2"
                  required
                />
              </div>

              <div>
                <Label
                  htmlFor="join-reason"
                  className="text-sm font-medium text-foreground"
                >
                  Reason for Joining
                </Label>
                <Input
                  type="text"
                  id="join-reason"
                  name="join-reason"
                  placeholder="Brief reason why you want to join this company"
                  className="mt-2"
                />
              </div>
            </div>

            {/* Search Results Section */}
            <div className="mt-8 p-6 bg-muted/50 rounded-lg">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                Search Results
              </h3>
              <p className="text-sm text-muted-foreground">
                Enter search criteria above to find companies
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row items-center justify-between pt-8 mt-8 border-t border-border gap-4">
              <p className="text-sm text-muted-foreground">
                All fields marked with{' '}
                <span className="text-destructive">*</span> are required
              </p>
              <div className="flex gap-3">
                <Button type="button" variant="outline" className="px-6 py-3">
                  Search Companies
                </Button>
                <Button type="submit" className="px-8 py-3" disabled>
                  Request to Join
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
