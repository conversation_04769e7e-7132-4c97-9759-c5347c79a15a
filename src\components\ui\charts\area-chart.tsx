'use client';

import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { ChevronDown } from 'lucide-react';
import { useCallback, useState } from 'react';
import {
  Area,
  AreaChart as Recharts<PERSON>reaChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const areaChartVariants = cva('', {
  variants: {
    variant: {
      default: '',
      outline: 'border rounded-lg p-4',
    },
    size: {
      default: 'h-[300px]',
      sm: 'h-[200px]',
      lg: 'h-[400px]',
      xl: 'h-[500px]',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

export interface AreaChartProps {
  data: Array<Record<string, unknown>>;
  series: Array<{
    name: string;
    dataKey: string;
    color?: string;
    fillOpacity?: number;
    stackId?: string;
    type?: 'monotone' | 'linear' | 'step' | 'stepBefore' | 'stepAfter';
    dot?: boolean | object;
    activeDot?: boolean | object;
  }>;
  xAxisDataKey: string;
  yAxisLabel?: string;
  title?: string;
  className?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  variant?: 'default' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'xl';
  tooltipFormatter?: (value: unknown, name: string) => React.ReactNode;
  tooltipLabelFormatter?: (label: string) => React.ReactNode;
  onPointClick?: (data: unknown, index: number) => void;
  emptyState?: React.ReactNode;
}

export function AreaChart({
  data,
  series,
  xAxisDataKey,
  yAxisLabel,
  title,
  className,
  showLegend = true,
  showGrid = true,
  showTooltip = true,
  variant = 'default',
  size = 'default',
  tooltipFormatter,
  tooltipLabelFormatter,
  onPointClick,
  emptyState,
}: AreaChartProps) {
  // Always use light mode colors
  const textColor = '#424242';
  const gridColor = '#e0e0e0';

  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const handlePointClick = useCallback(
    (data: unknown, index: number) => {
      setActiveIndex(index === activeIndex ? null : index);
      if (onPointClick) {
        onPointClick(data, index);
      }
    },
    [activeIndex, onPointClick],
  );

  // Check if data is empty or all values are zero
  const isDataEmpty =
    !data ||
    data.length === 0 ||
    series.every((s) =>
      data.every(
        (d) =>
          !d[s.dataKey as keyof typeof d] ||
          Number(d[s.dataKey as keyof typeof d]) === 0,
      ),
    );

  const renderContent = () => {
    if (isDataEmpty && emptyState) {
      return (
        <div className="flex items-center justify-center h-full w-full">
          {emptyState}
        </div>
      );
    }

    return (
      <ResponsiveContainer width="100%" height="100%">
        <RechartsAreaChart
          data={data}
          margin={{ top: 10, right: 30, left: 20, bottom: 40 }}
        >
          {showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              stroke={gridColor}
            />
          )}
          <XAxis
            dataKey={xAxisDataKey}
            stroke={textColor}
            tick={{ fill: textColor, fontSize: 12 }}
            tickLine={{ stroke: textColor }}
            axisLine={{ stroke: textColor }}
          />
          <YAxis
            stroke={textColor}
            tick={{ fill: textColor, fontSize: 12 }}
            tickLine={{ stroke: textColor }}
            axisLine={{ stroke: textColor }}
            label={
              yAxisLabel
                ? {
                    value: yAxisLabel,
                    angle: -90,
                    position: 'insideLeft',
                    style: { textAnchor: 'middle', fill: textColor },
                  }
                : undefined
            }
          />
          {showTooltip && (
            <Tooltip
              formatter={tooltipFormatter}
              labelFormatter={tooltipLabelFormatter}
              contentStyle={{
                backgroundColor: '#fff',
                border: '1px solid #e0e0e0',
                borderRadius: '6px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                padding: '8px 12px',
                color: textColor,
              }}
            />
          )}
          {showLegend && (
            <Legend
              verticalAlign="top"
              height={36}
              wrapperStyle={{ fontSize: '12px', color: textColor }}
              iconType="circle"
              iconSize={8}
            />
          )}
          {series.map((s, index) => (
            <Area
              key={`area-${index}`}
              type={s.type || 'monotone'}
              dataKey={s.dataKey}
              name={s.name}
              stroke={s.color || `hsl(${index * 40}, 70%, 60%)`}
              fill={s.color || `hsl(${index * 40}, 70%, 60%)`}
              fillOpacity={s.fillOpacity !== undefined ? s.fillOpacity : 0.3}
              stackId={s.stackId}
              dot={
                s.dot === false
                  ? false
                  : s.dot || {
                      r: 4,
                      strokeWidth: 1,
                      fill: 'white',
                      stroke: s.color || `hsl(${index * 40}, 70%, 60%)`,
                    }
              }
              activeDot={
                s.activeDot === false
                  ? false
                  : s.activeDot || {
                      r: 6,
                      onClick: handlePointClick,
                      style: { cursor: 'pointer' },
                    }
              }
              isAnimationActive={true}
              animationDuration={1000}
            />
          ))}
        </RechartsAreaChart>
      </ResponsiveContainer>
    );
  };

  if (variant === 'outline' || title) {
    return (
      <Card className={cn('overflow-hidden', className)}>
        {title && (
          <div className="flex justify-between items-center px-4 py-3 border-b">
            <div className="font-medium">{title}</div>
            <button className="rounded-full p-1 hover:bg-secondary">
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        )}
        <div className={areaChartVariants({ variant: 'default', size })}>
          {renderContent()}
        </div>
      </Card>
    );
  }

  return (
    <div className={cn(areaChartVariants({ variant, size }), className)}>
      {renderContent()}
    </div>
  );
}
