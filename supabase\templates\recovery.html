<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Password</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>,
          'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f9f9f9;
      }
      .email-container {
        background-color: white;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .logo {
        font-size: 28px;
        font-weight: bold;
        color: #6366f1;
        margin-bottom: 10px;
      }
      .title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 10px;
      }
      .subtitle {
        font-size: 16px;
        color: #6b7280;
        margin-bottom: 30px;
      }
      .code-container {
        background-color: #f3f4f6;
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        margin: 30px 0;
      }
      .code-label {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      .verification-code {
        font-size: 32px;
        font-weight: bold;
        font-family: 'Courier New', monospace;
        color: #1f2937;
        letter-spacing: 4px;
        margin: 10px 0;
      }
      .code-instructions {
        font-size: 14px;
        color: #6b7280;
        margin-top: 10px;
      }
      .instructions {
        font-size: 16px;
        color: #4b5563;
        margin: 20px 0;
        text-align: center;
      }
      .footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
        font-size: 14px;
        color: #6b7280;
        text-align: center;
      }
      .security-note {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 15px;
        margin: 20px 0;
        font-size: 14px;
        color: #92400e;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="logo">SimPLE</div>
        <h1 class="title">Reset Your Password</h1>
        <p class="subtitle">
          Use the verification code below to reset your password
        </p>
      </div>

      <div class="code-container">
        <div class="code-label">Verification Code</div>
        <div class="verification-code">{{ .Token }}</div>
        <div class="code-instructions">
          Enter this code in the verification form
        </div>
      </div>

      <p class="instructions">
        Go back to the SimPLE application and enter this 6-digit code to verify
        your identity and reset your password.
      </p>

      <div class="security-note">
        <strong>Security Notice:</strong> This code will expire in 1 hour. If
        you didn't request a password reset, please ignore this email or contact
        support if you have concerns.
      </div>

      <div class="footer">
        <p>This email was sent from SimPLE</p>
        <p>If you're having trouble, please contact our support team.</p>
      </div>
    </div>
  </body>
</html>
