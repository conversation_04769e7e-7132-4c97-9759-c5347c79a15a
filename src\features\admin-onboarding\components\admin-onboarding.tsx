'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { useTranslations } from 'next-intl';
import { useCompleteAdminOnboarding } from '../hooks/use-complete-admin-onboarding';
import { AdminOnboardingFormValues } from '../schemas/admin-onboarding-schemas';
import { AdminAccessModeForm } from './admin-access-mode-form';

interface AdminOnboardingProps {
  onComplete?: () => void;
}

/**
 * Admin onboarding component that guides JKR administrators through
 * setting up their access mode and monitoring preferences.
 */
export function AdminOnboarding({ onComplete }: AdminOnboardingProps) {
  const t = useTranslations('admin.onboarding');
  const completeOnboarding = useCompleteAdminOnboarding();

  const handleSubmit = async (values: AdminOnboardingFormValues) => {
    try {
      await completeOnboarding.mutateAsync(values);
      onComplete?.();
    } catch (error) {
      console.error('Admin onboarding submission error:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-foreground">
          {t('title', { fallback: 'Complete Your Admin Setup' })}
        </h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          {t('description', {
            fallback:
              'Configure your administrative access and monitoring preferences to optimize your JKR lift management experience.',
          })}
        </p>
      </div>

      {/* Onboarding Form */}
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-lg">
            {t('form.title', { fallback: 'Administrative Preferences' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AdminAccessModeForm
            onSubmit={handleSubmit}
            isLoading={completeOnboarding.isPending}
          />
        </CardContent>
      </Card>

      {/* Loading overlay during submission */}
      {completeOnboarding.isPending && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <Card className="w-auto">
            <CardContent className="flex items-center space-x-3 p-6">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="text-sm font-medium">
                {t('saving', { fallback: 'Saving your preferences...' })}
              </span>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
