-- ================================
-- PMA SUBSCRIPTION TRIAL PERIOD SYSTEM
-- Migration to add 15-day trial period for new contractors
-- ================================

-- ================================
-- 1. ADD TRIAL TRACKING COLUMN
-- ================================

-- Add trial_ends_at column to track trial period expiration
ALTER TABLE public.pma_subscriptions 
ADD COLUMN trial_ends_at TIMESTAMP WITH TIME ZONE NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.pma_subscriptions.trial_ends_at IS 'End date of 15-day trial period for new contractors. Set to contractor.created_at + 15 days when eligible for trial.';

-- ================================
-- 2. CREATE TRIAL ELIGIBILITY FUNCTION
-- ================================

-- Function to check if contractor is eligible for trial period
CREATE OR REPLACE FUNCTION public.is_contractor_trial_eligible(
    contractor_id UUID,
    check_date TIMESTAMPTZ DEFAULT NOW()
) RETURNS BOOLEAN AS $$
DECLARE
    contractor_created_at TIMESTAMPTZ;
    days_since_creation INTEGER;
BEGIN
    -- Get contractor creation date
    SELECT created_at 
    INTO contractor_created_at
    FROM public.contractors 
    WHERE id = contractor_id AND deleted_at IS NULL;
    
    -- Return false if contractor not found
    IF contractor_created_at IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Calculate days since contractor creation
    days_since_creation := EXTRACT(DAY FROM (check_date - contractor_created_at));
    
    -- Return true if contractor was created within 15 days
    RETURN days_since_creation <= 15;
END;
$$ LANGUAGE plpgsql STABLE;

-- Add comment for documentation
COMMENT ON FUNCTION public.is_contractor_trial_eligible IS 'Checks if contractor is eligible for 15-day trial period based on creation date. Returns true if contractor was created within 15 days of check_date.';

-- ================================
-- 3. CREATE TRIAL SUBSCRIPTION FUNCTION
-- ================================

-- Function to create PMA subscription with automatic trial detection
CREATE OR REPLACE FUNCTION public.create_pma_subscription_with_trial(
    p_pma_certificate_id UUID,
    p_contractor_id UUID,
    p_amount NUMERIC(10,2) DEFAULT 150.00
) RETURNS UUID AS $$
DECLARE
    subscription_id UUID;
    contractor_created_at TIMESTAMPTZ;
    is_trial_eligible BOOLEAN;
    trial_end_date TIMESTAMPTZ;
    subscription_status subscription_status;
BEGIN
    -- Check trial eligibility
    SELECT public.is_contractor_trial_eligible(p_contractor_id) INTO is_trial_eligible;
    
    -- Get contractor creation date for trial end calculation
    SELECT created_at INTO contractor_created_at 
    FROM public.contractors 
    WHERE id = p_contractor_id AND deleted_at IS NULL;
    
    -- Set subscription parameters based on trial eligibility
    IF is_trial_eligible THEN
        subscription_status := 'trial';
        trial_end_date := contractor_created_at + INTERVAL '15 days';
    ELSE
        subscription_status := 'pending_payment';
        trial_end_date := NULL;
    END IF;
    
    -- Create the subscription
    INSERT INTO public.pma_subscriptions (
        pma_certificate_id,
        contractor_id,
        amount,
        calculated_amount,
        status,
        trial_ends_at,
        created_at,
        updated_at
    ) VALUES (
        p_pma_certificate_id,
        p_contractor_id,
        p_amount,
        p_amount,
        subscription_status,
        trial_end_date,
        NOW(),
        NOW()
    ) RETURNING id INTO subscription_id;
    
    RETURN subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Add comment for documentation
COMMENT ON FUNCTION public.create_pma_subscription_with_trial IS 'Creates PMA subscription with automatic trial period detection. Sets trial status and end date for contractors created within 15 days.';

-- ================================
-- 4. UPDATE DAILY CRON JOB FUNCTION
-- ================================

-- Drop and recreate function to handle trial expiration with new return type
DROP FUNCTION IF EXISTS public.update_prorated_pma_amounts();

CREATE OR REPLACE FUNCTION public.update_prorated_pma_amounts() 
RETURNS TABLE(
    updated_count INTEGER,
    trial_expired_count INTEGER,
    execution_time TIMESTAMPTZ,
    details TEXT
) AS $$
DECLARE
    prorated_update_count INTEGER := 0;
    trial_expiry_count INTEGER := 0;
    start_time TIMESTAMPTZ := CURRENT_TIMESTAMP;
BEGIN
    -- 1. Handle trial period expiration (convert trial to pending_payment)
    UPDATE public.pma_subscriptions 
    SET 
        status = 'pending_payment',
        updated_at = CURRENT_TIMESTAMP
    WHERE status = 'trial' 
    AND trial_ends_at < CURRENT_TIMESTAMP;
    
    -- Get count of expired trials
    GET DIAGNOSTICS trial_expiry_count = ROW_COUNT;
    
    -- 2. Update calculated_amount for all pending_payment subscriptions (existing logic)
    UPDATE public.pma_subscriptions 
    SET 
        calculated_amount = public.calculate_prorated_pma_amount(created_at, CURRENT_DATE),
        updated_at = CURRENT_TIMESTAMP
    WHERE status = 'pending_payment';
    
    -- Get count of prorated updates
    GET DIAGNOSTICS prorated_update_count = ROW_COUNT;
    
    -- Return execution summary
    RETURN QUERY SELECT 
        prorated_update_count,
        trial_expiry_count,
        start_time,
        format('Updated %s pending payments with pro-rated amounts. Expired %s trial subscriptions.', 
               prorated_update_count, trial_expiry_count);
END;
$$ LANGUAGE plpgsql;

-- Update function comment
COMMENT ON FUNCTION public.update_prorated_pma_amounts IS 'Daily function to handle trial expiration and update calculated_amount for pending_payment PMA subscriptions. Called by cron job at 12:00 AM.';

-- ================================
-- 5. ADD PERFORMANCE INDEXES
-- ================================

-- Index for trial period queries
CREATE INDEX idx_pma_subscriptions_trial_status 
    ON public.pma_subscriptions(status, trial_ends_at) 
    WHERE status = 'trial';

-- Index for trial eligibility queries (contractor creation date)
CREATE INDEX idx_contractors_created_at_active 
    ON public.contractors(created_at) 
    WHERE deleted_at IS NULL;

-- ================================
-- 6. ADD CONSTRAINT FOR TRIAL LOGIC
-- ================================

-- Ensure trial_ends_at is set when status is trial
ALTER TABLE public.pma_subscriptions 
ADD CONSTRAINT chk_pma_trial_logic CHECK (
    (status = 'trial' AND trial_ends_at IS NOT NULL) OR
    (status != 'trial' AND trial_ends_at IS NULL)
);

-- ================================
-- 7. UPDATE EXISTING SUBSCRIPTIONS (IF APPLICABLE)
-- ================================

-- Check for any contractors created within the last 15 days who have pending subscriptions
-- and convert them to trial status if eligible
DO $$
DECLARE
    update_count INTEGER := 0;
BEGIN
    UPDATE public.pma_subscriptions 
    SET 
        status = 'trial',
        trial_ends_at = c.created_at + INTERVAL '15 days',
        updated_at = NOW()
    FROM public.contractors c
    WHERE pma_subscriptions.contractor_id = c.id
    AND pma_subscriptions.status = 'pending_payment'
    AND c.created_at > NOW() - INTERVAL '15 days'
    AND c.deleted_at IS NULL;
    
    GET DIAGNOSTICS update_count = ROW_COUNT;
    
    RAISE NOTICE 'Updated % existing subscriptions to trial status for recent contractors', update_count;
END $$;