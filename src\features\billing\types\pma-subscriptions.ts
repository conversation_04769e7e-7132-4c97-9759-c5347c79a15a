import type { Database } from '@/types/database';

type PmaSubscription = Database['public']['Tables']['pma_subscriptions']['Row'];

export interface PmaSubscriptionWithDetails extends PmaSubscription {
  pma_certificates: {
    id: string;
    project_id: string | null;
    pma_number: string | null;
    status: string;
    expiry_date: string;
    location: string | null;
    projects: {
      id: string;
      name: string;
      location: string | null;
    } | null;
  } | null;
  contractors: {
    id: string;
    name: string;
  } | null;
}
