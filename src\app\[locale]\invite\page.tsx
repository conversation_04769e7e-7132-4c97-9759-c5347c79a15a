import { InvitationPage } from '@/features/project-invitations/components/invitation-page';
import { notFound } from 'next/navigation';

interface InvitePageProps {
  searchParams: Promise<{
    token?: string;
  }>;
}

/**
 * Project invitation acceptance page
 * Route: /[locale]/invite?token=...
 */
export default async function InvitePage({ searchParams }: InvitePageProps) {
  const { token } = await searchParams;

  if (!token) {
    notFound();
  }

  return <InvitationPage token={token} />;
}

export const metadata = {
  title: 'Project Invitation - SimPLE',
  description: 'Accept your project invitation to join SimPLE',
};
