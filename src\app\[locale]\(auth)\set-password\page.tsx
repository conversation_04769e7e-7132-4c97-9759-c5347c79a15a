import { SetPasswordPage } from '@/features/auth/components/set-password-page';

interface SetPasswordRouteProps {
  searchParams: Promise<{
    token?: string;
    project?: string;
  }>;
}

/**
 * Password setup page for new users from invitations
 * Route: /[locale]/auth/set-password?token=...&project=...
 */
export default async function SetPasswordRoute({
  searchParams,
}: SetPasswordRouteProps) {
  const { token, project } = await searchParams;

  return <SetPasswordPage token={token} projectToken={project} />;
}

export const metadata = {
  title: 'Set Password - SimPLE',
  description: 'Set your password to complete account setup',
};
