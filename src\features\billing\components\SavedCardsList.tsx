'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { CreditCard, Trash2, Star, AlertTriangle } from 'lucide-react';
import type { SavedCardInfo } from '../types';

export interface SavedCardsListProps {
  savedCards: SavedCardInfo[];
  selectedCardId?: string;
  onCardSelect: (cardId: string) => void;
  onDeleteCard?: (cardId: string) => void;
  onSetDefaultCard?: (cardId: string) => void;
  isLoading?: boolean;
  isDeleting?: boolean;
  isSettingDefault?: boolean;
  error?: Error | null;
  className?: string;
}

function getCardBrandColor(brand: string): string {
  switch (brand.toLowerCase()) {
    case 'visa':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'mastercard':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'amex':
    case 'american express':
      return 'bg-green-100 text-green-800 border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

function formatExpiryDate(month: number, year: number): string {
  return `${month.toString().padStart(2, '0')}/${year.toString().slice(-2)}`;
}

function isCardExpired(month: number, year: number): boolean {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;

  if (year < currentYear) return true;
  if (year === currentYear && month < currentMonth) return true;
  return false;
}

export function SavedCardsList({
  savedCards,
  selectedCardId,
  onCardSelect,
  onDeleteCard,
  onSetDefaultCard,
  isLoading = false,
  isDeleting = false,
  isSettingDefault = false,
  error,
  className,
}: SavedCardsListProps) {
  if (error) {
    return (
      <Alert variant="destructive" className={cn('', className)}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error.message || 'Failed to load saved cards'}
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className={cn('space-y-3', className)}>
        {[1, 2].map((i) => (
          <div
            key={i}
            className="animate-pulse p-4 border rounded-lg bg-muted/20"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-muted rounded-full" />
                <div className="space-y-2">
                  <div className="w-32 h-4 bg-muted rounded" />
                  <div className="w-24 h-3 bg-muted rounded" />
                </div>
              </div>
              <div className="w-16 h-6 bg-muted rounded" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (savedCards.length === 0) {
    return (
      <div className={cn('text-center py-8', className)}>
        <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">No saved cards found</p>
        <p className="text-sm text-muted-foreground">
          Add a new card to save it for future payments
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)}>
      <RadioGroup
        value={selectedCardId}
        onValueChange={onCardSelect}
        className="space-y-3"
      >
        {savedCards.map((card) => {
          const isExpired = isCardExpired(card.exp_month, card.exp_year);
          const isSelected = selectedCardId === card.id;

          return (
            <div
              key={card.id}
              className={cn(
                'relative p-4 border rounded-lg transition-colors',
                isSelected
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:border-primary/50',
                isExpired && 'opacity-60',
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <RadioGroupItem
                    value={card.id}
                    id={card.id}
                    disabled={isExpired}
                    className="mt-0.5"
                  />
                  <Label
                    htmlFor={card.id}
                    className="flex items-center gap-3 cursor-pointer flex-1"
                  >
                    <CreditCard className="h-5 w-5 text-muted-foreground" />
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          •••• •••• •••• {card.last_four}
                        </span>
                        <Badge
                          className={cn(
                            'text-xs',
                            getCardBrandColor(card.brand),
                          )}
                        >
                          {card.brand.toUpperCase()}
                        </Badge>
                        {card.is_default && (
                          <Badge variant="secondary" className="text-xs">
                            <Star className="h-3 w-3 mr-1 fill-current" />
                            Default
                          </Badge>
                        )}
                        {isExpired && (
                          <Badge variant="destructive" className="text-xs">
                            Expired
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Expires{' '}
                        {formatExpiryDate(card.exp_month, card.exp_year)}
                      </p>
                    </div>
                  </Label>
                </div>

                <div className="flex items-center gap-1">
                  {!card.is_default && onSetDefaultCard && !isExpired && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onSetDefaultCard(card.id)}
                      disabled={isSettingDefault}
                      className="text-xs px-2 py-1 h-auto"
                    >
                      Set Default
                    </Button>
                  )}
                  {onDeleteCard && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteCard(card.id)}
                      disabled={isDeleting}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10 p-2 h-auto"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete card</span>
                    </Button>
                  )}
                </div>
              </div>

              {isExpired && (
                <div className="mt-2 text-sm text-destructive">
                  This card has expired and cannot be used for payments
                </div>
              )}
            </div>
          );
        })}
      </RadioGroup>

      {savedCards.some((card) =>
        isCardExpired(card.exp_month, card.exp_year),
      ) && (
        <Alert className="mt-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Some of your saved cards have expired. Please update or remove them.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
