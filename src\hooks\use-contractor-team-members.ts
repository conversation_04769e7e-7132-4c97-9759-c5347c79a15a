import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';

export interface TeamMemberData {
  id: string;
  name: string | null;
  email: string;
  phone_number: string | null;
  user_role: string;
  created_at: string | null;
  onboarding_completed: boolean;
}

/**
 * Hook to get all team members for a contractor company
 * Fetches all users that belong to the same contractor_id
 */
export function useContractorTeamMembers(contractorId?: string) {
  return useQuery({
    queryKey: ['contractor-team-members', contractorId],
    queryFn: async (): Promise<TeamMemberData[]> => {
      if (!contractorId) throw new Error('Contractor ID is required');

      const { data, error } = await supabase
        .from('users')
        .select(
          'id, name, email, phone_number, user_role, created_at, onboarding_completed',
        )
        .eq('contractor_id', contractorId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching team members:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
