import type { AdminAccessMode, StateCode } from '@/types/access-control';
import { z } from 'zod';

/**
 * Schema for admin onboarding form
 * Collects admin access mode and monitoring state if applicable
 */
export const adminOnboardingSchema = z
  .object({
    // Personal information (optional updates)
    fullName: z.string().min(1, 'Full name is required').max(100),
    phoneNumber: z
      .string()
      .optional()
      .refine(
        (val) => !val || /^[\+]?[0-9\s\-\(\)]{7,15}$/.test(val),
        'Please enter a valid phone number',
      ),

    // Admin-specific configuration
    adminAccessMode: z.enum(['state', 'project'], {
      required_error: 'Please select an access mode',
    }),

    // Required only if access mode is 'state'
    monitoringState: z
      .enum([
        'JH',
        'KD',
        'KT',
        'ML',
        'NS',
        'PH',
        'PN',
        'PK',
        'PL',
        'SB',
        'SW',
        'SL',
        'TR',
        'WP',
        'LBN',
        'PW',
        'OTH',
      ])
      .optional(),
  })
  .refine(
    (data) => {
      // If access mode is 'state', monitoring state is required
      if (data.adminAccessMode === 'state') {
        return !!data.monitoringState;
      }
      return true;
    },
    {
      message:
        'Monitoring state is required when access mode is set to state-level',
      path: ['monitoringState'],
    },
  );

export type AdminOnboardingFormValues = z.infer<typeof adminOnboardingSchema>;

/**
 * State code options for dropdown
 */
export const STATE_OPTIONS: { value: StateCode; label: string }[] = [
  { value: 'JH', label: 'Johor' },
  { value: 'KD', label: 'Kedah' },
  { value: 'KT', label: 'Kelantan' },
  { value: 'ML', label: 'Melaka' },
  { value: 'NS', label: 'Negeri Sembilan' },
  { value: 'PH', label: 'Pahang' },
  { value: 'PN', label: 'Penang' },
  { value: 'PK', label: 'Perak' },
  { value: 'PL', label: 'Perlis' },
  { value: 'SB', label: 'Sabah' },
  { value: 'SW', label: 'Sarawak' },
  { value: 'SL', label: 'Selangor' },
  { value: 'TR', label: 'Terengganu' },
  { value: 'WP', label: 'Kuala Lumpur' },
  { value: 'LBN', label: 'Labuan' },
  { value: 'PW', label: 'Putrajaya' },
  { value: 'OTH', label: 'Other' },
];

/**
 * Admin access mode options
 */
export const ADMIN_ACCESS_MODE_OPTIONS: {
  value: AdminAccessMode;
  label: string;
  description: string;
}[] = [
  // {
  //   value: 'project',
  //   label: 'Project-Level Access',
  //   description: 'Full access to all projects across all states and regions',
  // },
  {
    value: 'state',
    label: 'State-Level Access',
    description: 'Access limited to projects within a specific state or region',
  },
];
