import { supabase } from './supabase';
import type { Database } from '@/types/database';

export interface SubscriptionCheckResult {
  hasAccess: boolean;
  status: Database['public']['Enums']['subscription_status'] | null;
  message: string;
  gracePeriodEnds?: string | null;
}

/**
 * Check if a PMA certificate has an active subscription that allows access
 */
export async function checkPmaSubscriptionAccess(
  pmaId: string,
): Promise<SubscriptionCheckResult> {
  if (!pmaId) {
    return {
      hasAccess: false,
      status: null,
      message: 'PMA certificate is required',
    };
  }

  try {
    const { data: subscription, error } = await supabase
      .from('pma_subscriptions')
      .select('status, access_allowed, grace_period_ends')
      .eq('pma_certificate_id', pmaId)
      .single();

    if (error) {
      console.error('Error checking subscription access:', error);
      return {
        hasAccess: false,
        status: null,
        message: 'Unable to verify subscription status',
      };
    }

    if (!subscription) {
      return {
        hasAccess: false,
        status: null,
        message: 'No subscription found for this PMA certificate',
      };
    }

    const hasAccess = subscription.access_allowed || false;
    const status = subscription.status;

    let message = '';
    switch (status) {
      case 'active':
        message = hasAccess
          ? 'Subscription is active'
          : 'Subscription access denied';
        break;
      case 'grace_period':
        message = hasAccess
          ? `Subscription in grace period until ${subscription.grace_period_ends}`
          : 'Subscription grace period expired';
        break;
      case 'pending_payment':
        message = 'Subscription payment is pending';
        break;
      case 'suspended':
        message = 'Subscription has been suspended';
        break;
      case 'cancelled':
        message = 'Subscription has been cancelled';
        break;
      default:
        message = 'Subscription status unknown';
    }

    return {
      hasAccess,
      status,
      message,
      gracePeriodEnds: subscription.grace_period_ends,
    };
  } catch (error) {
    console.error('Unexpected error checking subscription access:', error);
    return {
      hasAccess: false,
      status: null,
      message: 'Unable to verify subscription status',
    };
  }
}

/**
 * Get subscription status for multiple PMA certificates
 */
export async function checkMultiplePmaSubscriptionAccess(
  pmaIds: string[],
): Promise<Record<string, SubscriptionCheckResult>> {
  if (!pmaIds.length) {
    return {};
  }

  try {
    const { data: subscriptions, error } = await supabase
      .from('pma_subscriptions')
      .select('pma_certificate_id, status, access_allowed, grace_period_ends')
      .in('pma_certificate_id', pmaIds);

    if (error) {
      console.error('Error checking multiple subscription access:', error);
      // Return error state for all PMAs
      return pmaIds.reduce(
        (acc, pmaId) => {
          acc[pmaId] = {
            hasAccess: false,
            status: null,
            message: 'Unable to verify subscription status',
          };
          return acc;
        },
        {} as Record<string, SubscriptionCheckResult>,
      );
    }

    const results: Record<string, SubscriptionCheckResult> = {};

    // Process each PMA ID
    pmaIds.forEach((pmaId) => {
      const subscription = subscriptions?.find(
        (sub) => sub.pma_certificate_id === pmaId,
      );

      if (!subscription) {
        results[pmaId] = {
          hasAccess: false,
          status: null,
          message: 'No subscription found for this PMA certificate',
        };
        return;
      }

      const hasAccess = subscription.access_allowed || false;
      const status = subscription.status;

      let message = '';
      switch (status) {
        case 'active':
          message = hasAccess
            ? 'Subscription is active'
            : 'Subscription access denied';
          break;
        case 'grace_period':
          message = hasAccess
            ? `Subscription in grace period until ${subscription.grace_period_ends}`
            : 'Subscription grace period expired';
          break;
        case 'pending_payment':
          message = 'Subscription payment is pending';
          break;
        case 'suspended':
          message = 'Subscription has been suspended';
          break;
        case 'cancelled':
          message = 'Subscription has been cancelled';
          break;
        default:
          message = 'Subscription status unknown';
      }

      results[pmaId] = {
        hasAccess,
        status,
        message,
        gracePeriodEnds: subscription.grace_period_ends,
      };
    });

    return results;
  } catch (error) {
    console.error(
      'Unexpected error checking multiple subscription access:',
      error,
    );
    // Return error state for all PMAs
    return pmaIds.reduce(
      (acc, pmaId) => {
        acc[pmaId] = {
          hasAccess: false,
          status: null,
          message: 'Unable to verify subscription status',
        };
        return acc;
      },
      {} as Record<string, SubscriptionCheckResult>,
    );
  }
}

/**
 * Subscription validation error class
 */
export class SubscriptionError extends Error {
  public status: Database['public']['Enums']['subscription_status'] | null;
  public gracePeriodEnds?: string | null;

  constructor(
    message: string,
    status: Database['public']['Enums']['subscription_status'] | null = null,
    gracePeriodEnds?: string | null,
  ) {
    super(message);
    this.name = 'SubscriptionError';
    this.status = status;
    this.gracePeriodEnds = gracePeriodEnds;
  }
}
