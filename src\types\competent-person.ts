/**
 * Competent Person types and interfaces
 * Based on the competent_person table schema
 */

export type CPType = 'CP1' | 'CP2' | 'CP3';

export interface CompetentPerson {
  id: string;
  contractor_id: string;
  name: string;
  ic_no: string;
  phone_no?: string;
  address?: string;
  cp_type: CPType;
  cp_registeration_no?: string;
  cp_registeration_cert?: string;
  cert_exp_date?: string;
  no_of_pma: number;
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
  created_by?: string;
  updated_by?: string;
  deleted_by?: string;
}

export interface CompetentPersonInsert {
  contractor_id: string;
  name: string;
  ic_no: string;
  phone_no?: string;
  address?: string;
  cp_type: CPType;
  cp_registeration_no?: string;
  cp_registeration_cert?: string;
  cert_exp_date?: string;
  no_of_pma?: number;
  created_by?: string;
}

export interface CompetentPersonUpdate {
  name?: string;
  ic_no?: string;
  phone_no?: string;
  address?: string;
  cp_type?: CPType;
  cp_registeration_no?: string;
  cp_registeration_cert?: string;
  cert_exp_date?: string;
  no_of_pma?: number;
  updated_by?: string;
}
